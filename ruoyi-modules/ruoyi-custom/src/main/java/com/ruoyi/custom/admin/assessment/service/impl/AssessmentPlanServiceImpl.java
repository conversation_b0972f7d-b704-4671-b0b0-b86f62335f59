package com.ruoyi.custom.admin.assessment.service.impl;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.custom.admin.assessment.domain.AssessmentPlan;
import com.ruoyi.custom.admin.assessment.domain.AssessmentResult;
import com.ruoyi.custom.admin.assessment.domain.AssessmentTemplate;
import com.ruoyi.custom.admin.assessment.domain.ElderlyCapacityAssessment;
import com.ruoyi.custom.admin.assessment.mapper.AssessmentPlanMapper;
import com.ruoyi.custom.admin.assessment.mapper.ElderlyCapacityAssessmentMapper;
import com.ruoyi.custom.admin.assessment.service.IAssessmentPlanService;
import com.ruoyi.custom.admin.assessment.service.IAssessmentTemplateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 评估计划Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-03-03
 */
@Service
public class AssessmentPlanServiceImpl implements IAssessmentPlanService {
    @Autowired
    private AssessmentPlanMapper assessmentPlanMapper;

    @Autowired
    private IAssessmentTemplateService assessmentTemplateService;

    @Autowired
    private ElderlyCapacityAssessmentMapper elderlyCapacityAssessmentMapper;

    /**
     * 查询评估计划
     *
     * @param id 评估计划主键
     * @return 评估计划
     */
    @Override
    public AssessmentPlan selectAssessmentPlanById(Long id) {
        return assessmentPlanMapper.selectAssessmentPlanById(id);
    }

    /**
     * 查询评估计划列表
     *
     * @param assessmentPlan 评估计划
     * @return 评估计划
     */
    @Override
    public List<AssessmentPlan> selectAssessmentPlanList(AssessmentPlan assessmentPlan) {
        return assessmentPlanMapper.selectAssessmentPlanList(assessmentPlan);
    }

    /**
     * 新增评估计划
     *
     * @param assessmentPlan 评估计划
     * @return 结果
     */
    @Override
    public int insertAssessmentPlan(AssessmentPlan assessmentPlan) {
        // 检查用户输入的评估编号是否已存在
        AssessmentPlan params = new AssessmentPlan();
        params.setSerialNumber(assessmentPlan.getSerialNumber());
        List<AssessmentPlan> assessmentPlans = assessmentPlanMapper.selectAssessmentPlanList(params);
        if (CollectionUtil.isNotEmpty(assessmentPlans)) {
            throw new ServiceException("评估编号已存在");
        }

        // 设置初始状态
        assessmentPlan.setStatus("0");

        // 复制模版的题库
        if (assessmentPlan.getTemplateId() != 999) {
            AssessmentTemplate assessmentTemplate = assessmentTemplateService.selectAssessmentTemplateById(assessmentPlan.getTemplateId());
            assessmentPlan.setIndicator(assessmentTemplate.getIndicator());
            assessmentPlan.setGrade(assessmentTemplate.getGrade());
        }

        return assessmentPlanMapper.insertAssessmentPlan(assessmentPlan);
    }

    /**
     * 修改评估计划
     *
     * @param assessmentPlan 评估计划
     * @return 结果
     */
    @Override
    public int updateAssessmentPlan(AssessmentPlan assessmentPlan) {
        // 检查用户输入的评估编号是否已存在
        AssessmentPlan params = new AssessmentPlan();
        params.setSerialNumber(assessmentPlan.getSerialNumber());
        List<AssessmentPlan> assessmentPlans = assessmentPlanMapper.selectAssessmentPlanList(params);
        for (AssessmentPlan assessmentPlan1 : assessmentPlans) {
            if (!assessmentPlan1.getId().equals(assessmentPlan.getId())) {
                throw new ServiceException("评估编号已存在");
            }
        }

        return assessmentPlanMapper.updateAssessmentPlan(assessmentPlan);
    }

    /**
     * 批量删除评估计划
     *
     * @param ids 需要删除的评估计划主键
     * @return 结果
     */
    @Override
    public int deleteAssessmentPlanByIds(Long[] ids) {
        // 删除新评估表elderlyCapacityAssessment
        AssessmentPlan assessmentPlan = assessmentPlanMapper.selectAssessmentPlanById(ids[0]);
        ElderlyCapacityAssessment assessment = elderlyCapacityAssessmentMapper.selectElderlyCapacityAssessmentBySerialNumber(assessmentPlan.getSerialNumber());
        if (!Objects.isNull(assessment)) {
            elderlyCapacityAssessmentMapper.deleteElderlyCapacityAssessmentById(assessment.getId());
        }
        return assessmentPlanMapper.deleteAssessmentPlanByIds(ids);
    }

    /**
     * 删除评估计划信息
     *
     * @param id 评估计划主键
     * @return 结果
     */
    @Override
    public int deleteAssessmentPlanById(Long id) {
        return assessmentPlanMapper.deleteAssessmentPlanById(id);
    }

    /**
     * 开始评估&继续评估&查看评估
     *
     * @param id
     * @return
     */
    @Override
    public JSONArray startOrViewAnswer(Long id) {
        AssessmentPlan assessmentPlan = assessmentPlanMapper.selectAssessmentPlanById(id);
        if (Objects.isNull(assessmentPlan)) {
            throw new ServiceException("评估计划不存在");
        }

        // 如果评估人为空，则设置评估人为当前用户
        if (Objects.isNull(assessmentPlan.getAssessorId())) {
            assessmentPlan.setAssessorId(SecurityUtils.getUserId());
            assessmentPlan.setAssessorName(SecurityUtils.getUsername());
        }

        // 如果状态为0，则开始评估
        if (assessmentPlan.getStatus().equals("0")) {
            assessmentPlan.setStatus("1");
            assessmentPlan.setActualStartTime(DateTime.now());
            assessmentPlanMapper.updateAssessmentPlan(assessmentPlan);
        }

        return assessmentPlan.getIndicator();
    }

    @Override
    public JSONObject createAssessmentResultJson(AssessmentPlan params) {
        // 获取评估计划及老人基础数据
        AssessmentPlan assessmentPlan = assessmentPlanMapper.selectAssessmentPlanById(params.getId());
        assessmentPlan.setIndicator(params.getIndicator());
        // 计算指标等级、评估分数、评估结果
        JSONObject indicatorR = computeIndicator(assessmentPlan);

        JSONObject assessmentResult = new JSONObject();
        assessmentResult.put("assessmentTime", DateUtil.format(DateUtil.date(), "yyyy/MM/dd"));
        assessmentResult.put("elderlyName", assessmentPlan.getElderlyName());
        assessmentResult.put("gender", assessmentPlan.getElderlySex());
        assessmentResult.put("idCard", assessmentPlan.getElderlyIdCardNum());
        assessmentResult.put("birthDate", DateUtil.format(assessmentPlan.getElderlyDateBirth(), "yyyy/MM/dd"));
        assessmentResult.put("assessmentTemplate", assessmentPlan.getTemplateName());
        assessmentResult.put("indicatorLevel", indicatorR.getJSONArray("indicatorLevel"));
        assessmentResult.put("assessmentScore", indicatorR.getInteger("scoreTotal"));
        assessmentResult.put("assessmentResult", indicatorR.getString("grade"));
        assessmentResult.put("assessmentReason", assessmentPlan.getAssessmentReason());
        assessmentResult.put("assessor", assessmentPlan.getAssessorName());
        assessmentResult.put("suggestion", indicatorR.getString("suggestion"));
        assessmentResult.put("attachmentUrl", assessmentPlan.getAttachmentUrl());

        return assessmentResult;

    }

    /**
     * 计算指标等级
     *
     * @param assessmentPlan
     * @return
     */

    private JSONObject computeIndicator(AssessmentPlan assessmentPlan) {
        // [{"id": "ac42665b-ffe3-11ef-9298-000c29e2a59b", "sort": 8588, "grade": {"full": ["1", "2"], "mild": ["1", "2"], "severe": ["1", "2"], "moderate": ["1", "2"]}, "subject": [{"id": "c68bf257-e8de-4cd5-badc-a3170054ddbc", "sort": 1, "title": "从坐位到站位,再从站位到坐位的转换过程", "detail": [{"id": "27ebf11b-0133-4e1b-9896-f73a1f8c1c7f", "score": 7, "content": "需要他人协助,但以自身完成为主"}, {"id": "a9819ce4-c504-43ea-9c19-7aa790ddbe24", "score": 6, "content": "白天大部分时间可自行控制排尿,偶出现(每天<1次,但每周>1次)尿失禁"}]}, {"id": "a55fb825-2a38-454c-b51c-14338c3df7e0", "sort": 666, "title": "太复杂了", "detail": [{"id": "dd8b932d-2860-4ac9-b5a5-c06c062a0cea", "score": 7, "content": "需要他人协助,但以自身完成为主"}, {"id": "123221b7-d822-4f06-ba21-0464b929226b", "score": 6, "content": "白天大部分时间可自行控制排尿,偶出现(每天<1次,但每周>1次)尿失禁"}]}, {"id": "025d2fb5-007e-11f0-9298-000c29e2a59b", "sort": 55, "title": "饭实在是太香", "detail": []}], "indicator": "wish today"}, {"id": "c2ced2e4-ffe3-11ef-9298-000c29e2a59b", "sort": 56, "grade": {"full": [21, 30], "mild": [21, 30], "severe": [21, 30], "moderate": [21, 30]}, "subject": [], "indicator": "健康指标"}, {"id": "cd3711bf-ffe5-11ef-9298-000c29e2a59b", "sort": 56, "grade": {"full": [21, 30], "mild": [21, 30], "severe": [21, 30], "moderate": [21, 30]}, "subject": [], "indicator": "健康指标"}, {"id": "e9bf34a7-ffec-11ef-9298-000c29e2a59b", "sort": 55, "grade": {"full": [21, 30], "mild": [21, 30], "severe": [21, 30], "moderate": [21, 30]}, "subject": [], "indicator": "666"}]
        JSONArray answer = assessmentPlan.getIndicator();
        JSONObject finalR = new JSONObject();
        JSONArray indicatorLevel = new JSONArray(); // 指标等级计算结果
        AtomicInteger scoreTotal = new AtomicInteger(); // 总分
        String gradeStr = ""; // 最终老年人能力评估等级
        // 计算各个指标下总分
        answer.forEach(item -> {
            JSONObject indicator = JSONObject.parseObject(JSON.toJSONString(item)); // 指标
            String indicatorName = indicator.getString("indicator");
            AtomicInteger totalScore = new AtomicInteger();
            JSONObject grade = indicator.getJSONObject("grade"); // 指标 ->> 等级
            JSONArray subject = indicator.getJSONArray("subject");
            subject.forEach(item1 -> {
                JSONObject subjectItem = (JSONObject) item1; // 指标 ->> 题目
                // 题以及选择情况
                subjectItem.getJSONArray("detail").forEach(item2 -> {
                    JSONObject detailItem = (JSONObject) item2; // 指标 ->> 题目 ->> 题目选项
                    Boolean selected = detailItem.getBoolean("selected");
                    if (selected != null && selected) {
                        // 获取分数并累加
                        int score = detailItem.getInteger("score");
                        totalScore.addAndGet(score);
                    }
                });
            });

            // 确定等级
            String level = "unknown";
            if (isScoreInRange(totalScore.get(), grade.getJSONArray("full"))) {
                level = "full";
            } else if (isScoreInRange(totalScore.get(), grade.getJSONArray("mild"))) {
                level = "mild";
            } else if (isScoreInRange(totalScore.get(), grade.getJSONArray("moderate"))) {
                level = "moderate";
            } else if (isScoreInRange(totalScore.get(), grade.getJSONArray("severe"))) {
                level = "severe";
            }

            // 添加等级
            JSONObject indicatorLevelItem = new JSONObject();
            indicatorLevelItem.put("indicator", indicatorName);
            indicatorLevelItem.put("grade", level);
            indicatorLevel.add(indicatorLevelItem);

            // 累加总分
            scoreTotal.addAndGet(totalScore.get());
        });

        // 根据总分计算评估结果
        // {"full": [1, 2], "mild": [3, 4], "severe": [7, 8], "moderate": [5, 6]}
        JSONObject gradeR = assessmentPlan.getGrade();
        if (isScoreInRange(scoreTotal.get(), gradeR.getJSONArray("full"))) {
            gradeStr = "full";
        } else if (isScoreInRange(scoreTotal.get(), gradeR.getJSONArray("mild"))) {
            gradeStr = "mild";
        } else if (isScoreInRange(scoreTotal.get(), gradeR.getJSONArray("moderate"))) {
            gradeStr = "moderate";
        } else if (isScoreInRange(scoreTotal.get(), gradeR.getJSONArray("severe"))) {
            gradeStr = "severe";
        }

        // 拼装最终结果
        finalR.put("grade", gradeStr);
        finalR.put("indicatorLevel", indicatorLevel);
        finalR.put("scoreTotal", scoreTotal.get());
        return finalR;
    }

    private boolean isScoreInRange(int score, JSONArray range) {
        if (range.size() == 2) {
            int min = range.getInteger(0);
            int max = range.getInteger(1);
            return score >= min && score <= max;
        }
        return false;
    }

    @Override
    public List<String> getCalendarList(Date month) {
        return assessmentPlanMapper.getCalendarList(month);
    }

    @Override
    public int saveAssessmentResult(AssessmentResult assessmentResult) {
        return assessmentPlanMapper.saveAssessmentResult(assessmentResult);
    }

    @Override
    public int selectAssessmentPlanCount(AssessmentPlan params) {
        return assessmentPlanMapper.selectAssessmentPlanCount(params);
    }

    @Override
    public List<String> remindNotice(AssessmentPlan params) {
        String template1 = "将于{}，开始对{}进行{}评估，请做好准备！";
        String template2 = "您于{}，对{}进行{}评估，已经超时，请尽快评估！";
        Date tomorrow = DateUtil.tomorrow();

        // 查询计划日期是明天且状态未开始的数据
        params.setPlannedStartDate(tomorrow);
        params.setStatus("0");
        List<AssessmentPlan> list = assessmentPlanMapper.selectAssessmentPlanList(params);
        List<String> render1List = list.stream().map(item ->
                        StrUtil.format(template1, DateUtil.formatDate(item.getPlannedStartDate()), item.getElderlyName(), item.getTemplateName()))
                .collect(Collectors.toList());

        // 查询计划日期大于今天并且状态为未开始的数据
        params.setPlannedStartDate(null);
        params.setParams(MapUtil.builder(new HashMap<String, Object>()).put("beginPlannedStartDate", tomorrow).build());
        params.setStatus("0");
        List<AssessmentPlan> list2 = assessmentPlanMapper.selectAssessmentPlanList(params);
        List<String> render2List = list2.stream().map(item ->
                        StrUtil.format(template2, DateUtil.formatDate(item.getPlannedStartDate()), item.getElderlyName(), item.getTemplateName()))
                .collect(Collectors.toList());

        return (List<String>) CollectionUtil.union(render1List, render2List);

    }

}

