package com.ruoyi.custom.admin.securityguard.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.custom.admin.securityguard.domain.SecurityGuardDevciePeopleInfo;
import com.ruoyi.custom.admin.securityguard.mapper.SecurityGuardDevciePeopleInfoMapper;
import com.ruoyi.custom.admin.securityguard.service.ISecurityGuardDevciePeopleInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 老人与设备关联信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-02-06
 */
@Service
public class SecurityGuardDevciePeopleInfoServiceImpl extends ServiceImpl<SecurityGuardDevciePeopleInfoMapper, SecurityGuardDevciePeopleInfo> implements ISecurityGuardDevciePeopleInfoService {
    @Autowired
    private SecurityGuardDevciePeopleInfoMapper securityGuardDevciePeopleInfoMapper;

    /**
     * 查询老人与设备关联信息
     *
     * @param id 老人与设备关联信息主键
     * @return 老人与设备关联信息
     */
    @Override
    public SecurityGuardDevciePeopleInfo selectSecurityGuardDevciePeopleInfoById(Long id) {
        return securityGuardDevciePeopleInfoMapper.selectSecurityGuardDevciePeopleInfoById(id);
    }

    /**
     * 查询老人与设备关联信息列表
     *
     * @param securityGuardDevciePeopleInfo 老人与设备关联信息
     * @return 老人与设备关联信息
     */
    @Override
    public List<SecurityGuardDevciePeopleInfo> selectSecurityGuardDevciePeopleInfoList(SecurityGuardDevciePeopleInfo securityGuardDevciePeopleInfo) {
        return securityGuardDevciePeopleInfoMapper.selectSecurityGuardDevciePeopleInfoList(securityGuardDevciePeopleInfo);
    }

    /**
     * 新增老人与设备关联信息
     *
     * @param securityGuardDevciePeopleInfo 老人与设备关联信息
     * @return 结果
     */
    @Override
    public int insertSecurityGuardDevciePeopleInfo(SecurityGuardDevciePeopleInfo securityGuardDevciePeopleInfo) {
        SecurityGuardDevciePeopleInfo param = new SecurityGuardDevciePeopleInfo();
        param.setContextid(securityGuardDevciePeopleInfo.getContextid());
        List list = securityGuardDevciePeopleInfoMapper.selectSecurityGuardDevciePeopleInfoList(param);
        if (CollectionUtils.isEmpty(list)) {
            return securityGuardDevciePeopleInfoMapper.insertSecurityGuardDevciePeopleInfo(securityGuardDevciePeopleInfo);
        }
        return -1;
    }

    /**
     * 修改老人与设备关联信息
     *
     * @param securityGuardDevciePeopleInfo 老人与设备关联信息
     * @return 结果
     */
    @Override
    public int updateSecurityGuardDevciePeopleInfo(SecurityGuardDevciePeopleInfo securityGuardDevciePeopleInfo) {
        SecurityGuardDevciePeopleInfo param = new SecurityGuardDevciePeopleInfo();
        param.setContextid(securityGuardDevciePeopleInfo.getContextid());
        List<SecurityGuardDevciePeopleInfo> list = securityGuardDevciePeopleInfoMapper.selectSecurityGuardDevciePeopleInfoList(param);
        if (CollectionUtils.isEmpty(list)) {
            return securityGuardDevciePeopleInfoMapper.updateSecurityGuardDevciePeopleInfo(securityGuardDevciePeopleInfo);
        } else {
            List<SecurityGuardDevciePeopleInfo> filterList = list.stream().filter(a -> !a.getId().equals(securityGuardDevciePeopleInfo.getId())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(filterList)) {
                return -1;
            }
            return securityGuardDevciePeopleInfoMapper.updateSecurityGuardDevciePeopleInfo(securityGuardDevciePeopleInfo);
        }
    }

    /**
     * 批量删除老人与设备关联信息
     *
     * @param ids 需要删除的老人与设备关联信息主键
     * @return 结果
     */
    @Override
    public int deleteSecurityGuardDevciePeopleInfoByIds(Long[] ids) {
        return securityGuardDevciePeopleInfoMapper.deleteSecurityGuardDevciePeopleInfoByIds(ids);
    }

    /**
     * 删除老人与设备关联信息信息
     *
     * @param id 老人与设备关联信息主键
     * @return 结果
     */
    @Override
    public int deleteSecurityGuardDevciePeopleInfoById(Long id) {
        return securityGuardDevciePeopleInfoMapper.deleteSecurityGuardDevciePeopleInfoById(id);
    }
}
