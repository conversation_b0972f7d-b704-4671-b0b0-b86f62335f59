package com.ruoyi.custom.admin.assessment.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.custom.admin.assessment.domain.AssessmentPlan;
import com.ruoyi.custom.admin.assessment.domain.AssessmentResult;

import java.util.Date;
import java.util.List;


/**
 * 评估计划Service接口
 *
 * <AUTHOR>
 * @date 2025-03-03
 */
public interface IAssessmentPlanService {
    /**
     * 查询评估计划
     *
     * @param id 评估计划主键
     * @return 评估计划
     */
    public AssessmentPlan selectAssessmentPlanById(Long id);

    /**
     * 查询评估计划列表
     *
     * @param assessmentPlan 评估计划
     * @return 评估计划集合
     */
    public List<AssessmentPlan> selectAssessmentPlanList(AssessmentPlan assessmentPlan);

    /**
     * 新增评估计划
     *
     * @param assessmentPlan 评估计划
     * @return 结果
     */
    public int insertAssessmentPlan(AssessmentPlan assessmentPlan);

    /**
     * 修改评估计划
     *
     * @param assessmentPlan 评估计划
     * @return 结果
     */
    public int updateAssessmentPlan(AssessmentPlan assessmentPlan);

    /**
     * 批量删除评估计划
     *
     * @param ids 需要删除的评估计划主键集合
     * @return 结果
     */
    public int deleteAssessmentPlanByIds(Long[] ids);

    /**
     * 删除评估计划信息
     *
     * @param id 评估计划主键
     * @return 结果
     */
    public int deleteAssessmentPlanById(Long id);

    /**
     * 详情
     * @param id
     * @return
     */
    JSONArray startOrViewAnswer(Long id);

    /**
     * 创建评估结果json
     * @param assessmentPlan
     * @return
     */
    JSONObject createAssessmentResultJson(AssessmentPlan assessmentPlan);

    /**
     * 获取日历列表
     *
     * @param month
     * @param userId
     * @return
     */
    List<String> getCalendarList(Date month);

    /**
     * 保存评估结果
     *
     * @param assessmentResult
     * @return
     */
    int saveAssessmentResult(AssessmentResult assessmentResult);

    /**
     * 查询评估计划数量
     * @param params
     * @return
     */
    int selectAssessmentPlanCount(AssessmentPlan params);

    /**
     * 提醒通知
     * @param params
     * @return
     */
    List<String> remindNotice(AssessmentPlan params);
}

