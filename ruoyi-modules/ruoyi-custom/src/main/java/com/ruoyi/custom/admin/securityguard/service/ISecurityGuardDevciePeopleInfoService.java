package com.ruoyi.custom.admin.securityguard.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.custom.admin.securityguard.domain.SecurityGuardDevciePeopleInfo;

import java.util.List;

/**
 * 老人与设备关联信息Service接口
 *
 * <AUTHOR>
 * @date 2023-02-06
 */
public interface ISecurityGuardDevciePeopleInfoService extends IService<SecurityGuardDevciePeopleInfo> {
    /**
     * 查询老人与设备关联信息
     *
     * @param id 老人与设备关联信息主键
     * @return 老人与设备关联信息
     */
    public SecurityGuardDevciePeopleInfo selectSecurityGuardDevciePeopleInfoById(Long id);

    /**
     * 查询老人与设备关联信息列表
     *
     * @param securityGuardDevciePeopleInfo 老人与设备关联信息
     * @return 老人与设备关联信息集合
     */
    public List<SecurityGuardDevciePeopleInfo> selectSecurityGuardDevciePeopleInfoList(SecurityGuardDevciePeopleInfo securityGuardDevciePeopleInfo);

    /**
     * 新增老人与设备关联信息
     *
     * @param securityGuardDevciePeopleInfo 老人与设备关联信息
     * @return 结果
     */
    public int insertSecurityGuardDevciePeopleInfo(SecurityGuardDevciePeopleInfo securityGuardDevciePeopleInfo);

    /**
     * 修改老人与设备关联信息
     *
     * @param securityGuardDevciePeopleInfo 老人与设备关联信息
     * @return 结果
     */
    public int updateSecurityGuardDevciePeopleInfo(SecurityGuardDevciePeopleInfo securityGuardDevciePeopleInfo);

    /**
     * 批量删除老人与设备关联信息
     *
     * @param ids 需要删除的老人与设备关联信息主键集合
     * @return 结果
     */
    public int deleteSecurityGuardDevciePeopleInfoByIds(Long[] ids);

    /**
     * 删除老人与设备关联信息信息
     *
     * @param id 老人与设备关联信息主键
     * @return 结果
     */
    public int deleteSecurityGuardDevciePeopleInfoById(Long id);
}
