package com.ruoyi.custom.admin.securityguard.mapper;


import com.ruoyi.custom.admin.securityguard.domain.SecurityGuardDeviceInfo;
import com.ruoyi.custom.admin.securityguard.domain.SecurityGuardManufacturerInfo;

import java.util.List;

/**
 * 厂商信息Mapper接口
 *
 * <AUTHOR>
 * @date 2023-02-03
 */
public interface SecurityGuardManufacturerInfoMapper {
    /**
     * 查询厂商信息
     *
     * @param id 厂商信息主键
     * @return 厂商信息
     */
    public SecurityGuardManufacturerInfo selectSecurityGuardManufacturerInfoById(Long id);

    /**
     * 查询厂商信息列表
     *
     * @param securityGuardManufacturerInfo 厂商信息
     * @return 厂商信息集合
     */
    public List<SecurityGuardManufacturerInfo> selectSecurityGuardManufacturerInfoList(SecurityGuardManufacturerInfo securityGuardManufacturerInfo);

    /**
     * 新增厂商信息
     *
     * @param securityGuardManufacturerInfo 厂商信息
     * @return 结果
     */
    public int insertSecurityGuardManufacturerInfo(SecurityGuardManufacturerInfo securityGuardManufacturerInfo);

    /**
     * 修改厂商信息
     *
     * @param securityGuardManufacturerInfo 厂商信息
     * @return 结果
     */
    public int updateSecurityGuardManufacturerInfo(SecurityGuardManufacturerInfo securityGuardManufacturerInfo);

    /**
     * 删除厂商信息
     *
     * @param id 厂商信息主键
     * @return 结果
     */
    public int deleteSecurityGuardManufacturerInfoById(Long id);

    /**
     * 批量删除厂商信息
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSecurityGuardManufacturerInfoByIds(Long[] ids);

    /**
     * 批量删除设备信息
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSecurityGuardDeviceInfoByManufacturerIds(Long[] ids);

    /**
     * 批量新增设备信息
     *
     * @param securityGuardDeviceInfoList 设备信息列表
     * @return 结果
     */
    public int batchSecurityGuardDeviceInfo(List<SecurityGuardDeviceInfo> securityGuardDeviceInfoList);


    /**
     * 通过厂商信息主键删除设备信息信息
     *
     * @param id 厂商信息ID
     * @return 结果
     */
    public int deleteSecurityGuardDeviceInfoByManufacturerId(Long id);
}
