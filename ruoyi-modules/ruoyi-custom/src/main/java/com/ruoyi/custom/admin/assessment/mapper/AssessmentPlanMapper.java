package com.ruoyi.custom.admin.assessment.mapper;

import com.ruoyi.custom.admin.assessment.domain.AssessmentPlan;
import com.ruoyi.custom.admin.assessment.domain.AssessmentResult;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 评估计划Mapper接口
 *
 * <AUTHOR>
 * @date 2025-03-03
 */
public interface AssessmentPlanMapper {
    /**
     * 查询评估计划
     *
     * @param id 评估计划主键
     * @return 评估计划
     */
    public AssessmentPlan selectAssessmentPlanById(Long id);

    /**
     * 查询评估计划列表
     *
     * @param assessmentPlan 评估计划
     * @return 评估计划集合
     */
    public List<AssessmentPlan> selectAssessmentPlanList(AssessmentPlan assessmentPlan);

    /**
     * 新增评估计划
     *
     * @param assessmentPlan 评估计划
     * @return 结果
     */
    public int insertAssessmentPlan(AssessmentPlan assessmentPlan);

    /**
     * 修改评估计划
     *
     * @param assessmentPlan 评估计划
     * @return 结果
     */
    public int updateAssessmentPlan(AssessmentPlan assessmentPlan);

    /**
     * 删除评估计划
     *
     * @param id 评估计划主键
     * @return 结果
     */
    public int deleteAssessmentPlanById(Long id);

    /**
     * 批量删除评估计划
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteAssessmentPlanByIds(Long[] ids);

    /**
     * 根据月份&当前用户id返回选中月份需要评估的日期
     *
     * @param month
     * @param userId
     * @return
     */
    List<String> getCalendarList(@Param("month") Date month);

    /**
     * 保存评估结果  TODO 目前只保存建议，其他字段根据需要再加
     *
     * @param assessmentResult
     * @return
     */
    int saveAssessmentResult(AssessmentResult assessmentResult);

    /**
     * 查询评估计划数量
     * @param params
     * @return
     */
    int selectAssessmentPlanCount(AssessmentPlan params);

    // /**
    //  * 详情
    //  *
    //  * @param id
    //  * @return
    //  */
    // AssessmentPlan getAnswer(Long id);
}

