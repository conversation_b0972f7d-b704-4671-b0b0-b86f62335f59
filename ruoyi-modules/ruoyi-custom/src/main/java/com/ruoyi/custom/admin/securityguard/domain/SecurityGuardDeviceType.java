package com.ruoyi.custom.admin.securityguard.domain;

import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@Data
@ApiModel(value = "设备类型表")
public class SecurityGuardDeviceType {

    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "删除标志（0显示，1隐藏）")
    private String delFlag;
}

