package com.ruoyi.custom.api.assessmentApp;

import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.custom.admin.assessment.domain.AssessmentPlan;
import com.ruoyi.custom.admin.assessment.service.IAssessmentPlanService;
import com.ruoyi.custom.admin.elderlyPeople.domain.ElderlyPeopleInfo;
import com.ruoyi.custom.admin.elderlyPeople.service.IElderlyPeopleInfoService;
import com.ruoyi.custom.admin.marketing.domain.MarketingCustomerInfo;
import com.ruoyi.custom.admin.marketing.service.MarketingCustomerInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 评估端APP-老人管理 Controller
 *
 * <AUTHOR>
 * @date 2022-04-09
 */
@RestController("assessmentAppMarketingCustomerInfoController")
@RequestMapping("/assessmentApp/marketingCustomerInfo")
@Api(tags = "评估端APP-老人管理")
public class MarketingCustomerInfoController extends BaseController {

    @Autowired
    private IAssessmentPlanService assessmentPlanService;

    @Autowired
    private MarketingCustomerInfoService marketingCustomerInfoService;


    /**
     * 获取所有意向客户信息列表
     */
    @GetMapping("/list")
    @ApiOperation(value = "获取所有意向客户信息列表")
    public TableDataInfo list(MarketingCustomerInfo marketingCustomerInfo) {
        // 填充查询条件
        marketingCustomerInfo.setCustomerType("1");
        // marketingCustomerInfo.setMarketerId(SecurityUtils.getUserId());

        startPage();
        List<MarketingCustomerInfo> list = marketingCustomerInfoService.selectMarketingCustomerInfoList(marketingCustomerInfo);
        return getDataTable(list);
    }

    @GetMapping("/assessment/list")
    @ApiOperation(value = "评估记录")
    public TableDataInfo assessmentList(AssessmentPlan assessmentPlan) {
        startPage();
        assessmentPlan.setStatus("2"); // 已完成
        List<AssessmentPlan> list = assessmentPlanService.selectAssessmentPlanList(assessmentPlan);
        return getDataTable(list);
    }
}
