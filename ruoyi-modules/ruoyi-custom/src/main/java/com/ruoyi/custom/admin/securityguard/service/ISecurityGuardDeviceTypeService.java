package com.ruoyi.custom.admin.securityguard.service;

import com.ruoyi.custom.admin.securityguard.domain.SecurityGuardDeviceType;

import java.util.List;


/**
 * 设备类型Service接口
 *
 * <AUTHOR>
 * @date 2025-03-29
 */
public interface ISecurityGuardDeviceTypeService {
    /**
     * 查询设备类型
     *
     * @param id 设备类型主键
     * @return 设备类型
     */
    public SecurityGuardDeviceType selectSecurityGuardDeviceTypeById(Long id);

    /**
     * 查询设备类型列表
     *
     * @param securityGuardDeviceType 设备类型
     * @return 设备类型集合
     */
    public List<SecurityGuardDeviceType> selectSecurityGuardDeviceTypeList(SecurityGuardDeviceType securityGuardDeviceType);

    /**
     * 新增设备类型
     *
     * @param securityGuardDeviceType 设备类型
     * @return 结果
     */
    public int insertSecurityGuardDeviceType(SecurityGuardDeviceType securityGuardDeviceType);

    /**
     * 修改设备类型
     *
     * @param securityGuardDeviceType 设备类型
     * @return 结果
     */
    public int updateSecurityGuardDeviceType(SecurityGuardDeviceType securityGuardDeviceType);

    /**
     * 批量删除设备类型
     *
     * @param ids 需要删除的设备类型主键集合
     * @return 结果
     */
    public int deleteSecurityGuardDeviceTypeByIds(Long[] ids);

    /**
     * 删除设备类型信息
     *
     * @param id 设备类型主键
     * @return 结果
     */
    public int deleteSecurityGuardDeviceTypeById(Long id);
}

