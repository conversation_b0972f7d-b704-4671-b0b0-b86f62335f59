package com.ruoyi.custom.admin.securityguard.service.impl;


import com.ruoyi.custom.admin.securityguard.domain.SecurityGuardDeviceType;
import com.ruoyi.custom.admin.securityguard.mapper.SecurityGuardDeviceTypeMapper;
import com.ruoyi.custom.admin.securityguard.service.ISecurityGuardDeviceTypeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 设备类型Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-03-29
 */
@Service
public class SecurityGuardDeviceTypeServiceImpl implements ISecurityGuardDeviceTypeService {
    @Autowired
    private SecurityGuardDeviceTypeMapper securityGuardDeviceTypeMapper;

    /**
     * 查询设备类型
     *
     * @param id 设备类型主键
     * @return 设备类型
     */
    @Override
    public SecurityGuardDeviceType selectSecurityGuardDeviceTypeById(Long id) {
        return securityGuardDeviceTypeMapper.selectSecurityGuardDeviceTypeById(id);
    }

    /**
     * 查询设备类型列表
     *
     * @param securityGuardDeviceType 设备类型
     * @return 设备类型
     */
    @Override
    public List<SecurityGuardDeviceType> selectSecurityGuardDeviceTypeList(SecurityGuardDeviceType securityGuardDeviceType) {
        return securityGuardDeviceTypeMapper.selectSecurityGuardDeviceTypeList(securityGuardDeviceType);
    }

    /**
     * 新增设备类型
     *
     * @param securityGuardDeviceType 设备类型
     * @return 结果
     */
    @Override
    public int insertSecurityGuardDeviceType(SecurityGuardDeviceType securityGuardDeviceType) {
        return securityGuardDeviceTypeMapper.insertSecurityGuardDeviceType(securityGuardDeviceType);
    }

    /**
     * 修改设备类型
     *
     * @param securityGuardDeviceType 设备类型
     * @return 结果
     */
    @Override
    public int updateSecurityGuardDeviceType(SecurityGuardDeviceType securityGuardDeviceType) {
        return securityGuardDeviceTypeMapper.updateSecurityGuardDeviceType(securityGuardDeviceType);
    }

    /**
     * 批量删除设备类型
     *
     * @param ids 需要删除的设备类型主键
     * @return 结果
     */
    @Override
    public int deleteSecurityGuardDeviceTypeByIds(Long[] ids) {
        return securityGuardDeviceTypeMapper.deleteSecurityGuardDeviceTypeByIds(ids);
    }

    /**
     * 删除设备类型信息
     *
     * @param id 设备类型主键
     * @return 结果
     */
    @Override
    public int deleteSecurityGuardDeviceTypeById(Long id) {
        return securityGuardDeviceTypeMapper.deleteSecurityGuardDeviceTypeById(id);
    }
}

