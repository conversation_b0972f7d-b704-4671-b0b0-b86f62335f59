package com.ruoyi.custom.admin.elderlyPeople.service.impl;

import java.util.List;

import com.ruoyi.common.core.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.custom.admin.elderlyPeople.mapper.ElderlyPeopleHealthFileInfoMapper;
import com.ruoyi.custom.admin.elderlyPeople.domain.ElderlyPeopleHealthFileInfo;
import com.ruoyi.custom.admin.elderlyPeople.service.IElderlyPeopleHealthFileInfoService;

/**
 * 老人健康档案Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-04-06
 */
@Service
public class ElderlyPeopleHealthFileInfoServiceImpl implements IElderlyPeopleHealthFileInfoService {
    @Autowired
    private ElderlyPeopleHealthFileInfoMapper elderlyPeopleHealthFileInfoMapper;

    /**
     * 查询老人健康档案
     *
     * @param id 老人健康档案主键
     * @return 老人健康档案
     */
    @Override
    public ElderlyPeopleHealthFileInfo selectElderlyPeopleHealthFileInfoById(Long id) {
        return elderlyPeopleHealthFileInfoMapper.selectElderlyPeopleHealthFileInfoById(id);
    }


    /**
     * 健康管理模块中的健康信息
     *
     * @param userId
     * @return
     */
    @Override
    public ElderlyPeopleHealthFileInfo getDataInfoByUserId(String userId) {
        return elderlyPeopleHealthFileInfoMapper.getDataInfoByUserId(userId);
    }

    /**
     * 查询老人健康档案列表
     *
     * @param elderlyPeopleHealthFileInfo 老人健康档案
     * @return 老人健康档案
     */
    @Override
    public List<ElderlyPeopleHealthFileInfo> selectElderlyPeopleHealthFileInfoList(ElderlyPeopleHealthFileInfo elderlyPeopleHealthFileInfo) {
        return elderlyPeopleHealthFileInfoMapper.selectElderlyPeopleHealthFileInfoList(elderlyPeopleHealthFileInfo);
    }

    /**
     * 新增老人健康档案
     *
     * @param elderlyPeopleHealthFileInfo 老人健康档案
     * @return 结果
     */
    @Override
    public int insertElderlyPeopleHealthFileInfo(ElderlyPeopleHealthFileInfo elderlyPeopleHealthFileInfo) {
        elderlyPeopleHealthFileInfo.setCreateTime(DateUtils.getNowDate());
//        elderlyPeopleHealthFileInfo.setUserId(elderlyPeopleHealthFileInfo.getuId());
        return elderlyPeopleHealthFileInfoMapper.insertElderlyPeopleHealthFileInfo(elderlyPeopleHealthFileInfo);
    }

    /**
     * 修改老人健康档案
     *
     * @param elderlyPeopleHealthFileInfo 老人健康档案
     * @return 结果
     */
    @Override
    public int updateElderlyPeopleHealthFileInfo(ElderlyPeopleHealthFileInfo elderlyPeopleHealthFileInfo) {
        elderlyPeopleHealthFileInfo.setUpdateTime(DateUtils.getNowDate());
        return elderlyPeopleHealthFileInfoMapper.updateElderlyPeopleHealthFileInfo(elderlyPeopleHealthFileInfo);
    }

    /**
     * 批量删除老人健康档案
     *
     * @param ids 需要删除的老人健康档案主键
     * @return 结果
     */
    @Override
    public int deleteElderlyPeopleHealthFileInfoByIds(Long[] ids) {
        return elderlyPeopleHealthFileInfoMapper.deleteElderlyPeopleHealthFileInfoByIds(ids);
    }

    /**
     * 删除老人健康档案信息
     *
     * @param id 老人健康档案主键
     * @return 结果
     */
    @Override
    public int deleteElderlyPeopleHealthFileInfoById(Long id) {
        return elderlyPeopleHealthFileInfoMapper.deleteElderlyPeopleHealthFileInfoById(id);
    }
}
