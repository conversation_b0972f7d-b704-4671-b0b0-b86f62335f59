

create table if not exists  `t_elderly_capacity_assessment`
(
    id                              bigint auto_increment comment '主键'
    primary key,
    serial_number                   varchar(50) not null comment '编号，格式：pgjh-20230202-0001',
    assessment_obj_basics_info      json        null comment '评估对象基本信息',
    msg_supplier_contact_info       json        null comment '信息提供者及联系人信息表',
    disease_diagnosis_drug_usage    json        null comment '疾病诊断和用药情况',
    health_related_issues           json        null comment '健康相关问题',
    physiology_body_assessment      json        null comment '生理及身体评估',
    old_people_ability_assessment   json        null comment '老年人能力评估表',
    basic_motor_ability_assessment  json        null comment '基础运动能力评估表',
    mental_state                    json        null comment '精神状态表',
    perception_social_participation json        null comment '感知觉与社会参与评估表',
    health_problems                 json        null comment '健康问题',
    remarks                         text        null comment '备注',
    file_video_list                 json        null comment '视频url',
    file_img_list                   json        null comment '图片url',
    assessment_Id                   int         not null comment '评估id'
    )
    comment '老年人能力评估表' charset = utf8mb4;

set @ry_sql = 'select 1 from dual';
select 'alter table t_elderly_capacity_assessment add unique uniq_serial_number(`serial_number`);'
into @ry_sql from dual
where (select count(1)
       from information_schema.STATISTICS
       where TABLE_NAME = 't_elderly_capacity_assessment'
         and TABLE_SCHEMA = (select database())
         and upper(INDEX_NAME) = upper('uniq_serial_number')) = 0;
prepare custom from @ry_sql;
execute custom;
deallocate prepare custom;
