UPDATE `ry-cloud`.`sys_dict_data`
SET `dict_sort` = 2,
    `dict_label` = '超时未归',
    `dict_value` = '2',
    `dict_type` = 'custom_live_leave_state',
    `css_class` = NULL,
    `list_class` = 'default',
    `is_default` = 'N',
    `status` = '0',
    `create_by` = 'admin',
    `create_time` = '2024-12-05 14:14:55',
    `update_by` = 'admin',
    `update_time` = '2025-04-28 11:21:58',
    `remark` = NULL
WHERE `dict_type` = 'custom_live_leave_state' AND `dict_value` = '2';

UPDATE `ry-cloud`.`sys_dict_data`
SET `dict_sort` = 1,
    `dict_label` = '已销假',
    `dict_value` = '1',
    `dict_type` = 'custom_live_leave_state',
    `css_class` = NULL,
    `list_class` = 'default',
    `is_default` = 'N',
    `status` = '0',
    `create_by` = 'admin',
    `create_time` = '2024-12-05 14:14:48',
    `update_by` = 'admin',
    `update_time` = '2025-04-28 11:21:33',
    `remark` = NULL
WHERE `dict_type` = 'custom_live_leave_state' AND `dict_value` = '1';

INSERT INTO `ry-cloud`.`sys_dict_type` (`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( '机构养老-请假-是否免除餐费', 'custom_leave_is_free_meal', '0', 'admin', '2025-04-28 11:49:22', '', NULL, NULL);
INSERT INTO `ry-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2, '是', '1', 'custom_leave_is_free_meal', NULL, 'default', 'N', '0', 'admin', '2025-04-28 11:49:55', '', NULL, NULL);
INSERT INTO `ry-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1, '否', '0', 'custom_leave_is_free_meal', NULL, 'default', 'N', '0', 'admin', '2025-04-28 11:49:42', '', NULL, NULL);

INSERT INTO `ry-cloud`.`sys_dict_type` (`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( '机构养老-请假-申请人类型', 'custom_leave_applicant_type', '0', 'admin', '2025-04-28 13:47:49', '', NULL, NULL);
INSERT INTO `ry-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2, '亲属', '2', 'custom_leave_applicant_type', NULL, 'default', 'N', '0', 'admin', '2025-04-28 13:48:17', '', NULL, NULL);
INSERT INTO `ry-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1, '本人', '1', 'custom_leave_applicant_type', NULL, 'default', 'N', '0', 'admin', '2025-04-28 13:48:09', '', NULL, NULL);

INSERT INTO `ry-cloud`.`sys_dict_type` (`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( '机构养老-消费账户明细-类型', 'custom_consume_account_type', '0', 'admin', '2025-04-29 14:31:02', '', NULL, NULL);
INSERT INTO `ry-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (3, '充值', '3', 'custom_consume_account_type', NULL, 'default', 'N', '0', 'admin', '2025-04-29 14:31:27', '', NULL, NULL);
INSERT INTO `ry-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2, '支出', '2', 'custom_consume_account_type', NULL, 'default', 'N', '0', 'admin', '2025-04-29 14:31:20', '', NULL, NULL);
INSERT INTO `ry-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1, '收入', '1', 'custom_consume_account_type', NULL, 'default', 'N', '0', 'admin', '2025-04-29 14:31:13', '', NULL, NULL);

INSERT INTO `ry-cloud`.`sys_dict_type` ( `dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('机构养老-护理项目-服务人员类型', 'custom_care_project_staff_type', '0', 'admin', '2025-04-29 16:19:04', '', NULL, NULL);
INSERT INTO `ry-cloud`.`sys_dict_data` ( `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( 2, '护士', '2', 'custom_care_project_staff_type', NULL, 'default', 'N', '0', 'admin', '2025-04-29 16:19:26', '', NULL, NULL);
INSERT INTO `ry-cloud`.`sys_dict_data` ( `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( 1, '护工', '1', 'custom_care_project_staff_type', NULL, 'default', 'N', '0', 'admin', '2025-04-29 16:19:17', '', NULL, NULL);

INSERT INTO `ry-cloud`.`sys_dict_type` (`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('机构养老-用户增值服务-服务频次', 'custom_value_added_service_frequency', '0', 'admin', '2025-04-29 17:55:54', '', NULL, NULL);
INSERT INTO `ry-cloud`.`sys_dict_type` (`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('机构养老-用户增值服务-计费方式', 'custom_value_added_service_charging_method', '0', 'admin', '2025-04-29 17:55:13', '', NULL, NULL);
INSERT INTO `ry-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2, '每月', '2', 'custom_value_added_service_frequency', NULL, 'default', 'N', '0', 'admin', '2025-04-29 17:56:10', '', NULL, NULL);
INSERT INTO `ry-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1, '每天', '1', 'custom_value_added_service_frequency', NULL, 'default', 'N', '0', 'admin', '2025-04-29 17:56:05', '', NULL, NULL);
INSERT INTO `ry-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2, '月', '2', 'custom_value_added_service_charging_method', NULL, 'default', 'N', '0', 'admin', '2025-04-29 17:55:36', '', NULL, NULL);
INSERT INTO `ry-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1, '一次性', '1', 'custom_value_added_service_charging_method', NULL, 'default', 'N', '0', 'admin', '2025-04-29 17:55:31', '', NULL, NULL);

INSERT INTO `ry-cloud`.`sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `applet_icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `platform`, `applet_path`) VALUES (3967, '餐费套餐', 2036, 6, 'mealPckage', 'dietManage/mealPckage/index', NULL, 1, 0, 'C', '0', '0', NULL, 'drag', NULL, 'admin', '2025-04-29 19:54:10', '', NULL, '', '100', NULL);
INSERT INTO `ry-cloud`.`sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `applet_icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `platform`, `applet_path`) VALUES (3966, '服务类型', 2008, 9, 'typeOfService', 'NursingManagement/typeOfService/index', NULL, 1, 0, 'C', '0', '0', NULL, 'cascader', NULL, 'admin', '2025-04-28 20:55:33', '', NULL, '', '100', NULL);
INSERT INTO `ry-cloud`.`sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `applet_icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `platform`, `applet_path`) VALUES (3965, '增值服务', 2008, 8, 'valueAddedServices', 'NursingManagement/valueAddedServices/index', NULL, 1, 0, 'C', '0', '0', NULL, 'link', NULL, 'admin', '2025-04-28 20:06:26', '', NULL, '', '100', NULL);
INSERT INTO `ry-cloud`.`sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `applet_icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `platform`, `applet_path`) VALUES (3964, '用户选购记录', 2008, 7, 'userChooseRecord', 'NursingManagement/userChooseRecord/index', NULL, 1, 0, 'C', '0', '0', '', 'documentation', NULL, 'admin', '2025-04-28 19:30:55', 'admin', '2025-04-28 19:35:07', '', '100', NULL);
INSERT INTO `ry-cloud`.`sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `applet_icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `platform`, `applet_path`) VALUES (3963, '缴费提醒', 2012, 14, 'feesRemind', 'costManage/feesRemind/index', NULL, 1, 0, 'C', '0', '0', NULL, 'download', NULL, 'admin', '2025-04-25 14:14:28', '', NULL, '', '100', NULL);
INSERT INTO `ry-cloud`.`sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `applet_icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `platform`, `applet_path`) VALUES (3961, '费用统计', 2012, 13, 'feeStatistics', 'costManage/feeStatistics/index', NULL, 1, 0, 'C', '0', '0', '', 'cascader', NULL, 'admin', '2025-04-25 13:58:06', 'admin', '2025-04-25 14:12:33', '', '100', NULL);
INSERT INTO `ry-cloud`.`sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `applet_icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `platform`, `applet_path`) VALUES (3960, '医疗保障金支出记录', 2012, 12, 'medicalInsuranceFundExpenseRecord', 'costManage/medicalInsuranceFundExpenseRecord/index', NULL, 1, 0, 'C', '0', '0', NULL, 'documentation', NULL, 'admin', '2025-04-25 11:13:32', '', NULL, '', '100', NULL);
INSERT INTO `ry-cloud`.`sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `applet_icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `platform`, `applet_path`) VALUES (3959, '消费账户明细', 2012, 11, 'consumptionAccountDetails', 'costManage/consumptionAccountDetails/index', NULL, 1, 0, 'C', '0', '0', '', 'client', NULL, 'admin', '2025-04-25 10:43:42', 'admin', '2025-04-25 10:44:20', '', '100', NULL);

UPDATE `ry-cloud`.`sys_menu` SET `menu_name` = '选购服务', `parent_id` = 2008, `order_num` = 3, `path` = 'purchaseOfServicesList', `component` = 'NursingManagement/purchaseOfServices/list', `query` = NULL, `is_frame` = 1, `is_cache` = 0, `menu_type` = 'C', `visible` = '0', `status` = '0', `perms` = '', `icon` = 'edit', `applet_icon` = NULL, `create_by` = 'admin', `create_time` = '2024-11-25 15:16:25', `update_by` = 'admin', `update_time` = '2025-04-28 19:19:20', `remark` = '', `platform` = '100', `applet_path` = NULL WHERE `menu_id` = 3864;
UPDATE `ry-cloud`.`sys_menu` SET `menu_name` = '消费账户充值', `parent_id` = 2012, `order_num` = 2, `path` = 'costPayment', `component` = 'costManage/costPayment/index', `query` = NULL, `is_frame` = 1, `is_cache` = 0, `menu_type` = 'C', `visible` = '0', `status` = '0', `perms` = '', `icon` = 'component', `applet_icon` = NULL, `create_by` = 'admin', `create_time` = '2022-03-29 16:24:26', `update_by` = 'admin', `update_time` = '2025-04-25 10:56:10', `remark` = '', `platform` = '100', `applet_path` = NULL WHERE `menu_id` = 2015;
