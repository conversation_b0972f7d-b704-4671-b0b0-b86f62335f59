ALTER TABLE `ry-custom`.`t_fee_pay_info`
    DROP COLUMN `pay_user_id`,
    DROP COLUMN `pay_user_type`;

ALTER TABLE `ry-custom`.`t_fee_pay_info`
    MODIFY COLUMN `pay_type` varchar(4) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '支付方式（现金，刷卡，微信，支付宝）' AFTER `pay_time`;

ALTER TABLE `ry-custom`.`t_fee_pay_info`
    MODIFY COLUMN `pay_time` datetime NULL DEFAULT NULL COMMENT '充值时间' AFTER `user_id`,
    MODIFY COLUMN `pay_type` varchar(4) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '充值方式，字典：t_payment_record_payment_method，1：现金，2：微信，3：支付宝' AFTER `pay_time`,
    MODIFY COLUMN `pay_amount` decimal(10, 4) NULL DEFAULT NULL COMMENT '充值金额' AFTER `pay_type`,
    MODIFY COLUMN `begin_paid_amount` decimal(10, 4) NULL DEFAULT NULL COMMENT '充值前余额' AFTER `pay_amount`,
    MODIFY COLUMN `after_paid_amount` decimal(10, 4) NULL DEFAULT NULL COMMENT '充值后余额' AFTER `begin_paid_amount`;

CREATE TABLE `t_payment_change_record` (
                                           `id` varchar(64) NOT NULL COMMENT '主键ID',
                                           `contract_number` varchar(100) NOT NULL COMMENT '合同编号，管理表：t_contract_info',
                                           `elderly_id` varchar(64) NOT NULL COMMENT '老人ID',
                                           `elderly_name` varchar(50) DEFAULT NULL COMMENT '老人姓名',
                                           `contract_start_date` date DEFAULT NULL COMMENT '合同开始日期',
                                           `contract_end_date` date DEFAULT NULL COMMENT '合同结束日期',
                                           `contract_cycle` int DEFAULT NULL COMMENT '合同周期（月数）',
                                           `care_level` varchar(50) DEFAULT NULL COMMENT '护理级别',
                                           `bed_name` varchar(50) DEFAULT NULL COMMENT '房间号',
                                           `change_date` date DEFAULT NULL COMMENT '变更日期',
                                           `remark` varchar(500) DEFAULT NULL COMMENT '备注',
                                           `details` json DEFAULT NULL COMMENT '变更详情（List<Detail> JSON）',
                                           PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='缴费确认单';

ALTER TABLE `ry-custom`.t_live_leave_records
    ADD COLUMN reject_reason varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '拒绝理由' AFTER audit_state,
    ADD COLUMN is_free_meal varchar(4) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '是否免除餐费，字典：custom_leave_is_free_meal；1：否，1：是' AFTER reject_reason,
    ADD COLUMN applicant_name varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '申请人姓名' AFTER is_free_meal,
    ADD COLUMN companion_name varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '陪同人姓名' AFTER applicant_name,
    ADD COLUMN companion_phone varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '陪同人电话' AFTER companion_name,
    ADD COLUMN cancel_user_id varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '销假人id（关联sys_user表）' AFTER companion_phone,
    ADD COLUMN cancel_user_name varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '销假人姓名' AFTER cancel_user_id;
ALTER TABLE `ry-custom`.`t_live_leave_records`
    ADD COLUMN `applicant_type` varchar(4) NULL COMMENT '申请人类型，字典：custom_leave_applicant_type；1：本人，2：亲属' AFTER `is_free_meal`;

-- 餐费套餐基础表
CREATE TABLE `ry-custom`.`t_meal_combo_base` (
                                     `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                     `name` VARCHAR(255) DEFAULT NULL COMMENT '套餐名称',
                                     `remark` TEXT DEFAULT NULL COMMENT '备注',
                                     `del_flag` VARCHAR(2) DEFAULT '0' COMMENT '删除标识（0正常，1删除）',
                                     PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='餐费套餐基础表';

-- 餐费套餐费用表
CREATE TABLE `ry-custom`.`t_meal_combo_fee` (
                                    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                    `base_id` BIGINT NOT NULL COMMENT '餐费套餐ID，关联表：t_meal_combo_base',
                                    `fee` DECIMAL(10, 2) DEFAULT NULL COMMENT '费用（月）',
                                    `version` VARCHAR(10) DEFAULT NULL COMMENT '版本号（格式如：V1、V2等）',
                                    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='餐费套餐费用表';

CREATE TABLE `ry-custom`.`t_live_meal_combo_record` (
                                            `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                            `live_id` varchar(64) NOT NULL COMMENT '居住ID，关联表：t_live_base_info',
                                            `meal_id` bigint NOT NULL COMMENT '餐费套餐ID，关联表：t_meal_combo_base',
                                            `meal_fee_id` bigint NOT NULL COMMENT '餐费套餐费用ID，关联表：t_meal_combo_fee',
                                            `status` varchar(2) NOT NULL COMMENT '状态，0：生效，1：无效',
                                            PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='居住餐费套餐记录表';

CREATE TABLE `t_user_value_added_service` (
                                              `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                              `elderly_id` varchar(64) NOT NULL COMMENT '老人ID，关联表：t_elderly_people_info',
                                              `service_type_id` bigint NOT NULL COMMENT '服务类型ID，关联表：t_service_type',
                                              `service_project_id` bigint NOT NULL COMMENT '服务项目ID，关联表：t_service_project',
                                              `consumable` varchar(255) DEFAULT NULL COMMENT '耗材',
                                              `charging_method` varchar(2) DEFAULT NULL COMMENT '计费方式，字典：custom_value_added_service_charging_method；1：一次性，2：月',
                                              `service_start_date` date DEFAULT NULL COMMENT '服务开始日期',
                                              `service_end_date` date DEFAULT NULL COMMENT '服务结束日期',
                                              `service_frequency` varchar(2) DEFAULT NULL COMMENT '服务频次，字典：custom_value_added_service_frequency；1：每天，2：每月',
                                              `service_times` int DEFAULT NULL COMMENT '服务次数',
                                              `fee` decimal(10,2) DEFAULT NULL COMMENT '费用',
                                              PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='用户增值服务表';

CREATE TABLE `t_user_value_added_service_bill` (
                                                   `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                                   `user_value_added_service_id` bigint NOT NULL COMMENT '用户增值服务ID，关联表：t_user_value_added_service',
                                                   `cycle` varchar(50) DEFAULT NULL COMMENT '周期，格式：2025/06/01-2026/07/01',
                                                   `fee` decimal(10,2) DEFAULT NULL COMMENT '费用',
                                                   `is_confirm` varchar(2) DEFAULT NULL COMMENT '是否已入账，代表缴费确认单已确认，0：未确认，1：已确认',
                                                   PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='用户增值服务账单表';

CREATE TABLE `t_consume_account_detail` (
                                            `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                            `elderly_id` VARCHAR(64) NOT NULL COMMENT '老人ID',
                                            `elderly_name` VARCHAR(50) DEFAULT NULL COMMENT '老人姓名',
                                            `elderly_phone` VARCHAR(20) DEFAULT NULL COMMENT '老人手机号',
                                            `type` VARCHAR(2) DEFAULT NULL COMMENT '类型，字典：custom_consume_account_type；1：收入，2：支出，3：充值',
                                            `amount` DECIMAL(10,2) DEFAULT NULL COMMENT '金额',
                                            `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                            PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='消费账户明细表';

ALTER TABLE `ry-custom`.`t_contract_info`
    ADD COLUMN `bed_discount` VARCHAR(10) NULL COMMENT '床位折扣' AFTER `contract_end_date`,
    ADD COLUMN `discount_reason` text NULL COMMENT '折扣理由' AFTER `bed_discount`;

ALTER TABLE `ry-custom`.`t_service_project`
    DROP COLUMN `charge_mode`;

ALTER TABLE `ry-custom`.`t_care_project_base_info`
    ADD COLUMN `staff_type` varchar(4) NULL COMMENT '服务人员类型，字典：custom_care_project_staff_type；\r\n1：护士，2：护工' AFTER `care_name`;
