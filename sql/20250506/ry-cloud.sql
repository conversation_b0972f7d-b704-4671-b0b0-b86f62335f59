UPDATE `ry-cloud`.`sys_menu` SET `menu_name` = '护理类型', `parent_id` = 2008, `order_num` = 9, `path` = 'typeOfService', `component` = 'NursingManagement/typeOfService/index', `query` = NULL, `is_frame` = 1, `is_cache` = 0, `menu_type` = 'C', `visible` = '0', `status` = '0', `perms` = '', `icon` = 'cascader', `applet_icon` = NULL, `create_by` = 'admin', `create_time` = '2025-04-28 20:55:33', `update_by` = 'admin', `update_time` = '2025-05-06 20:05:43', `remark` = '', `platform` = '100', `applet_path` = NULL WHERE `menu_id` = 3966;

INSERT INTO `ry-cloud`.`sys_dict_type` (`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('机构养老-用户选购服务-状态', 'custom_service_project_status', '0', 'admin', '2025-05-06 20:27:02', '', NULL, NULL);
INSERT INTO `ry-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2, '下架', '1', 'custom_service_project_status', NULL, 'default', 'N', '0', 'admin', '2025-05-06 20:27:27', '', NULL, NULL);
INSERT INTO `ry-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1, '上架', '0', 'custom_service_project_status', NULL, 'default', 'N', '0', 'admin', '2025-05-06 20:27:20', '', NULL, NULL);

UPDATE `ry-cloud`.`sys_menu` SET `menu_name` = '费用缴纳', `parent_id` = 2012, `order_num` = 6, `path` = 'paymentOfFee', `component` = 'costManage/paymentOfFee/index', `query` = NULL, `is_frame` = 1, `is_cache` = 0, `menu_type` = 'C', `visible` = '1', `status` = '0', `perms` = '', `icon` = 'textarea', `applet_icon` = NULL, `create_by` = 'admin', `create_time` = '2025-04-13 11:30:08', `update_by` = 'admin', `update_time` = '2025-05-07 09:32:16', `remark` = '', `platform` = '100', `applet_path` = NULL WHERE `menu_id` = 3952;
UPDATE `ry-cloud`.`sys_menu` SET `menu_name` = '阶段性费用', `parent_id` = 2012, `order_num` = 4, `path` = 'phasedCost', `component` = 'costManage/phasedCost/index', `query` = NULL, `is_frame` = 1, `is_cache` = 0, `menu_type` = 'C', `visible` = '1', `status` = '0', `perms` = '', `icon` = 'dashboard', `applet_icon` = NULL, `create_by` = 'admin', `create_time` = '2022-03-30 16:44:40', `update_by` = 'admin', `update_time` = '2025-05-07 09:32:07', `remark` = '', `platform` = '100', `applet_path` = NULL WHERE `menu_id` = 2019;
UPDATE `ry-cloud`.`sys_menu` SET `menu_name` = '周期账单', `parent_id` = 2012, `order_num` = 1, `path` = 'periodicBill', `component` = 'costManage/periodicBill/index', `query` = NULL, `is_frame` = 1, `is_cache` = 0, `menu_type` = 'C', `visible` = '1', `status` = '0', `perms` = '', `icon` = 'email', `applet_icon` = NULL, `create_by` = 'admin', `create_time` = '2022-03-29 15:12:56', `update_by` = 'admin', `update_time` = '2025-05-07 09:32:02', `remark` = '', `platform` = '100', `applet_path` = NULL WHERE `menu_id` = 2014;

INSERT INTO `ry-cloud`.`sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `applet_icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `platform`, `applet_path`) VALUES (3968, '活动记录', 2008, 10, 'activityRecord', 'NursingManagement/activityRecord/index', NULL, 1, 0, 'C', '0', '0', NULL, 'color', NULL, 'admin', '2025-05-07 10:59:48', '', NULL, '', '100', NULL);

UPDATE `ry-cloud`.`sys_dict_data` SET `dict_sort` = 1, `dict_label` = '请假中', `dict_value` = '0', `dict_type` = 'custom_live_leave_state', `css_class` = NULL, `list_class` = 'default', `is_default` = 'N', `status` = '0', `create_by` = 'admin', `create_time` = '2024-12-05 14:14:41', `update_by` = 'admin', `update_time` = '2025-05-07 20:37:43', `remark` = NULL WHERE `dict_code` = 1502;
UPDATE `ry-cloud`.`sys_dict_data` SET `dict_sort` = 2, `dict_label` = '已销假，等待后勤确认', `dict_value` = '1', `dict_type` = 'custom_live_leave_state', `css_class` = NULL, `list_class` = 'default', `is_default` = 'N', `status` = '0', `create_by` = 'admin', `create_time` = '2024-12-05 14:14:48', `update_by` = 'admin', `update_time` = '2025-05-07 20:37:53', `remark` = NULL WHERE `dict_code` = 1503;
UPDATE `ry-cloud`.`sys_dict_data` SET `dict_sort` = 4, `dict_label` = '超时未归', `dict_value` = '3', `dict_type` = 'custom_live_leave_state', `css_class` = NULL, `list_class` = 'default', `is_default` = 'N', `status` = '0', `create_by` = 'admin', `create_time` = '2024-12-05 14:14:55', `update_by` = 'admin', `update_time` = '2025-05-07 20:38:09', `remark` = NULL WHERE `dict_code` = 1504;
INSERT INTO `ry-cloud`.`sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1614, 3, '已销假，后勤已确认', '2', 'custom_live_leave_state', NULL, 'default', 'N', '0', 'admin', '2025-05-07 20:37:31', '', NULL, NULL);

INSERT INTO `ry-cloud`.`sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `applet_icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `platform`, `applet_path`) VALUES (3970, '请假管理', 2036, 7, 'leaveManagement', 'dietManage/leaveManagement/index', NULL, 1, 0, 'C', '0', '0', NULL, 'chart', NULL, 'admin', '2025-05-08 09:02:12', '', NULL, '', '100', NULL);
UPDATE `ry-cloud`.`sys_menu` SET `menu_name` = '费用明细（老）', `parent_id` = 2012, `order_num` = 5, `path` = 'costDetail', `component` = 'costManage/costDetail/index', `query` = NULL, `is_frame` = 1, `is_cache` = 0, `menu_type` = 'C', `visible` = '1', `status` = '0', `perms` = '', `icon` = 'druid', `applet_icon` = NULL, `create_by` = 'admin', `create_time` = '2022-03-30 18:14:15', `update_by` = 'admin', `update_time` = '2025-05-08 09:24:40', `remark` = '', `platform` = '100', `applet_path` = NULL WHERE `menu_id` = 2020;

INSERT INTO `ry-cloud`.`sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `applet_icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `platform`, `applet_path`) VALUES (3969, '护工查房记录', 2008, 11, 'caregiverRoundingRecord', 'NursingManagement/caregiverRoundingRecord/index', '', 1, 0, 'C', '0', '0', '', 'druid', NULL, 'admin', '2025-05-07 11:27:10', 'admin', '2025-05-07 11:31:16', '', '100', NULL);
INSERT INTO `ry-cloud`.`sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `applet_icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `platform`, `applet_path`) VALUES (3968, '活动记录', 2008, 10, 'activityRecord', 'NursingManagement/activityRecord/index', NULL, 1, 0, 'C', '0', '0', NULL, 'color', NULL, 'admin', '2025-05-07 10:59:48', '', NULL, '', '100', NULL);

UPDATE `ry-cloud`.`sys_dict_data` SET `dict_sort` = 0, `dict_label` = '审核中', `dict_value` = '0', `dict_type` = 'common_audit_state', `css_class` = NULL, `list_class` = 'default', `is_default` = 'N', `status` = '0', `create_by` = 'admin', `create_time` = '2024-12-05 14:15:34', `update_by` = 'admin', `update_time` = '2025-05-08 16:50:52', `remark` = NULL WHERE `dict_code` = 1505;
