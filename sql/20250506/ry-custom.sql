ALTER TABLE `ry-custom`.`t_care_project_base_info`
    ADD COLUMN `service_type` bigint NULL COMMENT '服务类型，关联表：t_service_type' AFTER `id`;

ALTER TABLE `ry-custom`.`t_service_project` COMMENT = '选购服务';

ALTER TABLE `ry-custom`.`t_service_project`
    MODIFY COLUMN `service_type` bigint NULL DEFAULT NULL COMMENT '服务类型，关联表：t_service_type' AFTER `id`;

ALTER TABLE `ry-custom`.`t_service_project`
    MODIFY COLUMN `service_project` bigint NULL DEFAULT NULL COMMENT '服务项目id，关联表：t_care_project_base_info' AFTER `service_type`;

ALTER TABLE `ry-custom`.`t_service_project`
    ADD COLUMN `consumable` varchar(255) NULL COMMENT '耗材' AFTER `service_project`;

ALTER TABLE `ry-custom`.`t_live_leave_records`
    ADD COLUMN `returning_name` varchar(64) NULL COMMENT '送归人员姓名' AFTER `companion_phone`,
    ADD COLUMN `returning_type` varchar(4) NULL COMMENT '送归人员类型，字典：custom_leave_applicant_type；1：本人，2：亲属' AFTER `returning_name`;

ALTER TABLE `ry-custom`.`t_live_leave_records`
    ADD COLUMN `meal_days` int NULL COMMENT '用餐天数' AFTER `is_free_meal`,
    ADD COLUMN `avoid_meal_days` int NULL COMMENT '免除餐费天数' AFTER `meal_days`,
    ADD COLUMN `is_free_care` varchar(4) NULL COMMENT '是否免除护理费，字典：custom_leave_is_free_meal；1：否，1：是' AFTER `avoid_meal_days`,
    ADD COLUMN `avoid_care_days` int NULL COMMENT '免除护理费天数' AFTER `is_free_care`;

ALTER TABLE `ry-custom`.`t_live_leave_records`
    MODIFY COLUMN `state` varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '状态字典：custom_live_leave_state；状态(0请假中,1已销假，等待后勤确认,2已销假，后勤已确认,3超时未归)' AFTER `leave_reason`;

ALTER TABLE `ry-custom`.`t_live_leave_records`
    DROP COLUMN `leave_type`;

ALTER TABLE `ry-custom`.`t_live_leave_records`
    MODIFY COLUMN `id` bigint NOT NULL AUTO_INCREMENT FIRST;

ALTER TABLE `ry-custom`.`t_payment_record`
    ADD COLUMN `refund_cost` decimal(12, 2) NULL COMMENT '退费合计' AFTER `leave_dates`;

ALTER TABLE `ry-custom`.`t_payment_record`
    ADD COLUMN `offer_cost` decimal(12, 2) NULL COMMENT '缴纳合计' AFTER `refund_cost`;
