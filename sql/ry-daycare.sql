/*
 Navicat Premium Data Transfer

 Source Server         : zmd-test-root
 Source Server Type    : MySQL
 Source Server Version : 80024
 Source Host           : localhost:23307
 Source Schema         : ry-daycare

 Target Server Type    : MySQL
 Target Server Version : 80024
 File Encoding         : 65001

 Date: 18/03/2025 17:03:10
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for t_daycare_balance_info
-- ----------------------------
DROP TABLE IF EXISTS `t_daycare_balance_info`;
CREATE TABLE `t_daycare_balance_info`  (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `user_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '用户id',
  `last_amount` decimal(10, 4) NULL DEFAULT NULL COMMENT '上次余额',
  `amount` decimal(10, 4) NULL DEFAULT NULL COMMENT '余额',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建日期',
  `create_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '创建人员',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `update_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '修改人员',
  `del_flag` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT '0' COMMENT '逻辑删除标记（0：显示；1：隐藏',
  `remark` varchar(1000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '余额信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_daycare_balance_info
-- ----------------------------
INSERT INTO `t_daycare_balance_info` VALUES ('0b6ffcd730eb410ea76a3d0bb667277f', '3d5ca875cef047ad9f594ab703d5cd19', 0.0000, 1000.0000, '2022-09-13 16:09:40', '1', NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_balance_info` VALUES ('0d7e83abbafc4dfc945ca7754c5dbaf2', 'f9eb406ef62c478caffcd907f585f6e6', 6290.0000, 0.0000, '2022-09-07 14:35:34', '1', '2022-09-09 11:49:10', '1', '0', NULL);
INSERT INTO `t_daycare_balance_info` VALUES ('3d3638f04da449bc92e18fbbc9a2d765', '7ab7f1b374c04cc5a49ce4840ad7333e', 0.0000, 100.0000, '2022-12-14 17:17:14', '118', NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_balance_info` VALUES ('8df59655885c47118f368ef5df2c04aa', '863367c90003459b909b45f0c450eaec', 126.0000, 0.0000, '2022-09-09 11:56:57', '1', '2022-09-13 15:05:32', '1', '0', NULL);
INSERT INTO `t_daycare_balance_info` VALUES ('de518ad00a1943c69316a46cc6ea2493', '1231231', 0.0000, 2500.5600, '2022-09-02 14:50:31', '1', NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_balance_info` VALUES ('fe53f52567e34dfc957a1eeb4d653164', 'd78b91296a4b4c7794f31e8483c6cc48', 0.0000, 23.0000, '2022-09-13 17:47:40', '1', NULL, NULL, '0', NULL);

-- ----------------------------
-- Table structure for t_daycare_balance_records
-- ----------------------------
DROP TABLE IF EXISTS `t_daycare_balance_records`;
CREATE TABLE `t_daycare_balance_records`  (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `user_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '用户id',
  `last_amount` decimal(10, 4) NULL DEFAULT NULL COMMENT '上次余额',
  `changed_amount` decimal(10, 4) NULL DEFAULT NULL COMMENT '变动金额',
  `amount` decimal(10, 4) NULL DEFAULT NULL COMMENT '余额',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建日期',
  `create_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '创建人员',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `update_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '修改人员',
  `del_flag` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT '0' COMMENT '逻辑删除标记（0：显示；1：隐藏',
  `remark` varchar(1000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '备注',
  `changed_type` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '变动类型（1 充值 2 扣款 3退款）',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '余额记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_daycare_balance_records
-- ----------------------------
INSERT INTO `t_daycare_balance_records` VALUES ('05847646069642329e69b65da7064ed6', '863367c90003459b909b45f0c450eaec', 0.0000, 500.0000, 500.0000, '2022-09-09 11:56:57', '1', NULL, NULL, '0', NULL, '0');
INSERT INTO `t_daycare_balance_records` VALUES ('15d422268afe4c888610103afbe3fb61', '863367c90003459b909b45f0c450eaec', 126.0000, 126.0000, 0.0000, '2022-09-13 15:05:32', '1', '2022-09-13 15:05:32', '1', '0', NULL, '2');
INSERT INTO `t_daycare_balance_records` VALUES ('227fc12e59734ecfbd3ca2aebc015668', '863367c90003459b909b45f0c450eaec', 2282.0000, 500.0000, 2782.0000, '2022-09-13 15:05:15', '1', '2022-09-13 15:05:15', '1', '0', NULL, '0');
INSERT INTO `t_daycare_balance_records` VALUES ('2962e0805d3645dfa155a3a4c5603013', 'f9eb406ef62c478caffcd907f585f6e6', 8000.0000, 10.0000, 7990.0000, NULL, NULL, NULL, NULL, '0', NULL, '2');
INSERT INTO `t_daycare_balance_records` VALUES ('2d14472cd8334191bdd2b7065f23c420', 'f9eb406ef62c478caffcd907f585f6e6', 7990.0000, 100.0000, 7890.0000, NULL, NULL, NULL, NULL, '0', NULL, '2');
INSERT INTO `t_daycare_balance_records` VALUES ('33901b772e154e96925fb6d17f7d5340', 'f9eb406ef62c478caffcd907f585f6e6', 7890.0000, 1600.0000, 6290.0000, '2022-09-09 11:49:10', '1', '2022-09-09 11:49:10', '1', '0', NULL, '1');
INSERT INTO `t_daycare_balance_records` VALUES ('6945ba64e18f4f2e8e59f9d6608d4dd4', '863367c90003459b909b45f0c450eaec', 2782.0000, 2656.0000, 126.0000, '2022-09-13 15:05:32', '1', '2022-09-13 15:05:32', '1', '0', NULL, '1');
INSERT INTO `t_daycare_balance_records` VALUES ('7a66abf754f74ddbb7022f0c7d2034b6', '7ab7f1b374c04cc5a49ce4840ad7333e', 0.0000, 100.0000, 100.0000, '2022-12-14 17:17:14', '118', NULL, NULL, '0', NULL, '0');
INSERT INTO `t_daycare_balance_records` VALUES ('8c0edb6859a14a5898b739bf88f9db69', '3d5ca875cef047ad9f594ab703d5cd19', 0.0000, 1000.0000, 1000.0000, '2022-09-13 16:09:40', '1', NULL, NULL, '0', NULL, '0');
INSERT INTO `t_daycare_balance_records` VALUES ('8c4a33255b3d48719e91cc9c3dfc901d', 'f9eb406ef62c478caffcd907f585f6e6', 0.0000, 8000.0000, 8000.0000, '2022-09-07 14:35:34', '1', NULL, NULL, '0', NULL, '0');
INSERT INTO `t_daycare_balance_records` VALUES ('9ad654d92be44f4fad614a977d15fc74', '863367c90003459b909b45f0c450eaec', 500.0000, 10000.0000, 10500.0000, '2022-09-13 14:49:58', '1', '2022-09-13 14:49:57', '1', '0', NULL, '0');
INSERT INTO `t_daycare_balance_records` VALUES ('a6fe3cc542614258b55b8dd93d6c5dbf', 'd78b91296a4b4c7794f31e8483c6cc48', 0.0000, 23.0000, 23.0000, '2022-09-13 17:47:40', '1', NULL, NULL, '0', NULL, '0');
INSERT INTO `t_daycare_balance_records` VALUES ('b822a82ab16445a38ab891a00fc352fb', 'f9eb406ef62c478caffcd907f585f6e6', 6290.0000, 6290.0000, 0.0000, '2022-09-09 11:49:10', '1', '2022-09-09 11:49:10', '1', '0', NULL, '2');
INSERT INTO `t_daycare_balance_records` VALUES ('f315740d98e14cabb9f7382e74775f7b', '1231231', 0.0000, 2500.5600, 2500.5600, '2022-09-02 14:50:31', '1', NULL, NULL, '0', NULL, '0');
INSERT INTO `t_daycare_balance_records` VALUES ('f3af28fcfa6f48f09df40db1973201cc', '863367c90003459b909b45f0c450eaec', 10500.0000, 8218.0000, 2282.0000, '2022-09-13 14:50:06', '1', '2022-09-13 14:50:06', '1', '0', NULL, '1');

-- ----------------------------
-- Table structure for t_daycare_bed_base_info
-- ----------------------------
DROP TABLE IF EXISTS `t_daycare_bed_base_info`;
CREATE TABLE `t_daycare_bed_base_info`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `bed_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '床位名称',
  `bed_num` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '床位编号',
  `bed_state` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '床位状态(入住，空闲，停用)',
  `care_index` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '护工关联表id',
  `fee_index` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '床位费用版本表id',
  `live_records_index` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '入住记录表关联id',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建日期',
  `create_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '创建人员',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `update_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '修改人员',
  `del_flag` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT '0' COMMENT '逻辑删除标记（0：显示；1：隐藏',
  `remark` varchar(1000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '备注',
  `room_id` int NULL DEFAULT NULL COMMENT '房间id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 13 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '日间照料-床位基础信息' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_daycare_bed_base_info
-- ----------------------------
INSERT INTO `t_daycare_bed_base_info` VALUES (1, '10011号床位', NULL, '1', NULL, NULL, NULL, '2022-09-06 15:35:57', NULL, NULL, NULL, '0', NULL, 5);
INSERT INTO `t_daycare_bed_base_info` VALUES (2, '10021号床', NULL, '1', NULL, NULL, NULL, '2022-09-06 15:36:21', NULL, '2022-09-06 15:36:30', NULL, '0', NULL, 6);
INSERT INTO `t_daycare_bed_base_info` VALUES (3, '20011号床', NULL, '1', NULL, NULL, NULL, '2022-09-06 15:36:42', NULL, NULL, NULL, '0', NULL, 7);
INSERT INTO `t_daycare_bed_base_info` VALUES (4, '20021号床', NULL, '1', NULL, NULL, NULL, '2022-09-06 15:37:05', NULL, NULL, NULL, '0', NULL, 8);
INSERT INTO `t_daycare_bed_base_info` VALUES (5, '20012号床', NULL, '1', NULL, NULL, NULL, '2022-09-06 15:37:14', NULL, NULL, NULL, '0', NULL, 7);
INSERT INTO `t_daycare_bed_base_info` VALUES (6, '20022号床', NULL, '1', NULL, NULL, NULL, '2022-09-06 15:37:22', NULL, NULL, NULL, '0', NULL, 8);
INSERT INTO `t_daycare_bed_base_info` VALUES (7, '10011号床', NULL, '1', NULL, NULL, NULL, '2022-09-06 15:37:32', NULL, NULL, NULL, '0', NULL, 10);
INSERT INTO `t_daycare_bed_base_info` VALUES (8, '10012号床', NULL, '1', NULL, NULL, NULL, '2022-09-06 15:37:41', NULL, NULL, NULL, '0', NULL, 10);
INSERT INTO `t_daycare_bed_base_info` VALUES (9, '310101', NULL, '1', NULL, NULL, NULL, '2022-09-13 16:44:28', NULL, NULL, NULL, '0', NULL, 14);
INSERT INTO `t_daycare_bed_base_info` VALUES (10, '310102', NULL, '1', NULL, NULL, NULL, '2022-09-13 16:44:34', NULL, NULL, NULL, '0', NULL, 14);
INSERT INTO `t_daycare_bed_base_info` VALUES (11, '310103', NULL, '1', NULL, NULL, NULL, '2022-09-13 16:44:39', NULL, NULL, NULL, '1', NULL, 14);
INSERT INTO `t_daycare_bed_base_info` VALUES (12, '100202', NULL, '1', NULL, NULL, NULL, '2022-09-14 15:16:47', NULL, NULL, NULL, '0', NULL, 6);

-- ----------------------------
-- Table structure for t_daycare_care_combo_service_items_base_info
-- ----------------------------
DROP TABLE IF EXISTS `t_daycare_care_combo_service_items_base_info`;
CREATE TABLE `t_daycare_care_combo_service_items_base_info`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `combo_id` int NULL DEFAULT NULL COMMENT '护理套餐id',
  `care_project_id` int NULL DEFAULT NULL COMMENT '护理项目id',
  `cycle` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '周期',
  `frequency` int NULL DEFAULT NULL COMMENT '频次',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建日期',
  `create_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '创建人员',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `update_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '修改人员',
  `del_flag` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT '0' COMMENT '逻辑删除标记（0：显示；1：隐藏',
  `remark` varchar(1000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 18 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '护理套餐中的服务项' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_daycare_care_combo_service_items_base_info
-- ----------------------------
INSERT INTO `t_daycare_care_combo_service_items_base_info` VALUES (1, 1, 1, '1', 2, '2022-04-09 15:34:31', '', NULL, '', '0', '');
INSERT INTO `t_daycare_care_combo_service_items_base_info` VALUES (2, 6, 1, '1', 1, '2022-04-09 15:34:46', '', '2022-04-14 17:25:39', '', '1', '');
INSERT INTO `t_daycare_care_combo_service_items_base_info` VALUES (3, 6, 2, '0', 2, '2022-04-14 17:12:42', NULL, '2022-04-14 17:25:17', NULL, '1', NULL);
INSERT INTO `t_daycare_care_combo_service_items_base_info` VALUES (4, 10, 3, '0', 1, '2022-04-15 09:07:55', NULL, NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_care_combo_service_items_base_info` VALUES (5, 3, 2, '0', 3, '2022-04-19 11:20:43', NULL, NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_care_combo_service_items_base_info` VALUES (6, 2, 2, '0', 2, '2022-09-06 15:58:40', NULL, NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_care_combo_service_items_base_info` VALUES (7, 2, 1, '1', 7, '2022-09-06 15:58:54', NULL, NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_care_combo_service_items_base_info` VALUES (8, 3, 2, '0', 2, '2022-09-06 15:59:08', NULL, NULL, NULL, '1', NULL);
INSERT INTO `t_daycare_care_combo_service_items_base_info` VALUES (9, 3, 4, '1', 7, '2022-09-06 15:59:37', NULL, NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_care_combo_service_items_base_info` VALUES (10, 2, 7, '0', 11, '2022-09-07 14:50:58', NULL, NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_care_combo_service_items_base_info` VALUES (11, 5, 4, '1', 7, '2022-09-14 09:27:55', NULL, NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_care_combo_service_items_base_info` VALUES (12, 6, 2, '0', 3, '2022-09-14 09:47:19', NULL, NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_care_combo_service_items_base_info` VALUES (13, 6, 3, '0', 1, '2022-09-14 09:47:27', NULL, NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_care_combo_service_items_base_info` VALUES (14, 7, 1, '0', 1, '2022-09-15 10:51:20', NULL, NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_care_combo_service_items_base_info` VALUES (15, 7, 3, '0', 2, '2022-09-15 10:51:26', NULL, NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_care_combo_service_items_base_info` VALUES (16, 8, 2, '0', 1, '2022-09-15 10:54:11', NULL, NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_care_combo_service_items_base_info` VALUES (17, 8, 3, '0', 2, '2022-09-15 10:54:15', NULL, NULL, NULL, '0', NULL);

-- ----------------------------
-- Table structure for t_daycare_care_project_base_info
-- ----------------------------
DROP TABLE IF EXISTS `t_daycare_care_project_base_info`;
CREATE TABLE `t_daycare_care_project_base_info`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `care_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '护理项目名称',
  `status` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '状态',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建日期',
  `create_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '创建人员',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `update_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '修改人员',
  `del_flag` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT '0' COMMENT '逻辑删除标记（0：显示；1：隐藏',
  `remark` varchar(1000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 8 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '护理项目基础表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_daycare_care_project_base_info
-- ----------------------------
INSERT INTO `t_daycare_care_project_base_info` VALUES (1, '康复治疗', '', '2022-04-09 15:15:48', '', '2022-09-06 15:57:58', '', '0', '');
INSERT INTO `t_daycare_care_project_base_info` VALUES (2, '助餐', '', '2022-04-09 15:18:54', '', NULL, '', '0', '');
INSERT INTO `t_daycare_care_project_base_info` VALUES (3, '快乐治疗', '', '2022-04-14 10:37:05', '', NULL, '', '0', '');
INSERT INTO `t_daycare_care_project_base_info` VALUES (4, '轻奢治疗', '', '2022-04-14 10:37:32', '', '2022-04-14 10:37:59', '', '0', '');
INSERT INTO `t_daycare_care_project_base_info` VALUES (5, '测试项目', NULL, '2022-04-15 09:22:44', NULL, '2022-04-15 09:24:45', NULL, '1', NULL);
INSERT INTO `t_daycare_care_project_base_info` VALUES (6, '', '', '2022-09-05 09:22:42', '', NULL, '', '1', '');
INSERT INTO `t_daycare_care_project_base_info` VALUES (7, '洗脚', NULL, '2022-09-06 15:58:20', NULL, NULL, NULL, '0', NULL);

-- ----------------------------
-- Table structure for t_daycare_care_record_info
-- ----------------------------
DROP TABLE IF EXISTS `t_daycare_care_record_info`;
CREATE TABLE `t_daycare_care_record_info`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '老人id',
  `service_items_id` int NULL DEFAULT NULL COMMENT '服务项目id',
  `care_worker_id` int NULL DEFAULT NULL COMMENT '护工id',
  `begin_time` datetime NULL DEFAULT NULL COMMENT '开始时间',
  `end_time` datetime NULL DEFAULT NULL COMMENT '结束时间',
  `care_img` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '护理照片',
  `care_task_id` int NULL DEFAULT NULL COMMENT '护理任务id',
  `live_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '居住基础表id',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建日期',
  `create_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '创建人员',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `update_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '修改人员',
  `del_flag` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT '0' COMMENT '逻辑删除标记（0：显示；1：隐藏',
  `remark` text CHARACTER SET utf8 COLLATE utf8_bin NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '护理记录' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_daycare_care_record_info
-- ----------------------------

-- ----------------------------
-- Table structure for t_daycare_combo_base_info
-- ----------------------------
DROP TABLE IF EXISTS `t_daycare_combo_base_info`;
CREATE TABLE `t_daycare_combo_base_info`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `care_level` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '护理级别',
  `combo_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '套餐名称',
  `introduce` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '介绍',
  `state` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '状态',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建日期',
  `create_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '创建人员',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `update_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '修改人员',
  `del_flag` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT '0' COMMENT '逻辑删除标记（0：显示；1：隐藏',
  `remark` varchar(1000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 9 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '护理套餐' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_daycare_combo_base_info
-- ----------------------------
INSERT INTO `t_daycare_combo_base_info` VALUES (1, '', '', '', '', '2022-09-05 09:20:57', '', NULL, '', '1', '');
INSERT INTO `t_daycare_combo_base_info` VALUES (2, '0', '一级护理标准套餐', '一级护理标准', NULL, '2022-09-06 15:51:33', NULL, '2022-09-06 15:52:06', NULL, '0', NULL);
INSERT INTO `t_daycare_combo_base_info` VALUES (3, '1', '二级护理标准套餐', '二级护理标准', NULL, '2022-09-06 15:52:28', NULL, '2022-09-06 15:52:31', NULL, '0', NULL);
INSERT INTO `t_daycare_combo_base_info` VALUES (4, '2', '三级护理标准套餐', '', NULL, '2022-09-13 17:14:37', NULL, NULL, NULL, '1', NULL);
INSERT INTO `t_daycare_combo_base_info` VALUES (5, '2', '三级护理标准套餐', '三级护理标准', NULL, '2022-09-14 09:25:51', NULL, NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_combo_base_info` VALUES (6, '3', '特级护理标准套餐', '特级护理标准', NULL, '2022-09-14 09:46:50', NULL, NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_combo_base_info` VALUES (7, '1', '二级护理豪华套餐', '1231', NULL, '2022-09-14 15:33:48', NULL, '2022-09-15 10:51:12', NULL, '0', NULL);
INSERT INTO `t_daycare_combo_base_info` VALUES (8, '0', '123', '', NULL, '2022-09-15 10:53:49', NULL, NULL, NULL, '0', NULL);

-- ----------------------------
-- Table structure for t_daycare_consumption_registration
-- ----------------------------
DROP TABLE IF EXISTS `t_daycare_consumption_registration`;
CREATE TABLE `t_daycare_consumption_registration`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT 'id',
  `user_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '老人id',
  `user_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '老人名称',
  `consume_content` varchar(2000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '消费内容',
  `consume_date` datetime NULL DEFAULT NULL COMMENT '消费日期',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建日期',
  `create_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '创建人员',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `update_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '修改人员',
  `del_flag` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT '0' COMMENT '逻辑删除标记（0：显示；1：隐藏',
  `remark` varchar(1000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '备注',
  `last_amount` decimal(10, 2) NULL DEFAULT NULL COMMENT '上次余额',
  `changed_amount` decimal(10, 2) NULL DEFAULT NULL COMMENT '变动金额',
  `amount` decimal(10, 2) NULL DEFAULT NULL COMMENT '余额',
  `live_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '居住id',
  `settlement_flag` tinyint NULL DEFAULT NULL COMMENT '结算状态（0否1是）',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 8 CHARACTER SET = utf8 COLLATE = utf8_bin COMMENT = '老人消费登记' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_daycare_consumption_registration
-- ----------------------------
INSERT INTO `t_daycare_consumption_registration` VALUES (4, '863367c90003459b909b45f0c450eaec', '于传蛟', '胡辣汤', '2022-08-10 00:00:00', '2022-09-13 14:10:45', '1', NULL, NULL, '0', '', NULL, 20.00, NULL, '97f1587037284e01888984bb42b39144', 0);
INSERT INTO `t_daycare_consumption_registration` VALUES (5, '863367c90003459b909b45f0c450eaec', '于传蛟', '按摩', '2022-08-25 00:00:00', '2022-09-13 14:16:24', '1', NULL, NULL, '0', '', NULL, 998.00, NULL, '97f1587037284e01888984bb42b39144', 0);
INSERT INTO `t_daycare_consumption_registration` VALUES (6, '863367c90003459b909b45f0c450eaec', '于传蛟', '测试', '2022-09-13 00:00:00', '2022-09-13 14:22:42', '1', NULL, NULL, '0', '', NULL, 56.00, NULL, '97f1587037284e01888984bb42b39144', 0);
INSERT INTO `t_daycare_consumption_registration` VALUES (7, 'f9eb406ef62c478caffcd907f585f6e6', '张三', '胡辣汤', '2022-09-13 17:23:04', '2022-09-13 17:23:10', '1', NULL, NULL, '0', '', NULL, 5.00, NULL, '770a5cc5f9bb4aa98523daef8a25faa2', 0);

-- ----------------------------
-- Table structure for t_daycare_elderly_people_family_info
-- ----------------------------
DROP TABLE IF EXISTS `t_daycare_elderly_people_family_info`;
CREATE TABLE `t_daycare_elderly_people_family_info`  (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT 'id',
  `user_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '老人基础信息id',
  `name` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '家属姓名',
  `phone` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '手机号',
  `relation` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '关系',
  `live_with_status` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '是否与老人同住',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建日期',
  `create_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '创建人员',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `update_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '修改人员',
  `del_flag` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT '0' COMMENT '逻辑删除标记（0：显示；1：隐藏',
  `remark` varchar(1000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin COMMENT = '日间照料-老人家属信息' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_daycare_elderly_people_family_info
-- ----------------------------
INSERT INTO `t_daycare_elderly_people_family_info` VALUES ('486fb73b19e14dbeb3acc8c35df246d7', '7ab7f1b374c04cc5a49ce4840ad7333e', '王成放', '18322066658', '5', '1', '2022-12-14 17:12:58', '118', NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_elderly_people_family_info` VALUES ('7526e2e599d1478bbe621e7977c37f46', 'f9eb406ef62c478caffcd907f585f6e6', '赵四', '18338908999', '6', '1', '2022-09-06 14:21:38', '1', '2022-09-06 14:21:43', '1', '0', NULL);

-- ----------------------------
-- Table structure for t_daycare_elderly_people_health_management
-- ----------------------------
DROP TABLE IF EXISTS `t_daycare_elderly_people_health_management`;
CREATE TABLE `t_daycare_elderly_people_health_management`  (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT 'id',
  `user_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '老人基础信息id',
  `chronic_diseases` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '慢性病',
  `incapacitation_condition` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '失能情况',
  `blood_type` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '血型',
  `disability_situation` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '残疾情况',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建日期',
  `create_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '创建人员',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `update_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '修改人员',
  `del_flag` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT '0' COMMENT '逻辑删除标记（0：显示；1：隐藏',
  `remark` varchar(1000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin COMMENT = '日间照料-老人健康管理信息' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_daycare_elderly_people_health_management
-- ----------------------------

-- ----------------------------
-- Table structure for t_daycare_elderly_people_info
-- ----------------------------
DROP TABLE IF EXISTS `t_daycare_elderly_people_info`;
CREATE TABLE `t_daycare_elderly_people_info`  (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT 'id',
  `name` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '姓名',
  `sex` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '性别',
  `id_card_num` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '身份证号',
  `phone` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '手机号',
  `date_birth` datetime NULL DEFAULT NULL COMMENT '出生日期',
  `age` int NULL DEFAULT NULL COMMENT '年龄',
  `nation` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '民族',
  `marriage_status` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '婚姻情况',
  `living_situation` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '居住情况',
  `home_address` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '家庭住址',
  `emergency_contact_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '紧急联系人姓名',
  `emergency_contact_phone` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '紧急联系人手机号',
  `relation` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '关系',
  `economic_sources` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '经济来源',
  `monthly_income` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '月收入',
  `social_security_no` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '社保号',
  `status` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT '1' COMMENT '入住状态',
  `img` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '头像照片',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建日期',
  `create_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '创建人员',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `update_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '修改人员',
  `del_flag` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT '0' COMMENT '逻辑删除标记（0：显示；1：隐藏',
  `remark` varchar(1000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin COMMENT = '日间照料-老人基础信息' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_daycare_elderly_people_info
-- ----------------------------
INSERT INTO `t_daycare_elderly_people_info` VALUES ('3cf9c491b5774476b297c3544cacffda', '孙春伟', '0', '371521198410264915', '15836522145', '1984-10-26 00:00:00', 37, '1', '1', '0', '郑州市', '', '', '', '', '', '', '2', '', '2022-09-09 09:41:59', '1', '2022-09-13 14:02:05', '1', '0', NULL);
INSERT INTO `t_daycare_elderly_people_info` VALUES ('3d5ca875cef047ad9f594ab703d5cd18', '玛丽', '0', '350321096003237001', '18556223522', '0960-03-23 00:00:00', 1062, '1', '0', '0', '石更社区', '', '', '', '', '', '', '1', '', '2022-09-02 10:36:20', '1', '2022-09-08 09:20:59', '1', '0', NULL);
INSERT INTO `t_daycare_elderly_people_info` VALUES ('3d5ca875cef047ad9f594ab703d5cd19', '王德', '0', '410329200108259658', '18556223522', '2022-09-06 00:00:00', 20, '1', '0', '0', '石更社区', '', '', '', '', '', '', '0', '', '2022-09-02 10:36:20', '1', '2022-09-13 14:11:10', '1', '0', NULL);
INSERT INTO `t_daycare_elderly_people_info` VALUES ('7ab7f1b374c04cc5a49ce4840ad7333e', '王成发', '0', '372522197204184239', '16603448958', '1972-04-18 00:00:00', 50, '', '', '', '金水东路15号5栋20层162', '', '', '', '', '', '', '0', '', '2022-12-14 17:12:32', '118', '2022-12-14 17:13:23', '118', '0', NULL);
INSERT INTO `t_daycare_elderly_people_info` VALUES ('863367c90003459b909b45f0c450eaec', '于传蛟', '0', '372522197002110418', '15936522145', '1970-02-11 00:00:00', 52, '1', '1', '0', '阳谷县侨润办事处五里庙村', '', '', '', '', '', '', '2', '', '2022-09-09 11:34:19', '1', '2022-09-15 14:16:45', '1', '0', NULL);
INSERT INTO `t_daycare_elderly_people_info` VALUES ('d78b91296a4b4c7794f31e8483c6cc48', '王蔷', '1', '410725199606281218', '15936577569', '1996-06-28 00:00:00', 26, '1', '1', '2', '郑州市金水区', '王丽', '15936522145', '2', '0', '8000', '', '0', '', '2022-09-09 09:16:48', '1', '2022-09-13 14:05:48', '1', '0', NULL);
INSERT INTO `t_daycare_elderly_people_info` VALUES ('f9eb406ef62c478caffcd907f585f6e6', '张三', '0', '410520199506123201', '18338908888', '1995-06-12 00:00:00', 27, '1', '0', '2', '金水区文化路192', '', '', '', '', '', '', '0', 'http://**************:23360/ruoyi-auth/statics/2022/09/06/1662445271155_20220906142111A001.jpeg', '2022-09-06 14:21:11', '1', '2022-09-13 16:18:18', '1', '0', NULL);

-- ----------------------------
-- Table structure for t_daycare_elderly_people_life_style
-- ----------------------------
DROP TABLE IF EXISTS `t_daycare_elderly_people_life_style`;
CREATE TABLE `t_daycare_elderly_people_life_style`  (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT 'id',
  `dietary_status` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '饮食情况',
  `taste_selection` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '口味选择',
  `eat_breakfast` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '是否吃早餐',
  `take_exercise` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '是否锻炼',
  `exercise_times_per_week` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '每周锻炼次数',
  `exercise_time` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '锻炼时间',
  `sleep_quality` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '睡眠质量',
  `sleep_time` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '睡眠时间',
  `stay_up_late_status` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '熬夜情况',
  `smoking_status` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '抽烟情况',
  `quit_smoking_years` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '戒烟年数',
  `number_cigarettes_smoked_daily` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '日吸烟量',
  `smoking_time` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '烟龄',
  `drinking_status` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '饮酒情况',
  `capacity_for_liquor` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '每次酒量',
  `drinking_time` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '酒龄',
  `drinking_types` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '饮酒种类',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建日期',
  `create_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '创建人员',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `update_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '修改人员',
  `del_flag` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT '0' COMMENT '逻辑删除标记（0：显示；1：隐藏',
  `remark` varchar(1000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '备注',
  `user_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '老人基础信息id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin COMMENT = '日间照料-老人生活方式信息' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_daycare_elderly_people_life_style
-- ----------------------------
INSERT INTO `t_daycare_elderly_people_life_style` VALUES ('4ceebc17e9ed436689d8ea4f1ada7ab4', '0', '1', NULL, '1', '2', '3', '1', '7', '1', '2', NULL, '5', '10', '1', '200', '12', '1', '2022-09-06 14:24:12', '1', NULL, NULL, '0', NULL, 'f9eb406ef62c478caffcd907f585f6e6');

-- ----------------------------
-- Table structure for t_daycare_elderly_people_medication_history
-- ----------------------------
DROP TABLE IF EXISTS `t_daycare_elderly_people_medication_history`;
CREATE TABLE `t_daycare_elderly_people_medication_history`  (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT 'id',
  `user_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '老人基础信息id',
  `disease_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '疾病名称',
  `medicine_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '药品名称',
  `metering_usage` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '计量用法',
  `begin_time` datetime NULL DEFAULT NULL COMMENT '用药开始时间',
  `end_time` datetime NULL DEFAULT NULL COMMENT '用药结束时间',
  `status` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '状态(在用，停用)',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建日期',
  `create_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '创建人员',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `update_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '修改人员',
  `del_flag` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT '0' COMMENT '逻辑删除标记（0：显示；1：隐藏',
  `remark` varchar(1000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin COMMENT = '日间照料-老人用药史信息' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_daycare_elderly_people_medication_history
-- ----------------------------
INSERT INTO `t_daycare_elderly_people_medication_history` VALUES ('e34bae712ee24fa0b04ede6c94c84d0f', '7ab7f1b374c04cc5a49ce4840ad7333e', '心脏病', '速效救心丸', '1粒/1次', NULL, NULL, '0', '2023-01-28 17:47:12', '1', NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_elderly_people_medication_history` VALUES ('fd2e9647c70d4f41821733f0f16a32bd', 'f9eb406ef62c478caffcd907f585f6e6', '高血压', '降压片', '一天三次，一次两片', NULL, NULL, '0', '2022-09-06 14:22:51', '1', '2022-09-06 14:22:55', '1', '0', NULL);

-- ----------------------------
-- Table structure for t_daycare_fee_base_info
-- ----------------------------
DROP TABLE IF EXISTS `t_daycare_fee_base_info`;
CREATE TABLE `t_daycare_fee_base_info`  (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `fee_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '费用名称',
  `fee_type` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '费用类型',
  `state` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '是否有效',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建日期',
  `create_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '创建人员',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `update_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '修改人员',
  `del_flag` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT '0' COMMENT '逻辑删除标记（0：显示；1：隐藏',
  `remark` varchar(1000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '费用基础表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_daycare_fee_base_info
-- ----------------------------
INSERT INTO `t_daycare_fee_base_info` VALUES ('be59c7bbd93545d28b5f1ec73637f0fd', '国庆过节费', '0', NULL, '2022-09-13 17:50:52', '1', '2022-09-13 17:51:00', '1', '0', NULL);

-- ----------------------------
-- Table structure for t_daycare_fee_bill_info
-- ----------------------------
DROP TABLE IF EXISTS `t_daycare_fee_bill_info`;
CREATE TABLE `t_daycare_fee_bill_info`  (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `live_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `user_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '老人id',
  `bill_month` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '账单月份（202204）',
  `billing_cycle` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '账单周期',
  `bill_details` varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '账单明细',
  `Amount_due` decimal(10, 0) NULL DEFAULT NULL COMMENT '应缴金额',
  `Paid_amount` decimal(10, 0) NULL DEFAULT NULL COMMENT '实缴金额',
  `bill_state` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '账单状态（未结算，未结清，已结清）',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建日期',
  `create_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '创建人员',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `update_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '修改人员',
  `del_flag` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT '0' COMMENT '逻辑删除标记（0：显示；1：隐藏',
  `remark` varchar(1000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '费用账单周期表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_daycare_fee_bill_info
-- ----------------------------
INSERT INTO `t_daycare_fee_bill_info` VALUES ('1c803525477a49179e96d41c071a7414', '97f1587037284e01888984bb42b39144', '863367c90003459b909b45f0c450eaec', '202208', '20220801-20220831', '[{\"feeName\":\"床位费（长住）\",\"feeCycle\":\"20220801-20220831\",\"dueAmount\":6200},{\"feeName\":\"套餐费（长住）\",\"feeCycle\":\"20220801-20220831\",\"dueAmount\":1000},{\"feeName\":\"消费登记（胡辣汤）\",\"feeCycle\":\"2022-08-10 00:00:00\",\"dueAmount\":20},{\"feeName\":\"消费登记（按摩）\",\"feeCycle\":\"2022-08-25 00:00:00\",\"dueAmount\":998}]', 8218, 8218, '2', '2022-09-13 14:41:35', '1', '2022-09-13 14:50:06', '1', '0', '定时生成账单，生成时间:2022-09-13 14:41:35');
INSERT INTO `t_daycare_fee_bill_info` VALUES ('4abfb8afe99548e6b4d5084905276d99', '2bdecc4a9aa749cb9ab3ee97d48b1879', '3cf9c491b5774476b297c3544cacffda', '202208', '20220801-20220831', '[{\"feeName\":\"床位费（长住）\",\"feeCycle\":\"20220831-20220831\",\"dueAmount\":200},{\"feeName\":\"套餐费（长住）\",\"feeCycle\":\"20220831-20220831\",\"dueAmount\":32.26}]', 232, 0, '0', '2022-09-13 14:41:35', '1', NULL, NULL, '0', '定时生成账单，生成时间:2022-09-13 14:41:35');
INSERT INTO `t_daycare_fee_bill_info` VALUES ('6493e946f9474e99846af386cb39acf2', '97f1587037284e01888984bb42b39144', '863367c90003459b909b45f0c450eaec', '202209', '20220901-20220913', '[{\"feeName\":\"床位费（长住）\",\"feeCycle\":\"20220901-20220913\",\"dueAmount\":2166.67},{\"feeName\":\"套餐费（长住）\",\"feeCycle\":\"20220901-20220913\",\"dueAmount\":433.33},{\"feeName\":\"消费登记（测试）\",\"feeCycle\":\"2022-09-13 00:00:00\",\"dueAmount\":56}]', 2656, 2656, '2', '2022-09-13 15:05:32', '1', '2022-09-13 15:05:32', '1', '0', '退住生成账单');
INSERT INTO `t_daycare_fee_bill_info` VALUES ('6724f666f2424f00b13350ce0813d54f', '6a1efdc3b4a347b59127f08cc9ca4b5d', 'd78b91296a4b4c7794f31e8483c6cc48', '202208', '20220801-20220831', '[{\"feeName\":\"床位费（长住）\",\"feeCycle\":\"20220831-20220831\",\"dueAmount\":70},{\"feeName\":\"套餐费（长住）\",\"feeCycle\":\"20220831-20220831\",\"dueAmount\":32.26}]', 102, 0, '0', '2022-09-13 14:41:35', '1', NULL, NULL, '0', '定时生成账单，生成时间:2022-09-13 14:41:34');
INSERT INTO `t_daycare_fee_bill_info` VALUES ('d8d2ef09698c40fa982cd7f3c0eb08d8', 'a4981f0b9cf04509a3fc048cdcd14cf1', '3d5ca875cef047ad9f594ab703d5cd19', '202208', '20220801-20220831', '[{\"feeName\":\"床位费（短住）\",\"feeCycle\":\"20220831-20220831\",\"dueAmount\":70},{\"feeName\":\"套餐费（短住）\",\"feeCycle\":\"20220831-20220831\",\"dueAmount\":40}]', 110, 0, '0', '2022-09-13 14:41:35', '1', NULL, NULL, '0', '定时生成账单，生成时间:2022-09-13 14:41:34');

-- ----------------------------
-- Table structure for t_daycare_fee_combo_info
-- ----------------------------
DROP TABLE IF EXISTS `t_daycare_fee_combo_info`;
CREATE TABLE `t_daycare_fee_combo_info`  (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `combo_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '套餐id',
  `version` int NULL DEFAULT NULL COMMENT '版本号',
  `month_details` varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '月费用标准明细',
  `day_details` varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '日费用标准明细',
  `month_amount` decimal(10, 0) NULL DEFAULT NULL COMMENT '月费用合计',
  `day_amount` decimal(10, 0) NULL DEFAULT NULL COMMENT '日费用合计',
  `state` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '状态（在用，废止）',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建日期',
  `create_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '创建人员',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `update_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '修改人员',
  `del_flag` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT '0' COMMENT '逻辑删除标记（0：显示；1：隐藏',
  `remark` varchar(1000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '套餐费用表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_daycare_fee_combo_info
-- ----------------------------
INSERT INTO `t_daycare_fee_combo_info` VALUES ('0dc67def5f2b41708faea785208ead8c', '5', 1, '[{\"name\":\"清洁费\",\"value\":200}]', '[{\"name\":\"清洁费\",\"value\":200}]', 200, 200, '1', '2022-09-14 10:08:06', '1', '2022-09-15 10:52:24', '1', '0', NULL);
INSERT INTO `t_daycare_fee_combo_info` VALUES ('37f748c7df904b01879385ccaed67c13', '5', 2, '[{\"name\":\"清洁费\",\"value\":200},{\"name\":\"12\",\"value\":12}]', '[{\"name\":\"清洁费\",\"value\":200}]', 212, 200, '0', '2022-09-15 10:52:24', '1', NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_fee_combo_info` VALUES ('4e4efc7b11ef4b33b34c912072ae9d85', '2', 1, '[{\"name\":\"护理费\",\"value\":600},{\"name\":\"清洁费\",\"value\":300},{\"name\":\"物业费\",\"value\":100}]', '[{\"name\":\"护理费\",\"value\":20},{\"name\":\"清洁费\",\"value\":10},{\"name\":\"物业费\",\"value\":10}]', 1000, 40, '0', '2022-09-07 14:29:42', '1', NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_fee_combo_info` VALUES ('c5ed265fc5f648759e44bebc16ff5f91', '3', 1, '[{\"name\":\"优秀护理长住\",\"value\":200}]', '[{\"name\":\"优秀护理短住\",\"value\":250}]', 200, 250, '0', '2022-09-07 14:30:11', '1', NULL, NULL, '0', NULL);

-- ----------------------------
-- Table structure for t_daycare_fee_details
-- ----------------------------
DROP TABLE IF EXISTS `t_daycare_fee_details`;
CREATE TABLE `t_daycare_fee_details`  (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `invoice_num` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '单据编号',
  `invoice_type` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '单据类型(0 缴费 1退费)',
  `invoice_details` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '单据明细',
  `user_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '用户id',
  `live_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '入住id',
  `deduction_type` varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '扣费类型（足额扣除,部分扣除）',
  `before_balance` decimal(10, 0) NULL DEFAULT NULL COMMENT '变更前余额',
  `change_amount` decimal(10, 0) NULL DEFAULT NULL COMMENT '变动金额',
  `after_balance` decimal(10, 0) NULL DEFAULT NULL COMMENT '变更后余额',
  `operator_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '操作人id',
  `operator_type` varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '操作类型（0 自动扣费 1 手动）',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建日期',
  `create_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '创建人员',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `update_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '修改人员',
  `del_flag` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT '0' COMMENT '逻辑删除标记（0：显示；1：隐藏',
  `remark` varchar(1000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '费用明细表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_daycare_fee_details
-- ----------------------------
INSERT INTO `t_daycare_fee_details` VALUES ('3b7ad13357f14156a39bb12ce4431143', 'TF202209091149098589676', '1', NULL, 'f9eb406ef62c478caffcd907f585f6e6', 'b9e305689fa849ab840ea98e4f52574d', NULL, 6290, 6290, 0, NULL, NULL, '2022-09-09 11:49:10', '1', NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_fee_details` VALUES ('4261a22bcaeb492bae20d66a8f3fdbd2', 'ZD202209131450059669177', '0', '[{\"id\":\"1c803525477a49179e96d41c071a7414\",\"liveId\":\"97f1587037284e01888984bb42b39144\",\"userId\":\"863367c90003459b909b45f0c450eaec\",\"billMonth\":\"202208\",\"billingCycle\":\"20220801-20220831\",\"billDetails\":\"[{\\\"feeName\\\":\\\"床位费（长住）\\\",\\\"feeCycle\\\":\\\"20220801-20220831\\\",\\\"dueAmount\\\":6200},{\\\"feeName\\\":\\\"套餐费（长住）\\\",\\\"feeCycle\\\":\\\"20220801-20220831\\\",\\\"dueAmount\\\":1000},{\\\"feeName\\\":\\\"消费登记（胡辣汤）\\\",\\\"feeCycle\\\":\\\"2022-08-10 00:00:00\\\",\\\"dueAmount\\\":20},{\\\"feeName\\\":\\\"消费登记（按摩）\\\",\\\"feeCycle\\\":\\\"2022-08-25 00:00:00\\\",\\\"dueAmount\\\":998}]\",\"billDetailsList\":[{\"feeName\":\"床位费（长住）\",\"feeCycle\":\"20220801-20220831\",\"dueAmount\":6200},{\"feeName\":\"套餐费（长住）\",\"feeCycle\":\"20220801-20220831\",\"dueAmount\":1000},{\"feeName\":\"消费登记（胡辣汤）\",\"feeCycle\":\"2022-08-10 00:00:00\",\"dueAmount\":20},{\"feeName\":\"消费登记（按摩）\",\"feeCycle\":\"2022-08-25 00:00:00\",\"dueAmount\":998}],\"amountDue\":8218,\"paidAmount\":0,\"lackAmount\":8218,\"billState\":\"0\",\"delFlag\":\"0\",\"createBy\":\"1\",\"createTime\":1663051295000,\"remark\":\"定时生成账单，生成时间:2022-09-13 14:41:35\",\"params\":{}}]', '863367c90003459b909b45f0c450eaec', '97f1587037284e01888984bb42b39144', '0', 10500, 8218, 2282, '1', '1', '2022-09-13 14:50:06', NULL, NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_fee_details` VALUES ('7a4640ee04384026a229af8e8acf2044', 'ZD202209131505323259419', '0', '[{\"id\":\"6493e946f9474e99846af386cb39acf2\",\"liveId\":\"97f1587037284e01888984bb42b39144\",\"userId\":\"863367c90003459b909b45f0c450eaec\",\"billMonth\":\"202209\",\"billingCycle\":\"20220901-20220913\",\"billDetails\":\"[{\\\"feeName\\\":\\\"床位费（长住）\\\",\\\"feeCycle\\\":\\\"20220901-20220913\\\",\\\"dueAmount\\\":2166.67},{\\\"feeName\\\":\\\"套餐费（长住）\\\",\\\"feeCycle\\\":\\\"20220901-20220913\\\",\\\"dueAmount\\\":433.33},{\\\"feeName\\\":\\\"消费登记（测试）\\\",\\\"feeCycle\\\":\\\"2022-09-13 00:00:00\\\",\\\"dueAmount\\\":56}]\",\"billDetailsList\":[{\"feeName\":\"床位费（长住）\",\"feeCycle\":\"20220901-20220913\",\"dueAmount\":2166.67},{\"feeName\":\"套餐费（长住）\",\"feeCycle\":\"20220901-20220913\",\"dueAmount\":433.33},{\"feeName\":\"消费登记（测试）\",\"feeCycle\":\"2022-09-13 00:00:00\",\"dueAmount\":56}],\"amountDue\":2656,\"paidAmount\":0,\"lackAmount\":2656,\"billState\":\"0\",\"delFlag\":\"0\",\"createBy\":\"1\",\"createTime\":1663052732000,\"remark\":\"退住生成账单\",\"params\":{}}]', '863367c90003459b909b45f0c450eaec', '97f1587037284e01888984bb42b39144', '0', 2782, 2656, 126, '1', '1', '2022-09-13 15:05:32', NULL, NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_fee_details` VALUES ('d3eaf2354dfb456f9791478f9bdf1c81', 'ZD202209091149098079813', '0', '[{\"id\":\"01a92cf569614d54b332375949fe8d9f\",\"liveId\":\"b9e305689fa849ab840ea98e4f52574d\",\"userId\":\"f9eb406ef62c478caffcd907f585f6e6\",\"billMonth\":\"202209\",\"billingCycle\":\"20220901-20220909\",\"billDetails\":\"[{\\\"feeName\\\":\\\"床位费（长住）\\\",\\\"feeCycle\\\":\\\"20220901-20220909\\\",\\\"dueAmount\\\":1500},{\\\"feeName\\\":\\\"套餐费（长住）\\\",\\\"feeCycle\\\":\\\"20220907-20220909\\\",\\\"dueAmount\\\":100}]\",\"billDetailsList\":[{\"feeName\":\"床位费（长住）\",\"feeCycle\":\"20220901-20220909\",\"dueAmount\":1500},{\"feeName\":\"套餐费（长住）\",\"feeCycle\":\"20220907-20220909\",\"dueAmount\":100}],\"amountDue\":1600,\"paidAmount\":0,\"lackAmount\":1600,\"billState\":\"0\",\"delFlag\":\"0\",\"createBy\":\"1\",\"createTime\":1662695350000,\"remark\":\"退住生成账单\",\"params\":{}}]', 'f9eb406ef62c478caffcd907f585f6e6', 'b9e305689fa849ab840ea98e4f52574d', '0', 7890, 1600, 6290, '1', '1', '2022-09-09 11:49:10', NULL, NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_fee_details` VALUES ('e07ca826092b414d94c4bc36edc61b98', 'TF202209131505323989853', '1', NULL, '863367c90003459b909b45f0c450eaec', '97f1587037284e01888984bb42b39144', NULL, 126, 126, 0, NULL, NULL, '2022-09-13 15:05:32', '1', NULL, NULL, '0', NULL);

-- ----------------------------
-- Table structure for t_daycare_fee_pay_info
-- ----------------------------
DROP TABLE IF EXISTS `t_daycare_fee_pay_info`;
CREATE TABLE `t_daycare_fee_pay_info`  (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `pay_number` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '支付单号',
  `user_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '用户id',
  `pay_time` datetime NULL DEFAULT NULL COMMENT '支付时间',
  `pay_user_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '支付人id',
  `pay_user_type` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '支付人类型（本人，亲属）',
  `pay_type` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '支付方式（现金，刷卡，微信，支付宝）',
  `pay_amount` decimal(10, 4) NULL DEFAULT NULL COMMENT '支付金额',
  `begin_paid_amount` decimal(10, 4) NULL DEFAULT NULL COMMENT '支付前余额',
  `after_paid_amount` decimal(10, 4) NULL DEFAULT NULL COMMENT '支付后余额',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建日期',
  `create_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '创建人员',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `update_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '修改人员',
  `del_flag` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT '0' COMMENT '逻辑删除标记（0：显示；1：隐藏',
  `remark` varchar(1000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '费用支付记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_daycare_fee_pay_info
-- ----------------------------
INSERT INTO `t_daycare_fee_pay_info` VALUES ('0e86536f867343fbb9c1095918a44e24', 'JF202209071435344269702', 'f9eb406ef62c478caffcd907f585f6e6', NULL, NULL, NULL, '0', 8000.0000, 0.0000, 8000.0000, '2022-09-07 14:35:34', '1', NULL, NULL, '0', '');
INSERT INTO `t_daycare_fee_pay_info` VALUES ('26d35f1bafda4dcda3ef53fd7f4bba05', 'JF202212141717140675994', '7ab7f1b374c04cc5a49ce4840ad7333e', NULL, NULL, NULL, '0', 100.0000, 0.0000, 100.0000, '2022-12-14 17:17:14', '118', NULL, NULL, '0', '');
INSERT INTO `t_daycare_fee_pay_info` VALUES ('3cf9040e20db42078705d19e56c3c007', 'JF202209131747400769134', 'd78b91296a4b4c7794f31e8483c6cc48', NULL, NULL, NULL, '0', 23.0000, 0.0000, 23.0000, '2022-09-13 17:47:40', '1', NULL, NULL, '0', '');
INSERT INTO `t_daycare_fee_pay_info` VALUES ('50ba8554108545d4a2d29e922bd39d93', 'JF202209131505146159697', '863367c90003459b909b45f0c450eaec', NULL, NULL, NULL, '0', 500.0000, 2282.0000, 2782.0000, '2022-09-13 15:05:15', '1', NULL, NULL, '0', '');
INSERT INTO `t_daycare_fee_pay_info` VALUES ('6b1c13d9536f4c22a4890d555784b321', 'JF202209131449574499683', '863367c90003459b909b45f0c450eaec', NULL, NULL, NULL, '1', 10000.0000, 500.0000, 10500.0000, '2022-09-13 14:49:57', '1', NULL, NULL, '0', '');
INSERT INTO `t_daycare_fee_pay_info` VALUES ('dabacc3678a94e2aa93313df93226c6e', 'JF202209131609398549897', '3d5ca875cef047ad9f594ab703d5cd19', NULL, NULL, NULL, '1', 1000.0000, 0.0000, 1000.0000, '2022-09-13 16:09:40', '1', NULL, NULL, '0', '');
INSERT INTO `t_daycare_fee_pay_info` VALUES ('e1337ee58ba743f1ac86813d18254a05', 'JF202209091156567449346', '863367c90003459b909b45f0c450eaec', NULL, NULL, NULL, '0', 500.0000, 0.0000, 500.0000, '2022-09-09 11:56:57', '1', NULL, NULL, '0', '');

-- ----------------------------
-- Table structure for t_daycare_fee_staged_info
-- ----------------------------
DROP TABLE IF EXISTS `t_daycare_fee_staged_info`;
CREATE TABLE `t_daycare_fee_staged_info`  (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `fee_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '关联费用id',
  `version` int NULL DEFAULT NULL COMMENT '版本号',
  `billing_begin_date` date NULL DEFAULT NULL COMMENT '计费开始日期',
  `billing_end_date` date NULL DEFAULT NULL COMMENT '计费结束日期',
  `early_end_date` date NULL DEFAULT NULL COMMENT '提前终止日期',
  `billing_rule` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '计费规则',
  `fee_total` decimal(10, 0) NULL DEFAULT NULL COMMENT '费用合计',
  `state` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '状态（在用，停用）',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建日期',
  `create_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '创建人员',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `update_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '修改人员',
  `del_flag` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT '0' COMMENT '逻辑删除标记（0：显示；1：隐藏',
  `remark` varchar(1000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '阶段性费用表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_daycare_fee_staged_info
-- ----------------------------
INSERT INTO `t_daycare_fee_staged_info` VALUES ('161cdec9fc8f42dd92f2848768da54c0', 'be59c7bbd93545d28b5f1ec73637f0fd', 2, '2022-09-30', '2022-10-06', NULL, '1', 25, '0', '2022-09-13 17:51:00', '1', NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_fee_staged_info` VALUES ('d34432e2afc64df0a242165073485738', 'be59c7bbd93545d28b5f1ec73637f0fd', 1, '2022-09-30', '2022-10-06', NULL, '1', 20, '1', '2022-09-13 17:50:52', '1', '2022-09-13 17:51:00', '1', '0', NULL);

-- ----------------------------
-- Table structure for t_daycare_live_base_info
-- ----------------------------
DROP TABLE IF EXISTS `t_daycare_live_base_info`;
CREATE TABLE `t_daycare_live_base_info`  (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'id',
  `user_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '用户id',
  `live_type` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '入住类型',
  `live_date` date NULL DEFAULT NULL COMMENT '初始入住日期',
  `state` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '居住状态',
  `billing_date` date NULL DEFAULT NULL COMMENT '初始计费日期',
  `expired_date` date NULL DEFAULT NULL COMMENT '合同到期日期',
  `discounted_amount` decimal(10, 0) NULL DEFAULT NULL COMMENT '折扣金额',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建日期',
  `create_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '创建人员',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `update_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '修改人员',
  `del_flag` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT '0' COMMENT '逻辑删除标记（0：显示；1：隐藏',
  `remark` varchar(1000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '日间照料-居住信息基础表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_daycare_live_base_info
-- ----------------------------
INSERT INTO `t_daycare_live_base_info` VALUES ('2bdecc4a9aa749cb9ab3ee97d48b1879', '3cf9c491b5774476b297c3544cacffda', '0', '2022-08-31', '2', '2022-08-31', '2023-09-29', NULL, '2022-09-13 14:02:05', '1', '2022-09-13 17:54:55', '1', '0', NULL);
INSERT INTO `t_daycare_live_base_info` VALUES ('4594961115a848938020eebd9ea9d307', '7ab7f1b374c04cc5a49ce4840ad7333e', '0', '2022-12-14', '0', '2022-12-14', '2023-12-14', NULL, '2022-12-14 17:13:23', '118', NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_live_base_info` VALUES ('5ba9f63aba99475e9ab2423e92ddc899', '863367c90003459b909b45f0c450eaec', '0', '2022-09-15', '2', '2022-09-15', '2023-09-15', NULL, '2022-09-15 14:16:45', '1', NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_live_base_info` VALUES ('6a1efdc3b4a347b59127f08cc9ca4b5d', 'd78b91296a4b4c7794f31e8483c6cc48', '0', '2022-08-30', '0', '2022-08-30', '2023-08-30', NULL, '2022-09-13 14:05:48', '1', '2022-09-16 14:56:44', '1', '0', NULL);
INSERT INTO `t_daycare_live_base_info` VALUES ('770a5cc5f9bb4aa98523daef8a25faa2', 'f9eb406ef62c478caffcd907f585f6e6', '0', '2022-09-13', '2', '2022-09-14', '2022-10-15', NULL, '2022-09-13 16:18:18', '1', '2022-09-16 14:55:16', '1', '0', NULL);
INSERT INTO `t_daycare_live_base_info` VALUES ('97f1587037284e01888984bb42b39144', '863367c90003459b909b45f0c450eaec', '0', '2022-07-31', '3', '2022-07-31', '2023-09-29', NULL, '2022-09-13 14:00:43', '1', '2022-09-13 15:05:33', '1', '0', NULL);
INSERT INTO `t_daycare_live_base_info` VALUES ('a4981f0b9cf04509a3fc048cdcd14cf1', '3d5ca875cef047ad9f594ab703d5cd19', '1', '2022-08-31', '0', '2022-08-31', '2023-08-31', NULL, '2022-09-13 14:11:10', '1', '2022-09-15 13:40:23', '1', '0', NULL);

-- ----------------------------
-- Table structure for t_daycare_live_bed_records
-- ----------------------------
DROP TABLE IF EXISTS `t_daycare_live_bed_records`;
CREATE TABLE `t_daycare_live_bed_records`  (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'id',
  `live_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '关联主表id',
  `room_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '房间id',
  `room_version` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '房间版本',
  `bed_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '\r\n入住床位',
  `begin_date` date NULL DEFAULT NULL COMMENT '入住日期',
  `end_date` date NULL DEFAULT NULL COMMENT '结束日期',
  `billing_date` date NULL DEFAULT NULL COMMENT '计费日期',
  `live_state` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '居住状态（在住，变更，退住）',
  `remark` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '备注',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建日期',
  `create_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '创建人员',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `update_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '修改人员',
  `del_flag` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT '0' COMMENT '逻辑删除标记（0：显示；1：隐藏',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '日间照料-居住床位记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_daycare_live_bed_records
-- ----------------------------
INSERT INTO `t_daycare_live_bed_records` VALUES ('07e29e54dd214d1686d705e014a46804', '6a1efdc3b4a347b59127f08cc9ca4b5d', '7', 'V.1', '3', '2022-08-31', '2022-09-12', '2022-08-31', '1', NULL, '2022-09-13 14:05:48', '1', '2022-09-13 17:59:59', '1', '0');
INSERT INTO `t_daycare_live_bed_records` VALUES ('0f62e9bff0024ddfaeddd922879f3dd1', 'a4981f0b9cf04509a3fc048cdcd14cf1', '7', 'V.1', '5', '2022-08-31', '2022-09-13', '2022-08-31', '1', NULL, '2022-09-13 14:11:10', '1', '2022-09-15 11:03:41', '1', '0');
INSERT INTO `t_daycare_live_bed_records` VALUES ('21dc126af7ec41c9a790e2e6dbb4242d', '770a5cc5f9bb4aa98523daef8a25faa2', '6', 'V.1', '2', '2022-09-14', '2022-09-15', NULL, '1', NULL, '2022-09-14 15:10:30', '1', '2022-09-16 14:54:20', '1', '0');
INSERT INTO `t_daycare_live_bed_records` VALUES ('24a8a288d8a141fda0044f627f398a92', '2bdecc4a9aa749cb9ab3ee97d48b1879', '6', 'V.1', '2', NULL, '2022-09-12', NULL, '1', NULL, '2022-09-13 17:25:13', '1', '2022-09-13 17:47:49', '1', '0');
INSERT INTO `t_daycare_live_bed_records` VALUES ('26e90b9096d6498ea9ac46818c125604', '2bdecc4a9aa749cb9ab3ee97d48b1879', '5', 'V.1', '1', NULL, '2022-09-12', NULL, '1', NULL, '2022-09-13 17:47:49', '1', '2022-09-13 17:50:11', '1', '0');
INSERT INTO `t_daycare_live_bed_records` VALUES ('2cb77367608e4871a0777f4611da1903', '770a5cc5f9bb4aa98523daef8a25faa2', '14', 'V.1', '10', NULL, '2022-09-12', NULL, '1', NULL, '2022-09-13 16:45:58', '1', '2022-09-13 17:57:16', '1', '0');
INSERT INTO `t_daycare_live_bed_records` VALUES ('3676274d64014e908f3c252f9fc37d9a', '770a5cc5f9bb4aa98523daef8a25faa2', '10', 'V.1', '7', '2022-09-16', NULL, NULL, '0', NULL, '2022-09-16 14:55:16', '1', NULL, NULL, '0');
INSERT INTO `t_daycare_live_bed_records` VALUES ('3cbe9af596c3437c94919a58e2e2b97c', '6a1efdc3b4a347b59127f08cc9ca4b5d', '10', 'V.1', '7', '2022-09-13', '2022-09-15', NULL, '1', NULL, '2022-09-13 18:03:33', '1', '2022-09-16 14:55:16', '1', '0');
INSERT INTO `t_daycare_live_bed_records` VALUES ('3de4ff062a724914be50270ee8ccba71', '770a5cc5f9bb4aa98523daef8a25faa2', '5', 'V.1', '1', '2022-09-14', NULL, '2022-09-14', '1', NULL, '2022-09-13 16:18:18', '1', '2022-09-13 16:45:20', '1', '0');
INSERT INTO `t_daycare_live_bed_records` VALUES ('4765ea08b4ff4b0bbc7976729d9ab127', '6a1efdc3b4a347b59127f08cc9ca4b5d', '8', 'V.1', '4', NULL, '2022-09-12', NULL, '1', NULL, '2022-09-13 17:59:59', '1', '2022-09-13 18:00:17', '1', '0');
INSERT INTO `t_daycare_live_bed_records` VALUES ('4d1c70200ac04f92bf95004e2095b077', 'a4981f0b9cf04509a3fc048cdcd14cf1', '6', 'V.1', '12', '2022-09-15', '2022-09-14', NULL, '1', NULL, '2022-09-15 11:56:18', '1', '2022-09-15 11:56:49', '1', '1');
INSERT INTO `t_daycare_live_bed_records` VALUES ('56684aa5f29448de8c2a09ea5368e6a5', '97f1587037284e01888984bb42b39144', '5', 'V.1', '1', '2022-07-31', '2022-09-13', '2022-07-31', '2', NULL, '2022-09-13 14:00:43', '1', '2022-09-13 15:05:32', '1', '0');
INSERT INTO `t_daycare_live_bed_records` VALUES ('7125edef10db492b8d19a650de34279a', '4594961115a848938020eebd9ea9d307', '14', 'V.1', '10', '2022-12-14', NULL, '2022-12-14', '0', NULL, '2022-12-14 17:13:23', '118', NULL, NULL, '0');
INSERT INTO `t_daycare_live_bed_records` VALUES ('717aa0e29844441eb464d4ead91219e9', '770a5cc5f9bb4aa98523daef8a25faa2', '14', 'V.1', '9', NULL, NULL, NULL, '1', NULL, '2022-09-13 16:45:20', '1', '2022-09-13 16:45:58', '1', '0');
INSERT INTO `t_daycare_live_bed_records` VALUES ('73178e0840d8423bbaeb63129c277740', '2bdecc4a9aa749cb9ab3ee97d48b1879', '6', 'V.1', '2', '2022-08-31', NULL, '2022-08-31', '1', NULL, '2022-09-13 14:02:05', '1', '2022-09-13 16:46:23', '1', '0');
INSERT INTO `t_daycare_live_bed_records` VALUES ('733914dd0d584a01bc51a69c6dfbddee', '770a5cc5f9bb4aa98523daef8a25faa2', '6', 'V.1', '2', '2022-09-16', '2022-09-16', NULL, '1', NULL, '2022-09-16 14:54:20', '1', '2022-09-16 14:55:16', '1', '1');
INSERT INTO `t_daycare_live_bed_records` VALUES ('7bb6c076589e472586857551b9ab3e88', '2bdecc4a9aa749cb9ab3ee97d48b1879', '5', 'V.1', '1', NULL, '2022-09-13', NULL, '1', NULL, '2022-09-13 16:46:24', '1', '2022-09-13 17:25:13', '1', '0');
INSERT INTO `t_daycare_live_bed_records` VALUES ('7cc4fc32850049059951ce1f089ad41c', '2bdecc4a9aa749cb9ab3ee97d48b1879', '8', 'V.1', '4', '2022-09-13', '2022-09-12', NULL, '1', NULL, '2022-09-13 17:50:25', '1', '2022-09-13 17:54:55', '1', '0');
INSERT INTO `t_daycare_live_bed_records` VALUES ('987ca965b48b4ae591555f3be8c52504', '770a5cc5f9bb4aa98523daef8a25faa2', '6', 'V.1', '2', '2022-09-16', '2022-09-16', NULL, '1', NULL, '2022-09-16 14:54:20', '1', '2022-09-16 14:54:20', '1', '1');
INSERT INTO `t_daycare_live_bed_records` VALUES ('9cba6a1b142642a792da8b36dd9fcdf5', 'a4981f0b9cf04509a3fc048cdcd14cf1', '5', 'V.1', '1', '2022-09-15', '2022-09-14', NULL, '1', NULL, '2022-09-15 11:56:49', '1', '2022-09-15 13:39:09', '1', '1');
INSERT INTO `t_daycare_live_bed_records` VALUES ('9d1473ba73eb426490313db8dfb082a7', 'a4981f0b9cf04509a3fc048cdcd14cf1', '5', 'V.1', '1', '2022-09-15', '2022-09-14', NULL, '1', NULL, '2022-09-15 11:03:41', '1', '2022-09-15 11:04:00', '1', '1');
INSERT INTO `t_daycare_live_bed_records` VALUES ('9ff10401a60f459f955d14d6c1654636', '770a5cc5f9bb4aa98523daef8a25faa2', '7', 'V.1', '3', '2022-09-14', '2022-09-13', NULL, '1', NULL, '2022-09-14 15:09:51', '1', '2022-09-14 15:10:30', '1', '0');
INSERT INTO `t_daycare_live_bed_records` VALUES ('ac7c7a8ab886462bb0e5ed7265dc5885', '770a5cc5f9bb4aa98523daef8a25faa2', '5', 'V.1', '1', NULL, '2022-09-13', NULL, '1', NULL, '2022-09-13 17:57:16', '1', '2022-09-14 15:09:51', '1', '0');
INSERT INTO `t_daycare_live_bed_records` VALUES ('b1248a2da1a846338b09bd327ab49a43', 'a4981f0b9cf04509a3fc048cdcd14cf1', '7', 'V.1', '3', '2022-09-15', NULL, NULL, '0', NULL, '2022-09-15 13:40:23', '1', NULL, NULL, '0');
INSERT INTO `t_daycare_live_bed_records` VALUES ('b32e4f3f38474d3e867751a39505c0a8', 'a4981f0b9cf04509a3fc048cdcd14cf1', '14', 'V.1', '10', '2022-09-15', '2022-09-14', NULL, '1', NULL, '2022-09-15 13:39:44', '1', '2022-09-15 13:40:23', '1', '1');
INSERT INTO `t_daycare_live_bed_records` VALUES ('b698800a1cdd4d0dabed930d04180bfc', 'a4981f0b9cf04509a3fc048cdcd14cf1', '7', 'V.1', '3', '2022-09-15', '2022-09-14', NULL, '1', NULL, '2022-09-15 11:04:00', '1', '2022-09-15 11:34:33', '1', '0');
INSERT INTO `t_daycare_live_bed_records` VALUES ('c37cfc3878b6438a92355df097aeb708', 'a4981f0b9cf04509a3fc048cdcd14cf1', '6', 'V.1', '12', '2022-09-15', '2022-09-14', NULL, '1', NULL, '2022-09-15 13:39:09', '1', '2022-09-15 13:39:44', '1', '1');
INSERT INTO `t_daycare_live_bed_records` VALUES ('c9aedf272422441aaaf98b840146affb', 'a4981f0b9cf04509a3fc048cdcd14cf1', '10', 'V.1', '8', '2022-09-15', '2022-09-14', NULL, '1', NULL, '2022-09-15 11:34:33', '1', '2022-09-15 11:56:18', '1', '1');
INSERT INTO `t_daycare_live_bed_records` VALUES ('c9fbb8eebcf6480f8b46e83132845048', '2bdecc4a9aa749cb9ab3ee97d48b1879', '14', 'V.1', '9', '2022-09-13', NULL, NULL, '0', NULL, '2022-09-13 17:54:55', '1', NULL, NULL, '0');
INSERT INTO `t_daycare_live_bed_records` VALUES ('d20f1528562c4c78b27ef7f5cf9db54f', '6a1efdc3b4a347b59127f08cc9ca4b5d', '14', 'V.1', '10', '2022-09-13', '2022-09-12', NULL, '1', NULL, '2022-09-13 18:00:17', '1', '2022-09-13 18:03:32', '1', '0');
INSERT INTO `t_daycare_live_bed_records` VALUES ('d67e5b4df1c8473582913c8e4c326e72', '6a1efdc3b4a347b59127f08cc9ca4b5d', '6', 'V.1', '2', '2022-09-16', NULL, NULL, '0', NULL, '2022-09-16 14:55:16', '1', NULL, NULL, '0');
INSERT INTO `t_daycare_live_bed_records` VALUES ('d8039874f60147419a0e626540c2bd8c', '5ba9f63aba99475e9ab2423e92ddc899', '5', 'V.1', '1', '2022-09-15', NULL, '2022-09-15', '0', NULL, '2022-09-15 14:16:45', '1', NULL, NULL, '0');

-- ----------------------------
-- Table structure for t_daycare_live_combo_records
-- ----------------------------
DROP TABLE IF EXISTS `t_daycare_live_combo_records`;
CREATE TABLE `t_daycare_live_combo_records`  (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'id',
  `live_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '关联主表id',
  `live_date` date NULL DEFAULT NULL COMMENT '入住日期',
  `expired_date` date NULL DEFAULT NULL COMMENT '到期时间',
  `billing_date` date NULL DEFAULT NULL COMMENT '计费日期',
  `care_level` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '护理级别',
  `combo_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '套餐id',
  `combo_version` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '套餐版本',
  `state` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '状态（在用，已变更，终止）',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建日期',
  `create_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '创建人员',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `update_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '修改人员',
  `del_flag` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT '0' COMMENT '逻辑删除标记（0：显示；1：隐藏',
  `remark` varchar(1000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '居住套餐记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_daycare_live_combo_records
-- ----------------------------
INSERT INTO `t_daycare_live_combo_records` VALUES ('0438ab002bbb4bfa8431fc2caf671c0b', '770a5cc5f9bb4aa98523daef8a25faa2', '2022-09-14', NULL, '2022-09-14', '2', '5', '1', '0', '2022-09-14 15:11:10', '1', NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_live_combo_records` VALUES ('085ea02aa4b7443986208eca1a5b727b', '4594961115a848938020eebd9ea9d307', '2022-12-14', NULL, '2022-12-14', '2', '5', '2', '0', '2022-12-14 17:13:23', '118', NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_live_combo_records` VALUES ('090c62fb92f34b11a8813b3928b4cb5a', '2bdecc4a9aa749cb9ab3ee97d48b1879', NULL, '2022-09-12', NULL, '0', '2', '1', '1', '2022-09-13 17:25:13', '1', '2022-09-13 17:47:49', NULL, '0', NULL);
INSERT INTO `t_daycare_live_combo_records` VALUES ('0c5ecb5665ca4c44b94626cb1da488c6', '97f1587037284e01888984bb42b39144', '2022-07-31', NULL, '2022-07-31', '0', '2', '1', '0', '2022-09-13 14:00:43', '1', NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_live_combo_records` VALUES ('195d6eb5a77b418c8bf402c34949034e', '6a1efdc3b4a347b59127f08cc9ca4b5d', '2022-09-13', '2022-09-12', '2022-09-13', '1', '3', '1', '1', '2022-09-13 17:59:59', '1', '2022-09-13 18:00:17', NULL, '0', NULL);
INSERT INTO `t_daycare_live_combo_records` VALUES ('3bde61b277f341b0a8668973757ebd0c', '6a1efdc3b4a347b59127f08cc9ca4b5d', '2022-09-13', '2022-09-15', '2022-09-13', '0', '2', '1', '1', '2022-09-13 18:00:17', '1', '2022-09-16 14:56:32', NULL, '0', NULL);
INSERT INTO `t_daycare_live_combo_records` VALUES ('3c3d6984ae0448cf887dd1b38414450a', '2bdecc4a9aa749cb9ab3ee97d48b1879', NULL, '2022-09-12', '2022-09-13', '1', '3', '1', '1', '2022-09-13 17:47:49', '1', '2022-09-13 17:50:25', NULL, '0', NULL);
INSERT INTO `t_daycare_live_combo_records` VALUES ('4526c3a4a0904a1cb2ce379d90e9df9d', '6a1efdc3b4a347b59127f08cc9ca4b5d', '2022-09-16', '2022-09-16', '2022-09-16', '2', '5', '2', '1', '2022-09-16 14:56:32', '1', '2022-09-16 14:56:44', NULL, '1', NULL);
INSERT INTO `t_daycare_live_combo_records` VALUES ('4a97269c5e024d53aed5e60dd69722ef', '770a5cc5f9bb4aa98523daef8a25faa2', '2022-09-14', '2022-09-12', '2022-09-14', '1', '3', '1', '1', '2022-09-13 16:18:18', '1', '2022-09-13 17:57:16', NULL, '0', NULL);
INSERT INTO `t_daycare_live_combo_records` VALUES ('71e0d262c9f8477c84eceff0839da60c', '2bdecc4a9aa749cb9ab3ee97d48b1879', '2022-08-31', NULL, '2022-08-31', '0', '2', '1', '1', '2022-09-13 14:02:05', '1', '2022-09-13 16:47:11', NULL, '0', NULL);
INSERT INTO `t_daycare_live_combo_records` VALUES ('75cc769880654e75813960698cf9e7b2', 'a4981f0b9cf04509a3fc048cdcd14cf1', '2022-08-31', '2022-09-14', '2022-08-31', '0', '2', '1', '1', '2022-09-13 14:11:10', '1', '2022-09-15 13:39:44', NULL, '0', NULL);
INSERT INTO `t_daycare_live_combo_records` VALUES ('7c750ed31331490fb7fd0a4fe8f11d54', 'a4981f0b9cf04509a3fc048cdcd14cf1', '2022-09-15', '2022-09-14', '2022-09-15', '1', '3', '1', '1', '2022-09-15 13:39:44', '1', '2022-09-15 13:40:23', NULL, '1', NULL);
INSERT INTO `t_daycare_live_combo_records` VALUES ('8abff1116d114781b533ea7af2e8bfb9', '6a1efdc3b4a347b59127f08cc9ca4b5d', '2022-09-16', NULL, '2022-09-16', '1', '3', '1', '0', '2022-09-16 14:56:44', '1', NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_live_combo_records` VALUES ('9562b822f0e74692a77efd1309d58ed3', '2bdecc4a9aa749cb9ab3ee97d48b1879', NULL, '2022-09-12', '2022-09-13', '0', '2', '1', '1', '2022-09-13 17:50:25', '1', '2022-09-13 17:54:55', NULL, '0', NULL);
INSERT INTO `t_daycare_live_combo_records` VALUES ('9b32917f6e744214bd5253636a6bbdae', '5ba9f63aba99475e9ab2423e92ddc899', '2022-09-15', NULL, '2022-09-15', '0', '2', '1', '0', '2022-09-15 14:16:45', '1', NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_live_combo_records` VALUES ('a68260eee8fa45fc80da18f5f07c876d', 'a4981f0b9cf04509a3fc048cdcd14cf1', '2022-09-15', NULL, '2022-09-15', '2', '5', '2', '0', '2022-09-15 13:40:23', '1', NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_live_combo_records` VALUES ('abd5499d043c451ca858dd651812fff3', '770a5cc5f9bb4aa98523daef8a25faa2', '2022-09-13', '2022-09-13', '2022-09-13', '0', '2', '1', '1', '2022-09-13 17:57:16', '1', '2022-09-14 15:09:51', NULL, '0', NULL);
INSERT INTO `t_daycare_live_combo_records` VALUES ('ba6764be92a44b51bec71b4a9938f57f', '2bdecc4a9aa749cb9ab3ee97d48b1879', '2022-09-13', NULL, '2022-09-13', '1', '3', '1', '0', '2022-09-13 17:54:55', '1', NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_live_combo_records` VALUES ('c0921fcd537e47b48eb9b1d0fba8d053', '6a1efdc3b4a347b59127f08cc9ca4b5d', '2022-08-31', '2022-09-12', '2022-08-31', '0', '2', '1', '1', '2022-09-13 14:05:48', '1', '2022-09-13 17:59:59', NULL, '0', NULL);
INSERT INTO `t_daycare_live_combo_records` VALUES ('ecc8bf057fb44b5d9c3f2ff6307d3036', '770a5cc5f9bb4aa98523daef8a25faa2', '2022-09-14', '2022-09-13', '2022-09-14', '1', '3', '1', '1', '2022-09-14 15:09:51', '1', '2022-09-14 15:11:10', NULL, '0', NULL);
INSERT INTO `t_daycare_live_combo_records` VALUES ('f4003bc2c6af4a4d98267f510e86cdab', '2bdecc4a9aa749cb9ab3ee97d48b1879', NULL, '2022-09-13', NULL, '1', '3', '1', '1', '2022-09-13 16:47:11', '1', '2022-09-13 17:25:13', NULL, '0', NULL);

-- ----------------------------
-- Table structure for t_daycare_live_leave_records
-- ----------------------------
DROP TABLE IF EXISTS `t_daycare_live_leave_records`;
CREATE TABLE `t_daycare_live_leave_records`  (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `live_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '居住id',
  `user_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '用户id',
  `leave_time` datetime NULL DEFAULT NULL COMMENT '外出时间',
  `expected_back_time` datetime NULL DEFAULT NULL COMMENT '预计回院时间',
  `back_time` datetime NULL DEFAULT NULL COMMENT '实际归院时间',
  `leave_type` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '外出类型',
  `leave_reason` varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '外出事由',
  `state` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '状态',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建日期',
  `create_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '创建人员',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `update_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '修改人员',
  `del_flag` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT '0' COMMENT '逻辑删除标记（0：显示；1：隐藏',
  `remark` varchar(1000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '居住请假表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_daycare_live_leave_records
-- ----------------------------
INSERT INTO `t_daycare_live_leave_records` VALUES ('0eb52e57e7614df99a0963c79265fdd1', '5ba9f63aba99475e9ab2423e92ddc899', '863367c90003459b909b45f0c450eaec', '2022-09-15 17:41:28', '2022-09-16 15:00:00', NULL, '0', '', '0', '2022-09-15 17:41:40', '1', NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_live_leave_records` VALUES ('0ef529bcda1d4042a581d08a98e94f42', NULL, 'f9eb406ef62c478caffcd907f585f6e6', '2022-09-15 09:15:35', '2022-09-22 00:00:00', '2022-09-15 15:00:20', '1', '', '1', '2022-09-15 09:15:42', '1', '2022-09-15 15:00:22', '1', '0', NULL);
INSERT INTO `t_daycare_live_leave_records` VALUES ('0f6db10f2b2c4cf3bf303e2e1dc46cbc', NULL, '3cf9c491b5774476b297c3544cacffda', '2022-09-13 16:30:10', '2022-09-13 16:31:00', '2022-09-13 16:41:12', '1', '回家', '1', '2022-09-13 16:30:24', '1', '2022-09-13 16:41:13', '1', '0', NULL);
INSERT INTO `t_daycare_live_leave_records` VALUES ('1cee474aa58940668f015461257b1bcd', '770a5cc5f9bb4aa98523daef8a25faa2', 'f9eb406ef62c478caffcd907f585f6e6', '2022-09-16 10:00:00', '2022-09-16 17:00:00', '2022-09-17 00:00:00', '0', '', '1', '2022-09-15 15:39:33', '1', '2022-09-15 16:33:34', '1', '0', NULL);
INSERT INTO `t_daycare_live_leave_records` VALUES ('4bf2c6efb2c948e6b3bd6f792f763ab1', '2bdecc4a9aa749cb9ab3ee97d48b1879', '3cf9c491b5774476b297c3544cacffda', '2022-09-16 14:52:27', '2022-09-16 14:52:59', NULL, '0', '', '0', '2022-09-16 14:52:33', '1', NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_live_leave_records` VALUES ('672b411a5d50409aa3251846e44b2be8', NULL, 'd78b91296a4b4c7794f31e8483c6cc48', '2022-09-13 16:29:33', '2022-09-13 16:29:35', '2022-09-13 17:47:59', '0', '', '1', '2022-09-13 16:29:38', '1', '2022-09-13 17:48:00', '1', '0', NULL);
INSERT INTO `t_daycare_live_leave_records` VALUES ('7a131c10325e46259510dd6c562f2989', NULL, 'd78b91296a4b4c7794f31e8483c6cc48', '2022-09-14 09:24:54', '2022-09-15 00:00:00', '2022-09-15 14:53:57', '0', '', '1', '2022-09-14 09:24:59', '1', '2022-09-15 14:53:59', '1', '0', NULL);
INSERT INTO `t_daycare_live_leave_records` VALUES ('94a11115b0af4fcaa0fe9c73b99f2360', NULL, '3cf9c491b5774476b297c3544cacffda', '2022-09-14 00:00:00', '2022-09-14 09:24:21', '2022-09-15 14:54:02', '0', '', '1', '2022-09-14 09:24:26', '1', '2022-09-15 14:54:04', '1', '0', NULL);
INSERT INTO `t_daycare_live_leave_records` VALUES ('ae69d74aac9443c7b60f5cbcaf9150b3', NULL, 'f9eb406ef62c478caffcd907f585f6e6', '2022-09-13 16:28:10', '2022-09-14 13:00:00', '2022-09-13 16:29:56', '0', '回家找李四喝酒', '1', '2022-09-13 16:28:31', '1', '2022-09-13 16:29:58', '1', '0', NULL);

-- ----------------------------
-- Table structure for t_daycare_photo_album
-- ----------------------------
DROP TABLE IF EXISTS `t_daycare_photo_album`;
CREATE TABLE `t_daycare_photo_album`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '相册名称',
  `cover` varchar(1000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '封面',
  `description` longtext CHARACTER SET utf8 COLLATE utf8_bin NULL COMMENT '描述',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建日期',
  `create_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '创建人员',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `update_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '修改人员',
  `del_flag` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT '0' COMMENT '逻辑删除标记（0：显示；1：隐藏',
  `remark` varchar(1000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 21 CHARACTER SET = utf8 COLLATE = utf8_bin COMMENT = '相册信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_daycare_photo_album
-- ----------------------------
INSERT INTO `t_daycare_photo_album` VALUES (1, '家庭相册', '', '', '2022-09-06 15:01:30', '1', '2022-09-06 15:48:24', '1', '1', '');
INSERT INTO `t_daycare_photo_album` VALUES (2, '相册名称', '', '相册描述相册描述相册描述', '2022-09-06 15:17:59', '1', '2022-09-06 15:48:15', '1', '1', '');
INSERT INTO `t_daycare_photo_album` VALUES (3, '测试相册', NULL, '这是一个测试相册。', '2022-09-06 15:42:40', '1', '2022-09-06 17:09:00', '1', '1', NULL);
INSERT INTO `t_daycare_photo_album` VALUES (4, '测试相册。', 'http://**************:23360/ruoyi-auth/statics/photo/2022/09/06/gq_20220906171446A001.png', '我是一个测试数据。', '2022-09-06 17:09:13', '1', '2022-09-06 17:54:00', '1', '1', NULL);
INSERT INTO `t_daycare_photo_album` VALUES (5, '测试相册。', 'http://**************:23360/ruoyi-auth/statics/photo/2022/09/06/测试_20220906172750A001.png', '我是一个测试数据。', '2022-09-06 17:09:13', '1', '2022-09-06 17:53:55', '1', '1', NULL);
INSERT INTO `t_daycare_photo_album` VALUES (6, '相册名称', 'http://**************:23360/ruoyi-auth/statics/photo/2022/09/06/wlgj_20220906172851A006.png', '相册描述', '2022-09-06 17:28:44', '1', '2022-09-07 10:37:57', '1', '1', NULL);
INSERT INTO `t_daycare_photo_album` VALUES (7, '默认相册', 'http://**************:23360/ruoyi-auth/statics/photo/2022/09/07/gq_20220907103949A001.png', '我是一个默认相册', '2022-09-07 10:38:12', '1', '2022-09-07 10:44:06', '1', '1', NULL);
INSERT INTO `t_daycare_photo_album` VALUES (8, '相册1', 'http://**************:23360/ruoyi-auth/statics/photo/2022/09/07/sxt_20220907105552A003.png', '我是默认相册', '2022-09-07 10:47:08', '1', '2022-09-07 16:31:21', '1', '0', NULL);
INSERT INTO `t_daycare_photo_album` VALUES (9, '相册2', 'http://**************:23360/ruoyi-auth/statics/photo/2022/09/07/微信图片_20220907162004_20220907163305A004.png', '张三的幸福生活', '2022-09-07 16:31:43', '1', NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_photo_album` VALUES (10, '相册3', 'http://**************:23360/ruoyi-auth/statics/2022/09/02/xcfm.jpg', '', '2022-09-07 16:34:27', '1', '2022-09-07 17:01:56', '1', '1', NULL);
INSERT INTO `t_daycare_photo_album` VALUES (11, '相册4', 'http://**************:23360/ruoyi-auth/statics/2022/09/02/xcfm.jpg', '', '2022-09-07 16:34:32', '1', NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_photo_album` VALUES (12, '相册5', 'http://**************:23360/ruoyi-auth/statics/2022/09/02/xcfm.jpg', '李四的晚年生活', '2022-09-07 16:35:24', '1', '2022-09-07 16:36:20', '1', '1', NULL);
INSERT INTO `t_daycare_photo_album` VALUES (13, '相册6', 'http://**************:23360/ruoyi-auth/statics/2022/09/02/xcfm.jpg', '', '2022-09-07 16:35:33', '1', NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_photo_album` VALUES (14, '相册10', 'http://**************:23360/ruoyi-auth/statics/2022/09/02/xcfm.jpg', '', '2022-09-07 16:35:37', '1', '2022-09-07 16:36:28', '1', '0', NULL);
INSERT INTO `t_daycare_photo_album` VALUES (15, '相册88', 'http://**************:23360/ruoyi-auth/statics/2022/09/02/xcfm.jpg', '', '2022-09-07 16:35:41', '1', '2022-09-13 18:08:08', '1', '0', NULL);
INSERT INTO `t_daycare_photo_album` VALUES (16, '相册9', 'http://**************:23360/ruoyi-auth/statics/photo/2022/09/13/eb485fca4b3c998e89efa881aa815f7_20220913101943A004.png', '相册9相册9相册9相册9', '2022-09-07 16:35:44', '1', '2022-09-13 10:31:42', '1', '0', NULL);
INSERT INTO `t_daycare_photo_album` VALUES (17, '相册10', 'http://**************:23360/ruoyi-auth/statics/2022/09/02/xcfm.jpg', '', '2022-09-07 16:35:47', '1', '2022-09-07 16:36:13', '1', '1', NULL);
INSERT INTO `t_daycare_photo_album` VALUES (18, '相册11', 'http://**************:23360/ruoyi-auth/statics/photo/2022/09/09/xw1_20220909151924A002.png', '', '2022-09-07 16:35:51', '1', '2022-09-07 16:47:51', '1', '0', NULL);
INSERT INTO `t_daycare_photo_album` VALUES (19, '相册12', 'http://**************:23360/ruoyi-auth/statics/2022/09/02/xcfm.jpg', '', '2022-09-07 16:35:55', '1', '2022-09-07 16:36:10', '1', '1', NULL);
INSERT INTO `t_daycare_photo_album` VALUES (20, '相册13', 'http://**************:23360/ruoyi-auth/statics/2022/09/02/xcfm.jpg', '', '2022-09-07 16:35:59', '1', '2022-09-07 16:36:07', '1', '1', NULL);

-- ----------------------------
-- Table structure for t_daycare_photo_album_detail
-- ----------------------------
DROP TABLE IF EXISTS `t_daycare_photo_album_detail`;
CREATE TABLE `t_daycare_photo_album_detail`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `file_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `file_url` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `file_size` double NULL DEFAULT NULL,
  `index_id` int NULL DEFAULT NULL COMMENT '关联id',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建日期',
  `create_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '创建人员',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `update_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '修改人员',
  `del_flag` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT '0' COMMENT '逻辑删除标记（0：显示；1：隐藏',
  `remark` varchar(1000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 81 CHARACTER SET = utf8 COLLATE = utf8_bin COMMENT = '相册明细表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_daycare_photo_album_detail
-- ----------------------------
INSERT INTO `t_daycare_photo_album_detail` VALUES (3, '图片1_20220905143640A003.png', 'http://**************:23360/ruoyi-auth/statics/photo/2022/09/05/图片1_20220905143640A003.png', 219399, 1, '2022-09-05 14:36:41', '1', NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_photo_album_detail` VALUES (4, '图片2_20220905143641A004.png', 'http://**************:23360/ruoyi-auth/statics/photo/2022/09/05/图片2_20220905143641A004.png', 79217, 1, '2022-09-05 14:36:41', '1', NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_photo_album_detail` VALUES (5, 'Building_template_1662348973629_20220906155626A002.xlsx', 'http://**************:23360/ruoyi-auth/statics/photo/2022/09/06/Building_template_1662348973629_20220906155626A002.xlsx', 12800, 1, '2022-09-06 15:56:26', '1', NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_photo_album_detail` VALUES (6, 'Building_template_1662348870909_20220906155626A003.xlsx', 'http://**************:23360/ruoyi-auth/statics/photo/2022/09/06/Building_template_1662348870909_20220906155626A003.xlsx', 12800, 1, '2022-09-06 15:56:26', '1', NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_photo_album_detail` VALUES (7, 'Building_template_1662348860734_20220906155626A004.xlsx', 'http://**************:23360/ruoyi-auth/statics/photo/2022/09/06/Building_template_1662348860734_20220906155626A004.xlsx', 12800, 1, '2022-09-06 15:56:26', '1', NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_photo_album_detail` VALUES (8, 'Building_template_1662348811383_20220906155626A005.xlsx', 'http://**************:23360/ruoyi-auth/statics/photo/2022/09/06/Building_template_1662348811383_20220906155626A005.xlsx', 12800, 1, '2022-09-06 15:56:26', '1', NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_photo_album_detail` VALUES (9, 'xw3_20220906161758A006.png', 'http://**************:23360/ruoyi-auth/statics/photo/2022/09/06/xw3_20220906161758A006.png', 281528, 3, '2022-09-06 16:17:59', '1', '2022-09-06 16:32:38', '1', '1', NULL);
INSERT INTO `t_daycare_photo_album_detail` VALUES (10, 'xw4_20220906161758A007.png', 'http://**************:23360/ruoyi-auth/statics/photo/2022/09/06/xw4_20220906161758A007.png', 80093, 3, '2022-09-06 16:17:59', '1', '2022-09-06 16:32:26', '1', '1', NULL);
INSERT INTO `t_daycare_photo_album_detail` VALUES (11, 'xwdtbg_20220906161758A008.png', 'http://**************:23360/ruoyi-auth/statics/photo/2022/09/06/xwdtbg_20220906161758A008.png', 317127, 3, '2022-09-06 16:17:59', '1', '2022-09-06 16:32:26', '1', '1', NULL);
INSERT INTO `t_daycare_photo_album_detail` VALUES (12, 'xwt_20220906161758A009.png', 'http://**************:23360/ruoyi-auth/statics/photo/2022/09/06/xwt_20220906161758A009.png', 27887, 3, '2022-09-06 16:17:59', '1', NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_photo_album_detail` VALUES (13, 'gq_20220906163247A010.png', 'http://**************:23360/ruoyi-auth/statics/photo/2022/09/06/gq_20220906163247A010.png', 55560, 3, '2022-09-06 16:32:47', '1', NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_photo_album_detail` VALUES (14, 'hlw-zwfw_20220906163247A011.png', 'http://**************:23360/ruoyi-auth/statics/photo/2022/09/06/hlw-zwfw_20220906163247A011.png', 7641, 3, '2022-09-06 16:32:47', '1', NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_photo_album_detail` VALUES (15, 'sxt_20220906163247A012.png', 'http://**************:23360/ruoyi-auth/statics/photo/2022/09/06/sxt_20220906163247A012.png', 37751, 3, '2022-09-06 16:32:47', '1', NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_photo_album_detail` VALUES (16, 'wldb_20220906163247A013.png', 'http://**************:23360/ruoyi-auth/statics/photo/2022/09/06/wldb_20220906163247A013.png', 35976, 3, '2022-09-06 16:32:47', '1', NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_photo_album_detail` VALUES (17, 'wlgj_20220906163247A014.png', 'http://**************:23360/ruoyi-auth/statics/photo/2022/09/06/wlgj_20220906163247A014.png', 29328, 3, '2022-09-06 16:32:47', '1', NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_photo_album_detail` VALUES (18, 'xw1_20220906163247A015.png', 'http://**************:23360/ruoyi-auth/statics/photo/2022/09/06/xw1_20220906163247A015.png', 396117, 3, '2022-09-06 16:32:47', '1', NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_photo_album_detail` VALUES (19, 'xw2_20220906163247A016.png', 'http://**************:23360/ruoyi-auth/statics/photo/2022/09/06/xw2_20220906163247A016.png', 95959, 3, '2022-09-06 16:32:47', '1', NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_photo_album_detail` VALUES (20, 'xw3_20220906163247A017.png', 'http://**************:23360/ruoyi-auth/statics/photo/2022/09/06/xw3_20220906163247A017.png', 281528, 3, '2022-09-06 16:32:48', '1', NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_photo_album_detail` VALUES (21, 'xw4_20220906163247A018.png', 'http://**************:23360/ruoyi-auth/statics/photo/2022/09/06/xw4_20220906163247A018.png', 80093, 3, '2022-09-06 16:32:48', '1', NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_photo_album_detail` VALUES (22, 'xwdtbg_20220906163247A019.png', 'http://**************:23360/ruoyi-auth/statics/photo/2022/09/06/xwdtbg_20220906163247A019.png', 317127, 3, '2022-09-06 16:32:48', '1', NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_photo_album_detail` VALUES (23, 'xwt_20220906163247A020.png', 'http://**************:23360/ruoyi-auth/statics/photo/2022/09/06/xwt_20220906163247A020.png', 27887, 3, '2022-09-06 16:32:48', '1', NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_photo_album_detail` VALUES (24, '微信图片_20210728105953_20220906163439A021.png', 'http://**************:23360/ruoyi-auth/statics/photo/2022/09/06/微信图片_20210728105953_20220906163439A021.png', 242432, 3, '2022-09-06 16:34:40', '1', NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_photo_album_detail` VALUES (25, 'gq_20220906171446A001.png', 'http://**************:23360/ruoyi-auth/statics/photo/2022/09/06/gq_20220906171446A001.png', 55560, 4, '2022-09-06 17:14:46', '1', NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_photo_album_detail` VALUES (26, 'hlw-zwfw_20220906171446A002.png', 'http://**************:23360/ruoyi-auth/statics/photo/2022/09/06/hlw-zwfw_20220906171446A002.png', 7641, 4, '2022-09-06 17:14:46', '1', NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_photo_album_detail` VALUES (27, 'sxt_20220906171446A003.png', 'http://**************:23360/ruoyi-auth/statics/photo/2022/09/06/sxt_20220906171446A003.png', 37751, 4, '2022-09-06 17:14:47', '1', NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_photo_album_detail` VALUES (28, 'wldb_20220906171446A004.png', 'http://**************:23360/ruoyi-auth/statics/photo/2022/09/06/wldb_20220906171446A004.png', 35976, 4, '2022-09-06 17:14:47', '1', NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_photo_album_detail` VALUES (29, 'wlgj_20220906171446A005.png', 'http://**************:23360/ruoyi-auth/statics/photo/2022/09/06/wlgj_20220906171446A005.png', 29328, 4, '2022-09-06 17:14:47', '1', NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_photo_album_detail` VALUES (30, 'xw1_20220906171446A006.png', 'http://**************:23360/ruoyi-auth/statics/photo/2022/09/06/xw1_20220906171446A006.png', 396117, 4, '2022-09-06 17:14:47', '1', NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_photo_album_detail` VALUES (31, 'xw2_20220906171446A007.png', 'http://**************:23360/ruoyi-auth/statics/photo/2022/09/06/xw2_20220906171446A007.png', 95959, 4, '2022-09-06 17:14:47', '1', NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_photo_album_detail` VALUES (32, 'xw3_20220906171446A008.png', 'http://**************:23360/ruoyi-auth/statics/photo/2022/09/06/xw3_20220906171446A008.png', 281528, 4, '2022-09-06 17:14:47', '1', NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_photo_album_detail` VALUES (33, 'xw4_20220906171446A009.png', 'http://**************:23360/ruoyi-auth/statics/photo/2022/09/06/xw4_20220906171446A009.png', 80093, 4, '2022-09-06 17:14:47', '1', NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_photo_album_detail` VALUES (34, 'xwdtbg_20220906171446A010.png', 'http://**************:23360/ruoyi-auth/statics/photo/2022/09/06/xwdtbg_20220906171446A010.png', 317127, 4, '2022-09-06 17:14:47', '1', '2022-09-06 17:25:32', '1', '1', NULL);
INSERT INTO `t_daycare_photo_album_detail` VALUES (35, 'xwt_20220906171446A011.png', 'http://**************:23360/ruoyi-auth/statics/photo/2022/09/06/xwt_20220906171446A011.png', 27887, 4, '2022-09-06 17:14:47', '1', NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_photo_album_detail` VALUES (36, '测试_20220906171446A012.png', 'http://**************:23360/ruoyi-auth/statics/photo/2022/09/06/测试_20220906171446A012.png', 7119, 4, '2022-09-06 17:14:47', '1', '2022-09-06 17:25:32', '1', '1', NULL);
INSERT INTO `t_daycare_photo_album_detail` VALUES (37, '测试_20220906172750A001.png', 'http://**************:23360/ruoyi-auth/statics/photo/2022/09/06/测试_20220906172750A001.png', 7119, 5, '2022-09-06 17:27:51', '1', NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_photo_album_detail` VALUES (38, 'sxt_20220906172810A002.png', 'http://**************:23360/ruoyi-auth/statics/photo/2022/09/06/sxt_20220906172810A002.png', 37751, 5, '2022-09-06 17:28:10', '1', NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_photo_album_detail` VALUES (39, 'hlw-zwfw_20220906172851A003.png', 'http://**************:23360/ruoyi-auth/statics/photo/2022/09/06/hlw-zwfw_20220906172851A003.png', 7641, 6, '2022-09-06 17:28:52', '1', '2022-09-06 17:32:08', '1', '1', NULL);
INSERT INTO `t_daycare_photo_album_detail` VALUES (40, 'sxt_20220906172851A004.png', 'http://**************:23360/ruoyi-auth/statics/photo/2022/09/06/sxt_20220906172851A004.png', 37751, 6, '2022-09-06 17:28:52', '1', '2022-09-06 17:38:57', '1', '1', NULL);
INSERT INTO `t_daycare_photo_album_detail` VALUES (41, 'wldb_20220906172851A005.png', 'http://**************:23360/ruoyi-auth/statics/photo/2022/09/06/wldb_20220906172851A005.png', 35976, 6, '2022-09-06 17:28:52', '1', '2022-09-06 17:39:47', '1', '1', NULL);
INSERT INTO `t_daycare_photo_album_detail` VALUES (42, 'wlgj_20220906172851A006.png', 'http://**************:23360/ruoyi-auth/statics/photo/2022/09/06/wlgj_20220906172851A006.png', 29328, 6, '2022-09-06 17:28:52', '1', NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_photo_album_detail` VALUES (43, 'xw1_20220906172851A007.png', 'http://**************:23360/ruoyi-auth/statics/photo/2022/09/06/xw1_20220906172851A007.png', 396117, 6, '2022-09-06 17:28:52', '1', NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_photo_album_detail` VALUES (44, 'xw2_20220906172852A008.png', 'http://**************:23360/ruoyi-auth/statics/photo/2022/09/06/xw2_20220906172852A008.png', 95959, 6, '2022-09-06 17:28:52', '1', '2022-09-06 17:36:39', '1', '1', NULL);
INSERT INTO `t_daycare_photo_album_detail` VALUES (45, 'xw3_20220906172852A009.png', 'http://**************:23360/ruoyi-auth/statics/photo/2022/09/06/xw3_20220906172852A009.png', 281528, 6, '2022-09-06 17:28:52', '1', NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_photo_album_detail` VALUES (46, 'xw4_20220906172852A010.png', 'http://**************:23360/ruoyi-auth/statics/photo/2022/09/06/xw4_20220906172852A010.png', 80093, 6, '2022-09-06 17:28:52', '1', '2022-09-06 17:32:34', '1', '1', NULL);
INSERT INTO `t_daycare_photo_album_detail` VALUES (47, 'wldb_20220906172940A011.png', 'http://**************:23360/ruoyi-auth/statics/photo/2022/09/06/wldb_20220906172940A011.png', 35976, 5, '2022-09-06 17:29:40', '1', NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_photo_album_detail` VALUES (48, '测试_20220906173018A012.png', 'http://**************:23360/ruoyi-auth/statics/photo/2022/09/06/测试_20220906173018A012.png', 7119, 6, '2022-09-06 17:30:19', '1', '2022-09-06 17:32:08', '1', '1', NULL);
INSERT INTO `t_daycare_photo_album_detail` VALUES (49, 'sxt_20220907091049A001.png', 'http://**************:23360/ruoyi-auth/statics/photo/2022/09/07/sxt_20220907091049A001.png', 37751, 6, '2022-09-07 09:10:49', '1', NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_photo_album_detail` VALUES (50, 'gq_20220907103949A001.png', 'http://**************:23360/ruoyi-auth/statics/photo/2022/09/07/gq_20220907103949A001.png', 55560, 7, '2022-09-07 10:39:50', '1', NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_photo_album_detail` VALUES (51, 'hlw-zwfw_20220907103950A002.png', 'http://**************:23360/ruoyi-auth/statics/photo/2022/09/07/hlw-zwfw_20220907103950A002.png', 7641, 7, '2022-09-07 10:39:51', '1', NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_photo_album_detail` VALUES (52, 'sxt_20220907103951A003.png', 'http://**************:23360/ruoyi-auth/statics/photo/2022/09/07/sxt_20220907103951A003.png', 37751, 7, '2022-09-07 10:39:51', '1', NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_photo_album_detail` VALUES (53, 'gq_20220907104718A001.png', 'http://**************:23360/ruoyi-auth/statics/photo/2022/09/07/gq_20220907104718A001.png', 55560, 8, '2022-09-07 10:47:18', '1', '2022-09-07 10:55:40', '1', '1', NULL);
INSERT INTO `t_daycare_photo_album_detail` VALUES (54, 'hlw-zwfw_20220907104718A002.png', 'http://**************:23360/ruoyi-auth/statics/photo/2022/09/07/hlw-zwfw_20220907104718A002.png', 7641, 8, '2022-09-07 10:47:18', '1', '2022-09-07 10:55:40', '1', '1', NULL);
INSERT INTO `t_daycare_photo_album_detail` VALUES (55, 'sxt_20220907104718A003.png', 'http://**************:23360/ruoyi-auth/statics/photo/2022/09/07/sxt_20220907104718A003.png', 37751, 8, '2022-09-07 10:47:18', '1', '2022-09-07 10:55:40', '1', '1', NULL);
INSERT INTO `t_daycare_photo_album_detail` VALUES (56, 'gq_20220907105552A001.png', 'http://**************:23360/ruoyi-auth/statics/photo/2022/09/07/gq_20220907105552A001.png', 55560, 8, '2022-09-07 10:55:52', '1', '2022-09-07 16:30:23', '1', '1', NULL);
INSERT INTO `t_daycare_photo_album_detail` VALUES (57, 'hlw-zwfw_20220907105552A002.png', 'http://**************:23360/ruoyi-auth/statics/photo/2022/09/07/hlw-zwfw_20220907105552A002.png', 7641, 8, '2022-09-07 10:55:52', '1', '2022-09-07 15:51:35', '1', '1', NULL);
INSERT INTO `t_daycare_photo_album_detail` VALUES (58, 'sxt_20220907105552A003.png', 'http://**************:23360/ruoyi-auth/statics/photo/2022/09/07/sxt_20220907105552A003.png', 37751, 8, '2022-09-07 10:55:52', '1', NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_photo_album_detail` VALUES (59, 'wldb_20220907105552A004.png', 'http://**************:23360/ruoyi-auth/statics/photo/2022/09/07/wldb_20220907105552A004.png', 35976, 8, '2022-09-07 10:55:52', '1', '2022-09-07 15:51:35', '1', '1', NULL);
INSERT INTO `t_daycare_photo_album_detail` VALUES (60, 'QQ截图20220907155157_20220907155213A001.png', 'http://**************:23360/ruoyi-auth/statics/photo/2022/09/07/QQ截图20220907155157_20220907155213A001.png', 11771, 8, '2022-09-07 15:52:14', '1', NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_photo_album_detail` VALUES (61, '微信图片_20220907162004_20220907163207A002.png', 'http://**************:23360/ruoyi-auth/statics/photo/2022/09/07/微信图片_20220907162004_20220907163207A002.png', 138082, 9, '2022-09-07 16:32:07', '1', '2022-09-07 16:32:56', '1', '1', NULL);
INSERT INTO `t_daycare_photo_album_detail` VALUES (62, '微信图片_202209071620041_20220907163217A003.png', 'http://**************:23360/ruoyi-auth/statics/photo/2022/09/07/微信图片_202209071620041_20220907163217A003.png', 276723, 9, '2022-09-07 16:32:18', '1', '2022-09-07 16:32:56', '1', '1', NULL);
INSERT INTO `t_daycare_photo_album_detail` VALUES (63, '微信图片_20220907162004_20220907163305A004.png', 'http://**************:23360/ruoyi-auth/statics/photo/2022/09/07/微信图片_20220907162004_20220907163305A004.png', 138082, 9, '2022-09-07 16:33:06', '1', NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_photo_album_detail` VALUES (64, '微信图片_20220907162004_20220907163319A005.png', 'http://**************:23360/ruoyi-auth/statics/photo/2022/09/07/微信图片_20220907162004_20220907163319A005.png', 138082, 9, '2022-09-07 16:33:19', '1', '2022-09-07 16:33:24', '1', '1', NULL);
INSERT INTO `t_daycare_photo_album_detail` VALUES (65, '微信图片_202209071620041_20220907163319A006.png', 'http://**************:23360/ruoyi-auth/statics/photo/2022/09/07/微信图片_202209071620041_20220907163319A006.png', 276723, 9, '2022-09-07 16:33:19', '1', NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_photo_album_detail` VALUES (66, 'wlgj_20220909151924A001.png', 'http://**************:23360/ruoyi-auth/statics/photo/2022/09/09/wlgj_20220909151924A001.png', 29328, 18, '2022-09-09 15:19:24', '1', '2024-08-01 11:44:10', '1', '1', NULL);
INSERT INTO `t_daycare_photo_album_detail` VALUES (67, 'xw1_20220909151924A002.png', 'http://**************:23360/ruoyi-auth/statics/photo/2022/09/09/xw1_20220909151924A002.png', 396117, 18, '2022-09-09 15:19:24', '1', NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_photo_album_detail` VALUES (68, 'xw2_20220909151924A003.png', 'http://**************:23360/ruoyi-auth/statics/photo/2022/09/09/xw2_20220909151924A003.png', 95959, 18, '2022-09-09 15:19:24', '1', NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_photo_album_detail` VALUES (69, 'xw3_20220909151924A004.png', 'http://**************:23360/ruoyi-auth/statics/photo/2022/09/09/xw3_20220909151924A004.png', 281528, 18, '2022-09-09 15:19:25', '1', NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_photo_album_detail` VALUES (70, 'xw4_20220909151924A005.png', 'http://**************:23360/ruoyi-auth/statics/photo/2022/09/09/xw4_20220909151924A005.png', 80093, 18, '2022-09-09 15:19:25', '1', NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_photo_album_detail` VALUES (71, 'xwdtbg_20220909151924A006.png', 'http://**************:23360/ruoyi-auth/statics/photo/2022/09/09/xwdtbg_20220909151924A006.png', 317127, 18, '2022-09-09 15:19:25', '1', NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_photo_album_detail` VALUES (72, 'xwt_20220909151924A007.png', 'http://**************:23360/ruoyi-auth/statics/photo/2022/09/09/xwt_20220909151924A007.png', 27887, 18, '2022-09-09 15:19:25', '1', NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_photo_album_detail` VALUES (73, 'e1b7f4616fb073c6a53b9118a63eb16_20220913101943A003.png', 'http://**************:23360/ruoyi-auth/statics/photo/2022/09/13/e1b7f4616fb073c6a53b9118a63eb16_20220913101943A003.png', 552145, 16, '2022-09-13 10:19:43', '1', '2022-09-13 10:19:56', '1', '1', NULL);
INSERT INTO `t_daycare_photo_album_detail` VALUES (74, 'eb485fca4b3c998e89efa881aa815f7_20220913101943A004.png', 'http://**************:23360/ruoyi-auth/statics/photo/2022/09/13/eb485fca4b3c998e89efa881aa815f7_20220913101943A004.png', 731772, 16, '2022-09-13 10:19:43', '1', NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_photo_album_detail` VALUES (75, 'sw_20220913101943A005.png', 'http://**************:23360/ruoyi-auth/statics/photo/2022/09/13/sw_20220913101943A005.png', 3393, 16, '2022-09-13 10:19:43', '1', NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_photo_album_detail` VALUES (76, 'tup_20220913101943A006.jpg', 'http://**************:23360/ruoyi-auth/statics/photo/2022/09/13/tup_20220913101943A006.jpg', 48603, 16, '2022-09-13 10:19:43', '1', NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_photo_album_detail` VALUES (77, 'tup_20220913102158A007.jpg', 'http://**************:23360/ruoyi-auth/statics/photo/2022/09/13/tup_20220913102158A007.jpg', 48603, 15, '2022-09-13 10:21:58', '1', '2022-09-13 10:22:17', '1', '1', NULL);
INSERT INTO `t_daycare_photo_album_detail` VALUES (78, 'xcfm_20220913102158A008.jpg', 'http://**************:23360/ruoyi-auth/statics/photo/2022/09/13/xcfm_20220913102158A008.jpg', 167696, 15, '2022-09-13 10:21:59', '1', '2022-09-13 10:22:17', '1', '1', NULL);
INSERT INTO `t_daycare_photo_album_detail` VALUES (79, '1687658035427_20230915141633A003.jpg', 'http://**************:23360/ruoyi-auth/statics/photo/2023/09/15/1687658035427_20230915141633A003.jpg', 252078, 18, '2023-09-15 14:16:34', '1', '2024-08-01 11:44:10', '1', '1', NULL);
INSERT INTO `t_daycare_photo_album_detail` VALUES (80, '1687315079601_20230915141633A004.jpg', 'http://**************:23360/ruoyi-auth/statics/photo/2023/09/15/1687315079601_20230915141633A004.jpg', 171864, 18, '2023-09-15 14:16:34', '1', '2024-08-01 11:44:10', '1', '1', NULL);

-- ----------------------------
-- Table structure for t_daycare_recipe_details_info
-- ----------------------------
DROP TABLE IF EXISTS `t_daycare_recipe_details_info`;
CREATE TABLE `t_daycare_recipe_details_info`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `type_id` int NULL DEFAULT NULL COMMENT '食谱类型id',
  `begin_date` datetime NULL DEFAULT NULL COMMENT '开始日期',
  `end_date` datetime NULL DEFAULT NULL COMMENT '结束日期',
  `data` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '食谱集合',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建日期',
  `create_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '创建人员',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `update_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '修改人员',
  `del_flag` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT '0' COMMENT '逻辑删除标记（0：显示；1：隐藏',
  `remark` varchar(1000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '食谱详情' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_daycare_recipe_details_info
-- ----------------------------
INSERT INTO `t_daycare_recipe_details_info` VALUES (1, 7, '2022-09-06 00:00:00', '2022-10-13 00:00:00', '[{\"date\":\"周一\",\"breakfast\":\"韭菜盒子\",\"lunch\":\"猪肉酸菜炖粉条\",\"dinner\":\"抻面、鸡架\"},{\"date\":\"周二\",\"breakfast\":\"\",\"lunch\":\"\",\"dinner\":\"\"},{\"date\":\"周三\",\"breakfast\":\"\",\"lunch\":\"\",\"dinner\":\"\"},{\"date\":\"周四\",\"breakfast\":\"\",\"lunch\":\"\",\"dinner\":\"\"},{\"date\":\"周五\",\"breakfast\":\"\",\"lunch\":\"\",\"dinner\":\"\"},{\"date\":\"周六\",\"breakfast\":\"\",\"lunch\":\"\",\"dinner\":\"\"},{\"date\":\"周日\",\"breakfast\":\"\",\"lunch\":\"\",\"dinner\":\"\"}]', '2022-09-06 16:49:33', NULL, NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_recipe_details_info` VALUES (2, 10, '2022-09-13 00:00:00', '2022-09-30 00:00:00', '[{\"date\":\"周一\",\"breakfast\":\"羊肉汤\",\"lunch\":\"拉面\",\"dinner\":\"牛肉汤\"},{\"date\":\"周二\",\"breakfast\":\"胡辣汤\",\"lunch\":\"葱爆羊肉\",\"dinner\":\"羊肉汤\"},{\"date\":\"周三\",\"breakfast\":\"羊肉汤\",\"lunch\":\"葱爆羊肉\",\"dinner\":\"羊肉汤\"},{\"date\":\"周四\",\"breakfast\":\"羊肉汤\",\"lunch\":\"拉面\",\"dinner\":\"水饺\"},{\"date\":\"周五\",\"breakfast\":\"羊肉汤\",\"lunch\":\"拉面\",\"dinner\":\"羊肉汤\"},{\"date\":\"周六\",\"breakfast\":\"羊肉汤\",\"lunch\":\"拉面\",\"dinner\":\"羊肉汤\"},{\"date\":\"周日\",\"breakfast\":\"羊肉汤\",\"lunch\":\"葱爆羊肉\",\"dinner\":\"水饺\"}]', '2022-09-13 17:52:28', NULL, '2022-09-13 17:52:40', NULL, '0', NULL);
INSERT INTO `t_daycare_recipe_details_info` VALUES (3, 6, '2022-09-13 00:00:00', '2022-09-30 00:00:00', '[{\"date\":\"周一\",\"breakfast\":\"回锅肉\",\"lunch\":\"毛血旺\",\"dinner\":\"四川炒鸡\"},{\"date\":\"周二\",\"breakfast\":\"四川炒鸡\",\"lunch\":\"毛血旺\",\"dinner\":\"回锅肉\"},{\"date\":\"周三\",\"breakfast\":\"毛血旺\",\"lunch\":\"四川炒鸡\",\"dinner\":\"回锅肉\"},{\"date\":\"周四\",\"breakfast\":\"回锅肉\",\"lunch\":\"四川炒鸡\",\"dinner\":\"毛血旺\"},{\"date\":\"周五\",\"breakfast\":\"回锅肉\",\"lunch\":\"四川炒鸡\",\"dinner\":\"毛血旺\"},{\"date\":\"周六\",\"breakfast\":\"四川炒鸡\",\"lunch\":\"回锅肉\",\"dinner\":\"回锅肉\"},{\"date\":\"周日\",\"breakfast\":\"毛血旺\",\"lunch\":\"回锅肉\",\"dinner\":\"四川炒鸡\"}]', '2022-09-13 17:53:37', NULL, NULL, NULL, '1', NULL);
INSERT INTO `t_daycare_recipe_details_info` VALUES (4, 6, '2023-01-12 00:00:00', '2023-02-13 00:00:00', '[{\"date\":\"周一\",\"breakfast\":\"豆浆\",\"lunch\":\"\",\"dinner\":\"\"},{\"date\":\"周二\",\"breakfast\":\"稀饭\",\"lunch\":\"\",\"dinner\":\"\"},{\"date\":\"周三\",\"breakfast\":\"面包\",\"lunch\":\"\",\"dinner\":\"\"},{\"date\":\"周四\",\"breakfast\":\"胡辣汤\",\"lunch\":\"\",\"dinner\":\"\"},{\"date\":\"周五\",\"breakfast\":\"小米粥\",\"lunch\":\"\",\"dinner\":\"\"},{\"date\":\"周六\",\"breakfast\":\"米线\",\"lunch\":\"\",\"dinner\":\"\"},{\"date\":\"周日\",\"breakfast\":\"热干面\",\"lunch\":\"\",\"dinner\":\"\"}]', '2023-01-12 11:41:04', NULL, NULL, NULL, '0', NULL);

-- ----------------------------
-- Table structure for t_daycare_recipe_menu_type_base_info
-- ----------------------------
DROP TABLE IF EXISTS `t_daycare_recipe_menu_type_base_info`;
CREATE TABLE `t_daycare_recipe_menu_type_base_info`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '食谱名称',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建日期',
  `create_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '创建人员',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `update_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '修改人员',
  `del_flag` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT '0' COMMENT '逻辑删除标记（0：显示；1：隐藏',
  `remark` varchar(1000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 11 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '食谱菜单类型基本信息' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_daycare_recipe_menu_type_base_info
-- ----------------------------
INSERT INTO `t_daycare_recipe_menu_type_base_info` VALUES (6, '川湘菜', '2022-09-01 14:58:44', '', '2022-09-13 17:54:00', '', '0', '');
INSERT INTO `t_daycare_recipe_menu_type_base_info` VALUES (7, '东北菜', '2022-09-01 17:03:36', '', NULL, '', '0', '');
INSERT INTO `t_daycare_recipe_menu_type_base_info` VALUES (8, '淮阳菜', '2022-09-01 17:11:12', '', '2022-09-05 09:06:28', '', '1', '');
INSERT INTO `t_daycare_recipe_menu_type_base_info` VALUES (9, '粤菜', '2022-09-06 16:47:24', NULL, NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_recipe_menu_type_base_info` VALUES (10, '清真', '2022-09-13 17:51:29', NULL, NULL, NULL, '0', NULL);

-- ----------------------------
-- Table structure for t_daycare_room_type_base_info
-- ----------------------------
DROP TABLE IF EXISTS `t_daycare_room_type_base_info`;
CREATE TABLE `t_daycare_room_type_base_info`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '房间类型名称',
  `status` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '状态',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建日期',
  `create_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '创建人员',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `update_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '修改人员',
  `del_flag` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT '0' COMMENT '逻辑删除标记（0：显示；1：隐藏',
  `remark` varchar(1000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '日间照料-房间类型' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_daycare_room_type_base_info
-- ----------------------------
INSERT INTO `t_daycare_room_type_base_info` VALUES (1, '单人间', NULL, '2022-09-06 11:51:18', NULL, NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_room_type_base_info` VALUES (2, '豪华间', NULL, '2022-09-06 11:51:30', NULL, NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_room_type_base_info` VALUES (3, '双人间', NULL, '2022-09-13 16:16:50', NULL, NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_room_type_base_info` VALUES (4, 'vip室', NULL, '2022-09-14 09:22:37', NULL, NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_room_type_base_info` VALUES (5, '豪华双人间', NULL, '2022-09-14 15:14:33', NULL, NULL, NULL, '0', NULL);

-- ----------------------------
-- Table structure for t_daycare_room_type_index_info
-- ----------------------------
DROP TABLE IF EXISTS `t_daycare_room_type_index_info`;
CREATE TABLE `t_daycare_room_type_index_info`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `type_id` int NULL DEFAULT NULL COMMENT '房间类型id',
  `type_version` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '房间类型版本号',
  `type_version_id` int NULL DEFAULT NULL COMMENT '房间类型版本号',
  `room_id` int NULL DEFAULT NULL COMMENT '房间id',
  `status` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '状态',
  `create_time` datetime NULL DEFAULT NULL COMMENT '开始日期',
  `create_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '创建人员',
  `end_time` datetime NULL DEFAULT NULL COMMENT '结束时间',
  `update_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '修改人员',
  `del_flag` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT '0' COMMENT '逻辑删除标记（0：显示；1：隐藏',
  `remark` varchar(1000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 15 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '日间照料-房间和类型的关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_daycare_room_type_index_info
-- ----------------------------
INSERT INTO `t_daycare_room_type_index_info` VALUES (1, 1, 'V.1', 1, 5, '0', '2022-09-06 14:49:32', '1', NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_room_type_index_info` VALUES (2, 1, 'V.1', 1, 6, '0', '2022-09-06 14:49:46', '1', NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_room_type_index_info` VALUES (3, 2, 'V.1', 2, 7, '0', '2022-09-06 14:50:06', '1', NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_room_type_index_info` VALUES (4, 2, 'V.1', 2, 8, '0', '2022-09-06 14:50:44', '1', NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_room_type_index_info` VALUES (5, 2, 'V.1', 2, 10, '0', '2022-09-06 14:51:09', '1', NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_room_type_index_info` VALUES (6, 1, 'V.1', 1, 14, '0', '2022-09-13 16:42:14', '1', NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_room_type_index_info` VALUES (7, 1, 'V.1', 1, 15, '0', '2022-09-13 16:42:22', '1', NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_room_type_index_info` VALUES (8, 2, 'V.1', 2, 16, '1', '2022-09-13 16:42:33', '1', NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_room_type_index_info` VALUES (9, 2, 'V.1', 2, 17, '1', '2022-09-13 16:42:44', '1', NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_room_type_index_info` VALUES (10, 2, 'V.1', 2, 18, '1', '2022-09-13 16:42:55', '1', NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_room_type_index_info` VALUES (11, 2, 'V.1', 2, 21, '0', '2022-09-13 17:05:07', '1', NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_room_type_index_info` VALUES (12, 2, 'V.1', 2, 22, '0', '2022-09-13 17:05:07', '1', NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_room_type_index_info` VALUES (13, 1, 'V.1', 1, 24, '0', '2022-09-13 17:05:08', '1', NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_room_type_index_info` VALUES (14, 1, 'V.1', 1, 26, '0', '2022-09-13 17:05:08', '1', NULL, NULL, '0', NULL);

-- ----------------------------
-- Table structure for t_daycare_room_type_version_info
-- ----------------------------
DROP TABLE IF EXISTS `t_daycare_room_type_version_info`;
CREATE TABLE `t_daycare_room_type_version_info`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `type_id` int NULL DEFAULT NULL COMMENT '房间类型id',
  `status` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '状态（0在用，1停用）',
  `fees` decimal(10, 4) NULL DEFAULT NULL COMMENT '费用',
  `daily_fee` decimal(10, 4) NULL DEFAULT NULL COMMENT '日费用',
  `version` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '版本号',
  `bed_num` int NULL DEFAULT NULL COMMENT '床位数',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建日期',
  `create_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '创建人员',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `update_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '修改人员',
  `del_flag` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT '0' COMMENT '逻辑删除标记（0：显示；1：隐藏',
  `remark` varchar(1000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '日间照料-房间类型版本' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_daycare_room_type_version_info
-- ----------------------------
INSERT INTO `t_daycare_room_type_version_info` VALUES (1, 1, '0', 5000.0000, 200.0000, 'V.1', NULL, '2022-09-06 11:51:18', '1', NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_room_type_version_info` VALUES (2, 2, '0', 2000.0000, 70.0000, 'V.1', NULL, '2022-09-06 11:51:30', '1', NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_room_type_version_info` VALUES (3, 3, '0', 2000.0000, 90.0000, 'V.1', NULL, '2022-09-13 16:16:50', '1', NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_room_type_version_info` VALUES (4, 4, '0', 3000.0000, 120.0000, 'V.1', NULL, '2022-09-14 09:22:37', '1', NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_room_type_version_info` VALUES (5, 5, '0', 200.0000, 4000.0000, 'V.1', NULL, '2022-09-14 15:14:33', '1', NULL, NULL, '0', NULL);

-- ----------------------------
-- Table structure for t_daycare_staff_dept
-- ----------------------------
DROP TABLE IF EXISTS `t_daycare_staff_dept`;
CREATE TABLE `t_daycare_staff_dept`  (
  `dept_id` bigint NOT NULL AUTO_INCREMENT COMMENT '部门id',
  `parent_id` bigint NULL DEFAULT 0 COMMENT '父部门id',
  `ancestors` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '祖级列表',
  `dept_name` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '部门名称',
  `order_num` int NULL DEFAULT 0 COMMENT '显示顺序',
  `leader` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '负责人',
  `phone` varchar(11) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '联系电话',
  `email` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '邮箱',
  `status` char(1) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '0' COMMENT '部门状态（0正常 1停用）',
  `del_flag` char(1) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`dept_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 103 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '员工部门信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_daycare_staff_dept
-- ----------------------------
INSERT INTO `t_daycare_staff_dept` VALUES (100, 0, '0', '日间照料中心', 0, '若依', '15888888888', '<EMAIL>', '0', '0', 'admin', '2022-03-16 10:14:01', '', NULL);
INSERT INTO `t_daycare_staff_dept` VALUES (101, 100, '0,100', '保洁部', 1, NULL, NULL, NULL, '0', '0', 'admin', '2022-09-13 18:00:11', 'admin', '2022-09-13 18:00:48');
INSERT INTO `t_daycare_staff_dept` VALUES (102, 100, '0,100', '保安部', 2, NULL, NULL, NULL, '0', '0', 'admin', '2022-09-13 18:00:21', '', NULL);

-- ----------------------------
-- Table structure for t_daycare_staff_info
-- ----------------------------
DROP TABLE IF EXISTS `t_daycare_staff_info`;
CREATE TABLE `t_daycare_staff_info`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '姓名',
  `id_card_number` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '身份证',
  `sex` tinyint NULL DEFAULT NULL COMMENT '性别',
  `birthday` date NULL DEFAULT NULL COMMENT '出生日期',
  `age` int NULL DEFAULT NULL COMMENT '年龄',
  `nation` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '民族',
  `marital_status` tinyint NULL DEFAULT NULL COMMENT '婚姻状况',
  `phone` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '联系电话',
  `political_status` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '政治面貌',
  `address` varchar(1000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '居住地址',
  `onboarding_date` date NULL DEFAULT NULL COMMENT '入职日期',
  `dept` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '所属部门',
  `position` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '职务',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建日期',
  `create_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '创建人员',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `update_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '修改人员',
  `del_flag` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT '0' COMMENT '逻辑删除标记（0：显示；1：隐藏',
  `remark` varchar(1000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '备注',
  `work_status` tinyint NULL DEFAULT NULL COMMENT '在职状态（0在职 1离职）',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 7 CHARACTER SET = utf8 COLLATE = utf8_bin COMMENT = '员工信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_daycare_staff_info
-- ----------------------------
INSERT INTO `t_daycare_staff_info` VALUES (2, '孙春伟', '371521198410264915', 0, '1984-10-26', 37, '2', 0, '17777777777', '0', '谷县侨润街道办事处孙庄村66号', '2022-08-24', '100', '保安', '2022-09-06 09:18:37', '1', '2022-09-06 09:35:18', '1', '1', NULL, 0);
INSERT INTO `t_daycare_staff_info` VALUES (3, '孙春伟', '371521198410264915', 0, '1984-10-26', 37, '1', 0, '17888888888', '0', '阳谷县侨润街道办事处孙庄村66号', '2021-09-16', '102', '保安', '2022-09-06 09:36:00', '1', '2022-09-13 18:01:25', '1', '0', NULL, 0);
INSERT INTO `t_daycare_staff_info` VALUES (4, '胡明良', '371521198710123153', 0, '1987-10-12', 34, '1', NULL, '18888888888', '', '', '2022-09-01', '100', '保安', '2022-09-06 13:51:24', '1', '2022-09-13 17:55:55', '1', '1', NULL, 0);
INSERT INTO `t_daycare_staff_info` VALUES (5, '张杰', '372522197302100019', 0, '1973-02-10', 49, '1', NULL, '16666666666', '', '', '2022-09-05', '102', '保安', '2022-09-06 13:52:07', '1', '2022-09-13 18:01:17', '1', '0', NULL, 0);
INSERT INTO `t_daycare_staff_info` VALUES (6, '李易峰', '420682198711075511', 0, '1987-11-07', 34, '2', 1, '16660668892', '0', '上海华明路看守所', '2022-09-13', '101', '清洁工', '2022-09-13 17:55:34', '1', '2022-09-13 18:01:06', '1', '0', NULL, 0);

-- ----------------------------
-- Table structure for t_daycare_storied_building_info
-- ----------------------------
DROP TABLE IF EXISTS `t_daycare_storied_building_info`;
CREATE TABLE `t_daycare_storied_building_info`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `serial_number` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '编号',
  `name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '名称',
  `type` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '类型1楼栋，2楼层，3房间',
  `sort` bigint NULL DEFAULT NULL COMMENT '排序',
  `total_floors_number` bigint(11) UNSIGNED ZEROFILL NULL DEFAULT NULL COMMENT '楼层总数',
  `total_rooms_number` bigint(11) UNSIGNED ZEROFILL NULL DEFAULT NULL COMMENT '房间总数',
  `total_bed_number` bigint(11) UNSIGNED ZEROFILL NULL DEFAULT NULL COMMENT '床位总数',
  `occupancy_number` bigint NULL DEFAULT NULL COMMENT '入住人数',
  `room_type` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '房间类型',
  `parent_id` int NULL DEFAULT NULL COMMENT '父级id',
  `ancestors` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '祖级列表',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建日期',
  `create_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '创建人员',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `update_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '修改人员',
  `del_flag` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT '0' COMMENT '逻辑删除标记（0：显示；1：隐藏',
  `remark` varchar(1000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 27 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '日间照料-楼栋树状信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_daycare_storied_building_info
-- ----------------------------
INSERT INTO `t_daycare_storied_building_info` VALUES (1, NULL, '1号楼', '1', 1, NULL, NULL, NULL, NULL, NULL, 0, '0,', '2022-09-06 14:48:14', NULL, NULL, '1', '0', NULL);
INSERT INTO `t_daycare_storied_building_info` VALUES (2, NULL, '2号楼', '1', 2, NULL, NULL, NULL, NULL, NULL, 0, '0,', '2022-09-06 14:48:22', NULL, NULL, '1', '0', NULL);
INSERT INTO `t_daycare_storied_building_info` VALUES (3, NULL, '一层', '2', 1, NULL, NULL, NULL, NULL, NULL, 1, '0,1,', '2022-09-06 14:48:50', NULL, NULL, '1', '0', NULL);
INSERT INTO `t_daycare_storied_building_info` VALUES (4, NULL, '二层', '2', 2, NULL, NULL, NULL, NULL, NULL, 1, '0,1,', '2022-09-06 14:48:59', NULL, NULL, '1', '0', NULL);
INSERT INTO `t_daycare_storied_building_info` VALUES (5, NULL, '1001号房间', '3', 1, NULL, NULL, 00000000001, 0, '1', 3, '0,1,3,', '2022-09-06 14:49:32', NULL, NULL, '1', '0', NULL);
INSERT INTO `t_daycare_storied_building_info` VALUES (6, NULL, '1002号房间', '3', 2, NULL, NULL, 00000000002, 0, '1', 3, '0,1,3,', '2022-09-06 14:49:45', NULL, NULL, '1', '0', NULL);
INSERT INTO `t_daycare_storied_building_info` VALUES (7, NULL, '2001号房间', '3', 1, NULL, NULL, 00000000002, 0, '2', 4, '0,1,4,', '2022-09-06 14:50:06', NULL, NULL, '1', '0', NULL);
INSERT INTO `t_daycare_storied_building_info` VALUES (8, NULL, '2002号房间', '3', 2, NULL, NULL, 00000000002, 0, '2', 4, '0,1,4,', '2022-09-06 14:50:44', NULL, NULL, '1', '0', NULL);
INSERT INTO `t_daycare_storied_building_info` VALUES (9, NULL, '一层', '2', 1, NULL, NULL, NULL, NULL, NULL, 2, '0,2,', '2022-09-06 14:50:59', NULL, NULL, '1', '0', NULL);
INSERT INTO `t_daycare_storied_building_info` VALUES (10, NULL, '1001号房间', '3', 1, NULL, NULL, 00000000002, 0, '2', 9, '0,2,9,', '2022-09-06 14:51:09', NULL, NULL, '1', '0', NULL);
INSERT INTO `t_daycare_storied_building_info` VALUES (11, NULL, '3号楼', '1', 3, 00000000000, 00000000000, 00000000000, 0, NULL, 0, '0,', '2022-09-13 16:41:34', NULL, '2022-09-13 16:41:42', '1', '0', NULL);
INSERT INTO `t_daycare_storied_building_info` VALUES (12, NULL, '一层', '2', 1, 00000000001, 00000000002, 00000000000, 0, NULL, 11, '0,11,', '2022-09-13 16:41:55', NULL, '2022-09-13 16:43:04', '1', '0', NULL);
INSERT INTO `t_daycare_storied_building_info` VALUES (13, NULL, '二层', '2', 2, 00000000001, 00000000003, 00000000000, 0, NULL, 11, '0,11,', '2022-09-13 16:42:00', NULL, '2022-09-13 16:43:10', '1', '1', NULL);
INSERT INTO `t_daycare_storied_building_info` VALUES (14, NULL, '3101', '3', 1, NULL, NULL, 00000000002, 0, '1', 12, '0,11,12,', '2022-09-13 16:42:14', NULL, NULL, '1', '0', NULL);
INSERT INTO `t_daycare_storied_building_info` VALUES (15, NULL, '3102', '3', 2, NULL, NULL, NULL, NULL, '1', 12, '0,11,12,', '2022-09-13 16:42:21', NULL, NULL, '1', '0', NULL);
INSERT INTO `t_daycare_storied_building_info` VALUES (16, NULL, '3201', '3', 1, NULL, NULL, NULL, NULL, '2', 13, '0,11,13,', '2022-09-13 16:42:33', NULL, NULL, '1', '1', NULL);
INSERT INTO `t_daycare_storied_building_info` VALUES (17, NULL, '3202', '3', 2, NULL, NULL, NULL, NULL, '2', 13, '0,11,13,', '2022-09-13 16:42:44', NULL, NULL, '1', '1', NULL);
INSERT INTO `t_daycare_storied_building_info` VALUES (18, NULL, '3203', '3', 3, NULL, NULL, NULL, NULL, '2', 13, '0,11,13,', '2022-09-13 16:42:55', NULL, NULL, '1', '1', NULL);
INSERT INTO `t_daycare_storied_building_info` VALUES (19, NULL, '5号楼', '1', 4, 00000000003, 00000000004, 00000000000, 0, NULL, 0, '0,', '2022-09-13 17:05:06', NULL, '2022-09-13 17:05:38', '1', '0', NULL);
INSERT INTO `t_daycare_storied_building_info` VALUES (20, NULL, '一层', '2', NULL, NULL, NULL, NULL, NULL, NULL, 19, '0,19,', '2022-09-13 17:05:07', NULL, NULL, '1', '0', NULL);
INSERT INTO `t_daycare_storied_building_info` VALUES (21, NULL, '5101', '3', NULL, NULL, NULL, NULL, NULL, '2', 20, '0,19,20,', '2022-09-13 17:05:07', NULL, NULL, '1', '0', NULL);
INSERT INTO `t_daycare_storied_building_info` VALUES (22, NULL, '5102', '3', NULL, NULL, NULL, NULL, NULL, '2', 20, '0,19,20,', '2022-09-13 17:05:07', NULL, NULL, '1', '0', NULL);
INSERT INTO `t_daycare_storied_building_info` VALUES (23, NULL, '二层', '2', NULL, NULL, NULL, NULL, NULL, NULL, 19, '0,19,', '2022-09-13 17:05:07', NULL, NULL, '1', '0', NULL);
INSERT INTO `t_daycare_storied_building_info` VALUES (24, NULL, '5201', '3', NULL, NULL, NULL, NULL, NULL, '1', 23, '0,19,23,', '2022-09-13 17:05:08', NULL, NULL, '1', '0', NULL);
INSERT INTO `t_daycare_storied_building_info` VALUES (25, NULL, '三层', '2', NULL, NULL, NULL, NULL, NULL, NULL, 19, '0,19,', '2022-09-13 17:05:08', NULL, NULL, '1', '0', NULL);
INSERT INTO `t_daycare_storied_building_info` VALUES (26, NULL, '5301', '3', NULL, NULL, NULL, NULL, NULL, '1', 25, '0,19,25,', '2022-09-13 17:05:08', NULL, NULL, '1', '0', NULL);

-- ----------------------------
-- Table structure for t_daycare_training_log
-- ----------------------------
DROP TABLE IF EXISTS `t_daycare_training_log`;
CREATE TABLE `t_daycare_training_log`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '培训名称',
  `type` int NULL DEFAULT NULL COMMENT '培训类型',
  `lecturer` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '讲师',
  `training_unit` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '培训单位',
  `training_location` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '培训地点',
  `training_start_time` datetime NULL DEFAULT NULL COMMENT '培训开始时间',
  `training_end_time` datetime NULL DEFAULT NULL COMMENT '培训结束时间',
  `participant` varchar(1000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '参与员工',
  `file` varchar(2000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '附件',
  `training_content` longtext CHARACTER SET utf8 COLLATE utf8_bin NULL COMMENT '培训内容',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建日期',
  `create_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '创建人员',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `update_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '修改人员',
  `del_flag` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT '0' COMMENT '逻辑删除标记（0：显示；1：隐藏',
  `remark` varchar(1000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8 COLLATE = utf8_bin COMMENT = '员工培训信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_daycare_training_log
-- ----------------------------
INSERT INTO `t_daycare_training_log` VALUES (1, '培训名称', 0, '培训人', '培训单位', '培训地点', '2022-09-06 08:00:00', '2022-09-09 19:00:00', NULL, 'http://**************:23360/ruoyi-auth/statics/2022/09/05/1、兰考县经济技术开发区数字化管理大数据平台功能清单--0627_20220905170038A009.xlsx,http://**************:23360/ruoyi-auth/statics/2022/09/05/政策_20220905170038A010.docx,', '培训内容培训内容培训内容培训内容', '2022-09-05 17:01:07', '1', '2022-09-05 17:49:09', '1', '1', NULL);
INSERT INTO `t_daycare_training_log` VALUES (2, '战场抗压', 2, '盖伦', '德玛西亚集训营', '德玛西亚1号营地', '2022-09-13 00:00:00', '2022-09-14 00:00:00', NULL, '[{\"name\":\"政策_20220905174943A013.docx\",\"url\":\"http://**************:23360/ruoyi-auth/statics/2022/09/05/政策_20220905174943A013.docx\"}]', '战场抗压战场抗压', '2022-09-05 17:47:23', '1', '2022-09-05 17:51:20', '1', '1', NULL);
INSERT INTO `t_daycare_training_log` VALUES (3, '作战抗压', 2, '卡特琳娜', '德玛西亚集训营', '德玛西亚第一集训营', '2022-09-08 00:00:00', '2022-09-12 00:00:00', NULL, '[{\"name\":\"1、兰考县经济技术开发区数字化管理大数据平台功能清单--0627_20220914105249A004.xlsx\",\"url\":\"http://**************:23360/ruoyi-auth/statics/2022/09/14/1、兰考县经济技术开发区数字化管理大数据平台功能清单--0627_20220914105249A004.xlsx\"},{\"name\":\"政策_20220914105249A005.docx\",\"url\":\"http://**************:23360/ruoyi-auth/statics/2022/09/14/政策_20220914105249A005.docx\"}]', '战场上作战抗压！', '2022-09-05 17:53:46', '1', '2022-09-14 10:52:57', '1', '0', NULL);
INSERT INTO `t_daycare_training_log` VALUES (4, '心肺复苏', 2, '李德全', '北京协和医院', '机构二楼小会议室', '2022-09-13 00:00:00', '2022-09-15 00:00:00', NULL, '[{\"name\":\"新建文本文档_20220913175704A001.txt\",\"url\":\"http://**************:23360/ruoyi-auth/statics/2022/09/13/新建文本文档_20220913175704A001.txt\"}]', '如何做正确的心肺复苏', '2022-09-13 17:57:21', '1', '2022-09-13 17:58:08', '1', '0', NULL);
INSERT INTO `t_daycare_training_log` VALUES (5, '人工呼吸', 2, '宋祖儿', '郑大一附院', '二楼楼梯口', '2022-09-14 11:30:00', '2022-09-14 12:00:00', '张杰,孙春伟', '[{\"name\":\"OA系统平台需求规格说明书V.1.0 _20220914154052A002.docx\",\"url\":\"http://**************:23360/ruoyi-auth/statics/2022/09/14/OA系统平台需求规格说明书V.1.0 _20220914154052A002.docx\"},{\"name\":\"1--智慧园区综合管理平台功能清单_20220914154127A003.xlsx\",\"url\":\"http://**************:23360/ruoyi-auth/statics/2022/09/14/1--智慧园区综合管理平台功能清单_20220914154127A003.xlsx\"}]', '讲解如何人工呼吸', '2022-09-14 15:39:40', '1', '2024-08-01 11:44:28', '1', '0', NULL);

-- ----------------------------
-- Table structure for t_daycare_training_staff_info
-- ----------------------------
DROP TABLE IF EXISTS `t_daycare_training_staff_info`;
CREATE TABLE `t_daycare_training_staff_info`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `index_id` int NULL DEFAULT NULL,
  `staff_id` int NULL DEFAULT NULL,
  `staff_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建日期',
  `create_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '创建人员',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `update_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '修改人员',
  `del_flag` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT '0' COMMENT '逻辑删除标记（0：显示；1：隐藏',
  `remark` varchar(1000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 15 CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_daycare_training_staff_info
-- ----------------------------
INSERT INTO `t_daycare_training_staff_info` VALUES (1, 2, 3, NULL, NULL, NULL, NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_training_staff_info` VALUES (2, 2, 4, NULL, NULL, NULL, NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_training_staff_info` VALUES (3, 3, 5, '张杰', '2022-09-06 15:34:50', '1', '2022-09-06 15:41:41', '1', '1', NULL);
INSERT INTO `t_daycare_training_staff_info` VALUES (4, 3, 4, '胡明良', '2022-09-06 15:34:50', '1', '2022-09-06 15:41:49', '1', '1', NULL);
INSERT INTO `t_daycare_training_staff_info` VALUES (5, 3, 3, '孙春伟', '2022-09-06 15:34:58', '1', '2022-09-06 15:41:41', '1', '1', NULL);
INSERT INTO `t_daycare_training_staff_info` VALUES (6, 3, 5, '张杰', '2022-09-06 15:41:51', '1', NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_training_staff_info` VALUES (7, 3, 3, '孙春伟', '2022-09-06 15:41:54', '1', '2022-09-07 14:58:29', '1', '1', NULL);
INSERT INTO `t_daycare_training_staff_info` VALUES (8, 3, 4, '胡明良', '2022-09-06 15:41:54', '1', NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_training_staff_info` VALUES (9, 4, 5, '张杰', '2022-09-13 17:57:28', '1', '2022-09-13 17:57:40', '1', '1', NULL);
INSERT INTO `t_daycare_training_staff_info` VALUES (10, 4, 6, '李易峰', '2022-09-13 17:57:28', '1', NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_training_staff_info` VALUES (11, 4, 5, '张杰', '2022-09-13 17:57:50', '1', NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_training_staff_info` VALUES (12, 5, 6, '李易峰', '2022-09-14 15:42:01', '1', '2022-09-14 15:42:28', '1', '1', NULL);
INSERT INTO `t_daycare_training_staff_info` VALUES (13, 5, 5, '张杰', '2022-09-14 15:42:01', '1', NULL, NULL, '0', NULL);
INSERT INTO `t_daycare_training_staff_info` VALUES (14, 5, 3, '孙春伟', '2022-09-14 15:42:13', '1', NULL, NULL, '0', NULL);

SET FOREIGN_KEY_CHECKS = 1;
