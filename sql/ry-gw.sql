/*
 Navicat Premium Data Transfer

 Source Server         : zmd-test-root
 Source Server Type    : MySQL
 Source Server Version : 80024
 Source Host           : localhost:23307
 Source Schema         : ry-gw

 Target Server Type    : MySQL
 Target Server Version : 80024
 File Encoding         : 65001

 Date: 31/03/2025 08:55:42
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for t_center_info
-- ----------------------------
DROP TABLE IF EXISTS `t_center_info`;
CREATE TABLE `t_center_info`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `title` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '标题',
  `content` text CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '内容',
  `icon_url` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '图标地址',
  `image_urls` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '图片地址，多个逗号隔开',
  `sort` int NOT NULL DEFAULT 0 COMMENT '排序',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '中心介绍表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_center_info
-- ----------------------------

-- ----------------------------
-- Table structure for t_home_banner
-- ----------------------------
DROP TABLE IF EXISTS `t_home_banner`;
CREATE TABLE `t_home_banner`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `sort` int NOT NULL COMMENT '排序',
  `enabled` tinyint NOT NULL COMMENT '是否启用, 0:是；1：否',
  `img_url` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '图片URL',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 9 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '首页轮播图设置表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_home_banner
-- ----------------------------
INSERT INTO `t_home_banner` VALUES (5, 1, 0, 'http://117.159.27.170:23407/test-api/file/statics/2025/03/28/微信图片_20250328115021_20250328114817A059.png');
INSERT INTO `t_home_banner` VALUES (6, 2, 1, 'http://117.159.27.170:23407/test-api/file/statics/2025/03/28/微信图片_20250328115015_20250328114824A060.png');
INSERT INTO `t_home_banner` VALUES (7, 1, 0, 'http://117.159.27.170:23407/test-api/file/statics/2025/03/28/微信图片_20250328113835_20250328113659A058.png');
INSERT INTO `t_home_banner` VALUES (8, 4, 1, 'http://117.159.27.170:23407/test-api/file/statics/2025/03/28/微信图片_20250328115534_20250328115333A061.jpg');

-- ----------------------------
-- Table structure for t_home_floating_window
-- ----------------------------
DROP TABLE IF EXISTS `t_home_floating_window`;
CREATE TABLE `t_home_floating_window`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `content` text CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '内容',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 8 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '首页飘窗设置表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_home_floating_window
-- ----------------------------
INSERT INTO `t_home_floating_window` VALUES (1, '200');
INSERT INTO `t_home_floating_window` VALUES (2, '200');
INSERT INTO `t_home_floating_window` VALUES (3, '这里是飘窗内容+');
INSERT INTO `t_home_floating_window` VALUES (4, '12121212');
INSERT INTO `t_home_floating_window` VALUES (5, '驻马店市为老服务中心位于驿城区盘龙山路与天颐路交叉口西南侧，建筑面积23163平方米，规划养老床位500张。');
INSERT INTO `t_home_floating_window` VALUES (6, '驻马店市为老服务中心位于驿城区盘龙山路与天颐路交叉口西南侧，建筑面积23163平方米，规划养老床位500张。');
INSERT INTO `t_home_floating_window` VALUES (7, '驻马店市为老服务中心位于驿城区盘龙山路与天颐路交叉口西南侧，建筑面积23163平方米，规划养老床位500张。');

-- ----------------------------
-- Table structure for t_news_info
-- ----------------------------
DROP TABLE IF EXISTS `t_news_info`;
CREATE TABLE `t_news_info`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `title` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '标题',
  `publish_time` datetime NOT NULL COMMENT '发布时间',
  `news_type` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '类型（字典：gw_news_info_type ）',
  `content` text CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '富文本内容',
  `summary` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '摘要',
  `img_url` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '封面图片地址',
  `del_flag` char(1) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '0' COMMENT '删除标志（0显示，1隐藏）',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 10 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '新闻资讯表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_news_info
-- ----------------------------
INSERT INTO `t_news_info` VALUES (1, 'zzzz', '2025-03-27 10:59:00', '1', '<p><span style=\"background-color: rgb(194, 79, 74);\">zzzzz</span></p>', 'zzzz', 'http://*************:8080/file/statics/2025/03/27/头像_20250327105910A020.jpg', '1');
INSERT INTO `t_news_info` VALUES (2, 'zzsss', '2025-03-27 11:23:00', '3', 'ggggggggggggg', 'asdadsa', 'http://*************:8080/file/statics/2025/03/27/头像_20250327112359A021.jpg', '1');
INSERT INTO `t_news_info` VALUES (3, 'zzzzzzzzzzzs', '2025-03-27 11:25:00', '1', 'asddassadadadwwfwfw', 'zzzzzzzzzzzzzzz', 'http://*************:8080/file/statics/2025/03/27/头像_20250327112524A022.jpg', '1');
INSERT INTO `t_news_info` VALUES (4, '真情暖夕阳，锦旗表谢意一一市为老服务中心再获家属赞誉', '2025-03-20 00:00:00', '1', '<p style=\"line-height:2;\">&nbsp; &nbsp; &nbsp; &nbsp;这面锦旗来自唐爷爷的家属。唐爷爷初住养老院初期情绪低落。照护部工作人员每天耐心开导，并鼓励他参与院内活动。从每日的肢体训练到心理疏导，无微不至。家属感动地说：“父亲从最初的抗拒到现在的主动配合，离不开护理员的耐心和专业。他们不仅是护理员，更是父亲的朋友和家人。”</p><p style=\"text-align:center; line-height:2;\"><img src=\"https://www.zmdswlfw.com/file/%E5%9B%BE%E7%89%87/mceclip0-20250317030530882.jpg\"/></p><p style=\"line-height:2;\">&nbsp; &nbsp; &nbsp; &nbsp; 这面锦旗来自邹奶奶的家属。邹奶奶患有阿尔茨海默症，且时常情绪波动。照护部工作人员不厌其烦地为她喂饭、擦身、换洗衣物，并像哄孩子一样安抚她的情绪。家属动情地说：“养老院的工作人员比我们做子女的还有耐心，把老人交给他们，我们一百个放心！”</p><p style=\"text-align:center; line-height:2;\"><img src=\"https://www.zmdswlfw.com/file/%E5%9B%BE%E7%89%87/mceclip2-2025031703073849.png\"/></p><p style=\"line-height:2;\">&nbsp; &nbsp; &nbsp; &nbsp; 这面锦旗来自门奶奶的家属。门奶奶刚入住养老院时，身体状况不佳，心理上也难以适应新环境。中心的护理团队迅速制定了个性化的护理方案，不仅在饮食起居上精心照料，还时常陪门奶奶聊天解闷，帮助她打开心扉。家属们看在眼里，感动在心里，特地送来锦旗表达对护理团队的感谢。</p><p style=\"text-align:center; line-height:2;\"><img src=\"https://www.zmdswlfw.com/file/%E5%9B%BE%E7%89%87/mceclip3-20250317030804146.png\"/></p><p style=\"line-height:2;\">&nbsp; &nbsp; &nbsp; &nbsp;而在这众多锦旗中，有一面显得格外特别。住在同一个房间的张奶奶亲手将一面锦旗送给了她的室友吴奶奶，锦旗上写着“古稀岁月仁心在，互助善举映春晖 ”。原来，两位长者自入住以来便相互陪伴、互相照顾，结下了深厚的友谊。</p><p style=\"text-align:center; line-height:2;\"><img src=\"https://www.zmdswlfw.com/file/%E5%9B%BE%E7%89%87/mceclip4-2025031703082357.jpg\"/></p><p style=\"line-height:2;\">&nbsp; &nbsp; &nbsp; &nbsp; 一面面锦旗，是肯定，是鼓励，更是鞭策。如今，这些锦旗被高高挂起，成为市为老服务中心一道独特的风景线，时刻提醒着每一个人这里所蕴含的爱与关怀。</p><p style=\"text-align:center; line-height:2;\"><img src=\"https://www.zmdswlfw.com/file/%E5%9B%BE%E7%89%87/mceclip5-2025031703085930.jpg\"/></p><p style=\"text-align:center; line-height:2;\"><img src=\"https://www.zmdswlfw.com/file/%E5%9B%BE%E7%89%87/mceclip6-20250317030904482.jpg\"/></p>', '这面锦旗来自唐爷爷的家属。唐爷爷初住养老院初期情绪低落。照护部工作人员每天耐心开导，并鼓励他参与院内活动。从每日的肢体训练到心理疏导，无微不至。家属感动地说：“父亲从最初的抗拒到现在的主动配合，离不开护理员的耐心和专业。他们不仅是护理员，更是父亲的朋友和家人。”', 'http://*************:8080/file/statics/2025/03/27/切图 95@2x_20250327151146A040.png', '0');
INSERT INTO `t_news_info` VALUES (5, '“原居安老” 可感可及', '2025-03-24 00:00:00', '2', '<p data-we-empty-p=\"\" style=\"text-align:center;\"><img src=\"http://*************:8080/file/statics/2025/03/27/切图 98@2x_20250327151952A050.png\" style=\"max-width:100%;\" contenteditable=\"false\"/><br/><br/></p><p style=\"text-align:center;\">“原居安老”<br/>可感可及</p><p style=\"text-align:center;\">“原居安老”<br/>可感可及</p><p style=\"text-align:center;\">“原居安老”<br/>可感可及</p><p style=\"text-align:center;\">“原居安老”<br/>可感可及</p><p style=\"text-align:center;\">“原居安老”<br/>可感可及</p><p data-we-empty-p=\"\" style=\"text-align:center;\"><br/></p><p style=\"text-align:center;\">“原居安老”<br/>可感可及</p><p style=\"text-align:center;\">“原居安老”<br/>可感可及-</p>', '“原居安老” 可感可及', 'http://*************:8080/file/statics/2025/03/27/切图 98@2x_20250327151944A049.png', '0');
INSERT INTO `t_news_info` VALUES (6, 'sadacx', '2025-03-27 17:28:00', '2', '<p>zzzzzzzzzzzz</p>', 'acscasca', 'http://*************:8080/file/statics/2025/03/27/头像_20250327172756A051.jpg', '1');
INSERT INTO `t_news_info` VALUES (7, '1111', '2025-03-28 10:08:00', '2', '<p>111</p>', '111', 'http://117.159.27.170:23407/test-api/file/statics/2025/03/28/_定位小_20250328100634A053.png', '0');
INSERT INTO `t_news_info` VALUES (8, '测试', '2025-03-28 11:37:00', '1', '<p>测试</p>', '测试', 'http://*************:8080/file/statics/2025/03/28/头像_20250328113811A001.jpg', '1');
INSERT INTO `t_news_info` VALUES (9, '测试', '2025-03-28 11:45:00', '1', '<p>测试</p>', '测试', 'http://*************:8080/file/statics/2025/03/28/头像_20250328114600A002.jpg', '1');

-- ----------------------------
-- Table structure for t_senior_activity
-- ----------------------------
DROP TABLE IF EXISTS `t_senior_activity`;
CREATE TABLE `t_senior_activity`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `title` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '标题',
  `img_url` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '图片地址',
  `content` text CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '富文本内容',
  `del_flag` char(1) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '0' COMMENT '删除标志（0显示，1隐藏）',
  `sort` int NOT NULL DEFAULT 0 COMMENT '排序',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 9 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '乐龄活动表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_senior_activity
-- ----------------------------
INSERT INTO `t_senior_activity` VALUES (4, 'asdas', 'http://*************:8080/file/statics/2025/03/27/90f4d0388d2676df7db2e3758bb55ac87020d1c66c9fc8e9aa545d56fea6411d_20250327115308A028.png', '<p>asd</p>', '0', 1);
INSERT INTO `t_senior_activity` VALUES (6, '测试', 'http://117.159.27.170:23407/test-api/file/statics/2025/03/28/_定位小_20250328100106A049.png', '<p>1</p>', '0', 1);
INSERT INTO `t_senior_activity` VALUES (7, '11', 'http://117.159.27.170:23407/test-api/file/statics/2025/03/28/_定位小_20250328100602A052.png', '<p>111</p>', '0', 11);

SET FOREIGN_KEY_CHECKS = 1;
