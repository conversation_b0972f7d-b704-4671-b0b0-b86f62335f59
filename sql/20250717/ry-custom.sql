-- 创建评估护理计划模版表
CREATE TABLE IF NOT EXISTS `t_nursing_plan_template` (
  `id` varchar(36) NOT NULL COMMENT '主键',
  `health_problem` varchar(255) DEFAULT NULL COMMENT '健康问题',
  `template_type` tinyint(4) DEFAULT NULL COMMENT '模版类型',
  `details` json DEFAULT NULL COMMENT '详情',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 1代表删除）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='评估护理计划模版表';

-- 添加模版类型字典
INSERT INTO `sys_dict_type` (`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) 
VALUES ('机构养老-评估护理计划-模版类型', 'custom_nursing_plan_template_type', '0', 'admin', NOW(), '', NULL, NULL);

INSERT INTO `sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) 
VALUES (1, '基础护理', '1', 'custom_nursing_plan_template_type', NULL, 'default', 'N', '0', 'admin', NOW(), '', NULL, NULL);

INSERT INTO `sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) 
VALUES (2, '专项护理', '2', 'custom_nursing_plan_template_type', NULL, 'default', 'N', '0', 'admin', NOW(), '', NULL, NULL);

INSERT INTO `sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) 
VALUES (3, '特殊护理', '3', 'custom_nursing_plan_template_type', NULL, 'default', 'N', '0', 'admin', NOW(), '', NULL, NULL); 