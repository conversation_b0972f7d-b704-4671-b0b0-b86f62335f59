# 护理计划模版API调用示例

## 查询部分内容
GET /assessment/nursingPlanTemplate/section/cause/a1b2c3d4-e5f6-7890-a1b2-c3d4e5f67890

返回示例：
```json
{
  "msg": "操作成功",
  "code": 200,
  "data": [
    {"uuid": "c001", "option": "胰岛素分泌不足"}, 
    {"uuid": "c002", "option": "胰岛素抵抗"}, 
    {"uuid": "c003", "option": "饮食控制不当"}, 
    {"uuid": "c004", "option": "运动不足"}
  ]
}
```

## 更新部分内容
PUT /assessment/nursingPlanTemplate/section/cause/a1b2c3d4-e5f6-7890-a1b2-c3d4e5f67890

请求体示例1 (直接传入数组):
```json
[
  {"uuid": "c001", "option": "胰岛素分泌不足"}, 
  {"uuid": "c002", "option": "胰岛素抵抗"}, 
  {"uuid": "c003", "option": "饮食控制不当修改后"}, 
  {"uuid": "c004", "option": "运动不足修改后"}
]
```

请求体示例2 (传入包含部分名称的对象):
```json
{
  "cause": [
    {"uuid": "c001", "option": "胰岛素分泌不足"}, 
    {"uuid": "c002", "option": "胰岛素抵抗"}, 
    {"uuid": "c003", "option": "饮食控制不当修改后"}, 
    {"uuid": "c004", "option": "运动不足修改后"}
  ]
}
```

返回示例：
```json
{
  "msg": "操作成功",
  "code": 200,
  "data": 1
}
```

## 获取模版详情
GET /assessment/nursingPlanTemplate/a1b2c3d4-e5f6-7890-a1b2-c3d4e5f67890

返回示例：
```json
{
  "msg": "操作成功",
  "code": 200,
  "data": {
    "id": "a1b2c3d4-e5f6-7890-a1b2-c3d4e5f67890",
    "healthProblem": "血糖控制异常",
    "templateType": 1,
    "details": {
      "cause": [
        {"uuid": "c001", "option": "胰岛素分泌不足"}, 
        {"uuid": "c002", "option": "胰岛素抵抗"}, 
        {"uuid": "c003", "option": "饮食控制不当"}, 
        {"uuid": "c004", "option": "运动不足"}
      ],
      "feature": [
        {"uuid": "f001", "option": "多尿"}, 
        {"uuid": "f002", "option": "多饮"}, 
        {"uuid": "f003", "option": "多食"}, 
        {"uuid": "f004", "option": "体重下降"}, 
        {"uuid": "f005", "option": "空腹血糖>7.0mmol/L"}, 
        {"uuid": "f006", "option": "餐后2小时血糖>11.1mmol/L"}
      ],
      "goal": [
        {"uuid": "g001", "option": "血糖维持在正常或接近正常范围"},
        {"uuid": "g002", "option": "避免低血糖和高血糖的并发症"},
        {"uuid": "g003", "option": "患者能够正确理解饮食控制的重要性"},
        {"uuid": "g004", "option": "患者能够掌握胰岛素注射的正确方法"}
      ],
      "measure": [
        {"uuid": "m001", "option": "监测并记录血糖", "details": [
          {"uuid": "md001", "option": "每日监测空腹血糖"},
          {"uuid": "md002", "option": "餐后2小时监测血糖"}
        ]},
        {"uuid": "m002", "option": "指导合理饮食", "details": [
          {"uuid": "md003", "option": "制定个体化饮食计划"},
          {"uuid": "md004", "option": "控制总热量摄入"}
        ]},
        {"uuid": "m003", "option": "胰岛素治疗", "details": [
          {"uuid": "md005", "option": "按医嘱使用胰岛素"},
          {"uuid": "md006", "option": "教会患者胰岛素注射技术"}
        ]},
        {"uuid": "m004", "option": "增加适当运动"}
      ],
      "evaluation": [
        {"uuid": "e001", "option": "血糖控制良好，空腹血糖<7.0mmol/L"},
        {"uuid": "e002", "option": "无低血糖或高血糖症状"},
        {"uuid": "e003", "option": "患者能够正确执行血糖监测"},
        {"uuid": "e004", "option": "患者能够独立进行胰岛素注射"}
      ]
    },
    "createTime": "2023-07-17 10:00:00",
    "updateTime": "2023-07-17 10:00:00",
    "createBy": "admin",
    "updateBy": "admin"
  }
}
```

## 注意事项
1. 更新section内容时，可以直接传入JSON数组，也可以传入包含部分名称的JSON对象。
2. 使用PUT请求更新部分内容时，需要提供完整的部分内容，不支持部分更新。
3. 数组中的每个对象必须包含uuid和option字段，如果是嵌套结构，可以包含details数组字段。 