ALTER TABLE `ry-custom`.`t_marketing_follow_up`
    DROP COLUMN `plan_title`;

ALTER TABLE `ry-custom`.`t_marketing_follow_up`
    MODIFY COLUMN `plan_time` datetime NULL COMMENT '计划时间' AFTER `customer_id`,
    ADD COLUMN `follower_id` int NULL COMMENT '跟进人id，关联：sys_user' AFTER `follow_up_situation`,
    ADD COLUMN `follower` varchar(255) NULL COMMENT '跟进人' AFTER `follower_id`;

ALTER TABLE `ry-custom`.`t_live_combo_records`
    DROP COLUMN `live_date`,
    DROP COLUMN `expired_date`,
    DROP COLUMN `billing_date`;

CREATE TABLE `t_version_info` (
                                  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
                                  `version_number` int DEFAULT NULL COMMENT '版本号',
                                  `version_name` varchar(255) DEFAULT NULL COMMENT '版本名称',
                                  `description` text COMMENT '版本描述',
                                  `release_date` date DEFAULT NULL COMMENT '发布日期',
                                  `app_download_url` varchar(255) DEFAULT NULL COMMENT 'app下载地址',
                                  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='版本信息表';

ALTER TABLE `ry-custom`.`t_payment_change_record`
    ADD COLUMN `live_id` varchar(64) NULL COMMENT '居住id，关联表：t_live_base_info' AFTER `contract_number`,
    ADD COLUMN `account_add_cost` decimal(10, 2) NULL COMMENT '本次账户变动金额' AFTER `bed_name`;
