ALTER TABLE `ry-custom`.`t_version_info`
    DROP COLUMN `version_name`,
    MODIFY COLUMN `description` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '说明' AFTER `version_number`,
    ADD COLUMN `app_name` varchar(255) NULL COMMENT 'app名称' AFTER `description`,
    MODIFY COLUMN `app_download_url` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'app下载地址' AFTER `description`,
    ADD COLUMN `status` varchar(2) NULL COMMENT '状态， 字典：custom_version_status，0：暂停中，1：使用中' AFTER `release_date`,
    ADD COLUMN `del_flag` varchar(2) NULL COMMENT ' 逻辑删除标记（0：显示；1：隐藏' AFTER `status`;
