INSERT INTO `ry-cloud`.`sys_dict_type` (`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('机构养老-居住信息-缴费方式', 'custom_live_base_info_payment_method', '0', 'admin', '2025-04-12 11:08:13', '', NULL, NULL);

INSERT INTO `ry-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1, '一次性', '1', 'custom_live_base_info_payment_method', NULL, 'default', 'N', '0', 'admin', '2025-04-12 11:08:54', '', NULL, NULL);
INSERT INTO `ry-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( 2, '月', '2', 'custom_live_base_info_payment_method', NULL, 'default', 'N', '0', 'admin', '2025-04-12 11:09:03', '', NULL, NULL);
INSERT INTO `ry-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( 3, '季度', '3', 'custom_live_base_info_payment_method', NULL, 'default', 'N', '0', 'admin', '2025-04-12 11:09:10', '', NULL, NULL);
INSERT INTO `ry-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( 4, '半年', '4', 'custom_live_base_info_payment_method', NULL, 'default', 'N', '0', 'admin', '2025-04-12 11:09:17', '', NULL, NULL);
INSERT INTO `ry-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( 5, '一年', '5', 'custom_live_base_info_payment_method', NULL, 'default', 'N', '0', 'admin', '2025-04-12 11:09:34', '', NULL, NULL);


INSERT INTO `ry-cloud`.`sys_dict_type` (`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('机构养老-费用缴纳-缴费状态', 'custom_payment_status', '0', 'admin', '2025-04-12 12:19:00', '', NULL, NULL);

INSERT INTO `ry-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1, '未缴费', '0', 'custom_payment_status', NULL, 'default', 'N', '0', 'admin', '2025-04-12 12:19:14', '', NULL, NULL);
INSERT INTO `ry-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2, '已缴费', '1', 'custom_payment_status', NULL, 'default', 'N', '0', 'admin', '2025-04-12 12:19:20', '', NULL, NULL);

UPDATE `ry-cloud`.`sys_menu` SET `menu_name` = '费用明细（老）', `parent_id` = 2012, `order_num` = 5, `path` = 'costDetail', `component` = 'costManage/costDetail/index', `query` = NULL, `is_frame` = 1, `is_cache` = 0, `menu_type` = 'C', `visible` = '0', `status` = '0', `perms` = '', `icon` = 'druid', `applet_icon` = NULL, `create_by` = 'admin', `create_time` = '2022-03-30 18:14:15', `update_by` = 'admin', `update_time` = '2025-04-13 11:21:28', `remark` = '', `platform` = '100', `applet_path` = NULL WHERE `menu_id` = 2020;
UPDATE `ry-cloud`.`sys_menu` SET `menu_name` = '费用缴纳（老）', `parent_id` = 2012, `order_num` = 2, `path` = 'costPayment', `component` = 'costManage/costPayment/index', `query` = NULL, `is_frame` = 1, `is_cache` = 0, `menu_type` = 'C', `visible` = '0', `status` = '0', `perms` = '', `icon` = 'component', `applet_icon` = NULL, `create_by` = 'admin', `create_time` = '2022-03-29 16:24:26', `update_by` = 'admin', `update_time` = '2025-04-13 11:15:51', `remark` = '', `platform` = '100', `applet_path` = NULL WHERE `menu_id` = 2015;
INSERT INTO `ry-cloud`.`sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `applet_icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `platform`, `applet_path`) VALUES (3953, '缴费记录', 2012, 7, 'paymentRecords', 'costManage/paymentRecords/index', NULL, 1, 0, 'C', '0', '0', NULL, 'druid', NULL, 'admin', '2025-04-13 11:50:38', '', NULL, '', '100', NULL);
INSERT INTO `ry-cloud`.`sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `applet_icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `platform`, `applet_path`) VALUES (3952, '费用缴纳', 2012, 6, 'paymentOfFee', 'costManage/paymentOfFee/index', NULL, 1, 0, 'C', '0', '0', NULL, 'textarea', NULL, 'admin', '2025-04-13 11:30:08', '', NULL, '', '100', NULL);
UPDATE `ry-cloud`.`sys_menu` SET `menu_name` = '新闻中心', `parent_id` = 3925, `order_num` = 4, `path` = 'news', `component` = '/homePageModule/news/index', `query` = NULL, `is_frame` = 1, `is_cache` = 0, `menu_type` = 'C', `visible` = '1', `status` = '1', `perms` = '', `icon` = 'clipboard', `applet_icon` = NULL, `create_by` = 'admin', `create_time` = '2025-03-23 13:58:00', `update_by` = 'admin', `update_time` = '2025-04-14 10:08:30', `remark` = '', `platform` = '105', `applet_path` = NULL WHERE `menu_id` = 3931;
UPDATE `ry-cloud`.`sys_menu` SET `menu_name` = '乐龄活动', `parent_id` = 3925, `order_num` = 3, `path` = 'seniorActivities', `component` = 'homePageModule/seniorActivities/index', `query` = NULL, `is_frame` = 1, `is_cache` = 0, `menu_type` = 'C', `visible` = '1', `status` = '1', `perms` = '', `icon` = 'email', `applet_icon` = NULL, `create_by` = 'admin', `create_time` = '2025-03-23 13:57:23', `update_by` = 'admin', `update_time` = '2025-04-14 10:08:26', `remark` = '', `platform` = '105', `applet_path` = NULL WHERE `menu_id` = 3930;
