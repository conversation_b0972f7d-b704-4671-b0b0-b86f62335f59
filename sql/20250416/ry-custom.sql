ALTER TABLE `ry-custom`.`t_live_base_info`
    DROP COLUMN `expired_date`,
    DROP COLUMN `discounted_amount`;

ALTER TABLE `ry-custom`.`t_payment_info`
    ADD COLUMN `live_id` varchar(64) NULL COMMENT '居住id，关联表：t_live_base_info' AFTER `elderly_name`,
    ADD COLUMN `del_flag` varchar(2) NULL DEFAULT 0 COMMENT '删除状态，0：显示，1：不显示' AFTER `handler_account`;

CREATE TABLE `ry-custom`.`t_checkout_fee_record` (
                                         `id` varchar(64) NOT NULL COMMENT '主键ID',
                                         `elderly_id` varchar(64) NOT NULL COMMENT '老人ID',
                                         `elderly_name` varchar(100) NOT NULL COMMENT '老人名称',
                                         `live_id` varchar(64) NOT NULL COMMENT '居住ID，关联表：t_live_base_info',
                                         `live_period` varchar(30) DEFAULT NULL COMMENT '入住周期，格式如********-*********',
                                         `paid_amount` decimal(10,2) DEFAULT '0.00' COMMENT '已缴纳金额',
                                         `unpaid_amount` decimal(10,2) DEFAULT '0.00' COMMENT '未缴纳金额',
                                         `supplement_amount` decimal(10,2) DEFAULT '0.00' COMMENT '补缴金额',
                                         `system_refund_amount` decimal(10,2) DEFAULT '0.00' COMMENT '系统计算退费金额',
                                         `actual_refund_amount` decimal(10,2) DEFAULT '0.00' COMMENT '实际退费金额',
                                         `checkout_date` date DEFAULT NULL COMMENT '退租日期',
                                         PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='退租费用记录表';

CREATE TABLE `ry-custom`.`t_checkout_live_record` (
                                          `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                          `checkout_fee_record_id` VARCHAR(64) NOT NULL COMMENT '退租费用记录ID，关联表：t_checkout_fee_record',
                                          `elderly_id` VARCHAR(64) NOT NULL COMMENT '老人ID，关联表：t_elderly_people_info',
                                          `elderly_name` VARCHAR(100) NOT NULL COMMENT '老人姓名',
                                          `contract_end_date` DATE DEFAULT NULL COMMENT '合同到期时间',
                                          `checkin_date` DATE DEFAULT NULL COMMENT '入住日期',
                                          `billing_date` DATE DEFAULT NULL COMMENT '计费日期',
                                          `checkout_date` DATE DEFAULT NULL COMMENT '退住日期',
                                          `bed_name` VARCHAR(100) DEFAULT NULL COMMENT '入住床位',
                                          `care_level` VARCHAR(4) DEFAULT NULL COMMENT '护理等级ID，字典：care_level',
                                          `combo_id` BIGINT DEFAULT NULL COMMENT '护理套餐ID',
                                          `combo_name` VARCHAR(100) DEFAULT NULL COMMENT '护理套餐',
                                          `contract_duration` int DEFAULT NULL COMMENT '合同时长',
                                          `payment_method` VARCHAR(20) DEFAULT NULL COMMENT '缴费方式，字典：custom_live_base_info_payment_method',
                                          `remark` VARCHAR(500) DEFAULT NULL COMMENT '备注',
                                          PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='退租居住记录表';

ALTER TABLE `ry-custom`.`t_marketing_customer_info`
    ADD COLUMN `del_flag` varchar(2) NULL COMMENT '逻辑删除标记（0：显示；1：隐藏' AFTER `direct_check_in`;
