/*
 Navicat Premium Data Transfer

 Source Server         : zmd-test-root
 Source Server Type    : MySQL
 Source Server Version : 80024
 Source Host           : localhost:23307
 Source Schema         : ry-config

 Target Server Type    : MySQL
 Target Server Version : 80024
 File Encoding         : 65001

 Date: 18/03/2025 17:03:01
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for config_info
-- ----------------------------
DROP TABLE IF EXISTS `config_info`;
CREATE TABLE `config_info`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `data_id` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT 'data_id',
  `group_id` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `content` longtext CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT 'content',
  `md5` varchar(32) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT 'md5',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  `src_user` text CHARACTER SET utf8 COLLATE utf8_bin NULL COMMENT 'source user',
  `src_ip` varchar(50) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT 'source ip',
  `app_name` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `tenant_id` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT '' COMMENT '租户字段',
  `c_desc` varchar(256) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `c_use` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `effect` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `type` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `c_schema` text CHARACTER SET utf8 COLLATE utf8_bin NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_configinfo_datagrouptenant`(`data_id` ASC, `group_id` ASC, `tenant_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 241 CHARACTER SET = utf8 COLLATE = utf8_bin COMMENT = 'config_info' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of config_info
-- ----------------------------
INSERT INTO `config_info` VALUES (1, 'application-dev.yml', 'DEFAULT_GROUP', '# 本地文件上传    \nfile:\n    #domain: http://127.0.0.1:9300\n  domain: http://*************:8080/file\n  back-domain: http://*************:8080/file\n  path: D:/ruoyi/uploadPath\n  prefix: /statics\nspring:\n  main:\n    allow-circular-references: true\n    allow-bean-definition-overriding: true\n  autoconfigure:\n    exclude: com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure\n  mvc:\n    pathmatch:\n      matching-strategy: ant_path_matcher\n  cloud:\n    sentinel:\n      filter:\n        # sentinel 在 springboot 2.6.x 不兼容问题的处理\n        enabled: false\n  servlet:\n    multipart:\n      max-request-size: 100MB\n      max-file-size: 100MB\n\n# feign 配置\nfeign:\n  sentinel:\n    enabled: true\n  okhttp:\n    enabled: true\n  httpclient:\n    enabled: false\n  client:\n    config:\n      default:\n        connectTimeout: 10000\n        readTimeout: 10000\n  compression:\n    request:\n      enabled: true\n    response:\n      enabled: true\n\n# 暴露监控端点\nmanagement:\n  endpoints:\n    web:\n      exposure:\n        include: \'*\'\n', '9bf1c3c6d66853fb6b7e1187c42c377b', '2020-05-20 12:00:00', '2024-11-12 03:43:15', 'nacos', '*************', '', '', '', '', '', 'yaml', '');
INSERT INTO `config_info` VALUES (2, 'ruoyi-gateway-dev.yml', 'DEFAULT_GROUP', 'spring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n  cloud:\n    gateway:\n      discovery:\n        locator:\n          lowerCaseServiceId: true\n          enabled: true\n      routes:\n        # 认证中心\n        - id: ruoyi-auth\n          uri: lb://ruoyi-auth\n          predicates:\n            - Path=/auth/**\n          filters:\n            # 验证码处理\n            - CacheRequestFilter\n            - ValidateCodeFilter\n            - StripPrefix=1\n        # 代码生成\n        - id: ruoyi-gen\n          uri: lb://ruoyi-gen\n          predicates:\n            - Path=/code/**\n          filters:\n            - StripPrefix=1\n        # 定时任务\n        - id: ruoyi-job\n          uri: lb://ruoyi-job\n          predicates:\n            - Path=/schedule/**\n          filters:\n            - StripPrefix=1\n        # 系统模块\n        - id: ruoyi-system\n          uri: lb://ruoyi-system\n          predicates:\n            - Path=/system/**\n          filters:\n            - StripPrefix=1\n        # 文件服务\n        - id: ruoyi-file\n          uri: lb://ruoyi-file\n          predicates:\n            - Path=/file/**\n          filters:\n            - StripPrefix=1\n        # 客户服务\n        - id: ruoyi-custom\n          uri: lb://ruoyi-custom\n          predicates:\n            - Path=/custom/**\n          filters:\n            - StripPrefix=1\n        # 居家服务\n        - id: ruoyi-homecare\n          uri: lb://ruoyi-homecare\n          predicates:\n            - Path=/homecare/**\n          filters:\n            - StripPrefix=1\n        # 日间照料服务\n        - id: ruoyi-daycare\n          uri: lb://ruoyi-daycare\n          predicates:\n            - Path=/daycare/**\n          filters:\n            - StripPrefix=1\n# 安全配置\nsecurity:\n  # 验证码\n  captcha:\n    enabled: false\n    type: math\n  # 防止XSS攻击\n  xss:\n    enabled: true\n    excludeUrls:\n      - /system/notice\n  # 不校验白名单\n  ignore:\n    whites:\n      - /auth/logout\n      - /auth/login\n      - /auth/register\n      - /*/v2/api-docs\n      - /csrf\n      - /custom/careWorkerInfo/careLogin\n      - /system/dict/data/type/**\n      - /homecare/homeCommunityBaseInfo/getCommunityList\n      - /file/**\n      - /homecare/HomeRegisterServiceProvider/registerServiceProvider\n      - /homecare/appApi/getOpenIdByCode\n      - /homecare/appApi/getTokenByOpenId\n      - /homecare/homeServiceProviderManagement/getIgnoreServiceProviderLabel\n      - /homecare/wechatPay/notify/**\n      - /auth/appLogin\n      - /auth/businessLogin\n      - /homecare/appApi/xxx/onWxMsg\n      - /system/dict/data/type/**\n      - /homecare/homeCommunityBaseInfo/getCommunityList\n      - /homecare/homeTimeCoinSettings/getSettingsList\n      - /homecare/homeStreetInfo/list\n      - /homecare/homeAppServicePlaceOrderVolunteerInfo/register\n      - /homecare/homeStreetInfo/ignoreWhiteTreeselect\n      - /homecare/appApi/reserve/login\n      - /homecare/appApi/reserve/getOpenIdByCode\n      - /homecare/appApi/reserve/getTokenByOpenId\n      - /homecare/statics/**\n      - /homecare/homeStreetInfo/threeList\n      - /system/notice/**', '4af97274fbc43d9484ff016671a28ad9', '2020-05-14 14:17:55', '2023-06-30 02:39:47', 'nacos', '127.0.0.1', '', '', '网关模块', 'null', 'null', 'yaml', 'null');
INSERT INTO `config_info` VALUES (3, 'ruoyi-auth-dev.yml', 'DEFAULT_GROUP', 'spring: \n  redis:\n    host: localhost\n    port: 6379\n    password: \n# swagger配置\nswagger:\n  title: 权限接口文档\n  license: Powered By ruoyi\n  licenseUrl: https://ruoyi.vip', 'b766cb5c59f7c4a6cc5362fb52cff7fd', '2020-11-20 00:00:00', '2023-03-06 09:07:44', 'nacos', '127.0.0.1', '', '', '认证中心', 'null', 'null', 'yaml', 'null');
INSERT INTO `config_info` VALUES (4, 'ruoyi-monitor-dev.yml', 'DEFAULT_GROUP', '# spring\r\nspring: \r\n  security:\r\n    user:\r\n      name: ruoyi\r\n      password: 123456\r\n  boot:\r\n    admin:\r\n      ui:\r\n        title: 若依服务状态监控\r\n', 'd8997d0707a2fd5d9fc4e8409da38129', '2020-11-20 00:00:00', '2020-12-21 16:28:07', NULL, '0:0:0:0:0:0:0:1', '', '', '监控中心', 'null', 'null', 'yaml', 'null');
INSERT INTO `config_info` VALUES (5, 'ruoyi-system-dev.yml', 'DEFAULT_GROUP', '# spring配置\nspring: \n  redis:\n    host: localhost\n    port: 6379\n    password: \n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: admin\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: jdbc:mysql://**************:23336/ry-cloud?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8\n            username: root\n            password: dabojin_2022\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n      # seata: true    # 开启seata代理，开启后默认每个数据源都代理，如果某个不需要代理可单独关闭\n\n# seata配置\nseata:\n  # 默认关闭，如需启用spring.datasource.dynami.seata需要同时开启\n  enabled: false\n  # Seata 应用编号，默认为 ${spring.application.name}\n  application-id: ${spring.application.name}\n  # Seata 事务组编号，用于 TC 集群名\n  tx-service-group: ${spring.application.name}-group\n  # 关闭自动代理\n  enable-auto-data-source-proxy: false\n  # 服务配置项\n  service:\n    # 虚拟组和分组的映射\n    vgroup-mapping:\n      ruoyi-system-group: default\n  config:\n    type: nacos\n    nacos:\n      serverAddr: 127.0.0.1:8848\n      group: SEATA_GROUP\n      namespace:\n  registry:\n    type: nacos\n    nacos:\n      application: seata-server\n      server-addr: 127.0.0.1:8848\n      namespace:\n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.ruoyi.system.api.domain.SysDictData,com.ruoyi.system.**\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/*.xml\n\n# swagger配置\nswagger:\n  title: 系统模块接口文档\n  license: Powered By ruoyi\n  licenseUrl: https://ruoyi.vip', '5db6d6d6c5be3fa2942f1210b1e1aafa', '2020-11-20 00:00:00', '2023-03-06 09:07:55', 'nacos', '127.0.0.1', '', '', '系统模块', 'null', 'null', 'yaml', 'null');
INSERT INTO `config_info` VALUES (6, 'ruoyi-gen-dev.yml', 'DEFAULT_GROUP', '# spring配置\nspring: \n  redis:\n    host: localhost\n    port: 6379\n    password: \n  datasource: \n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: jdbc:mysql://**************:23336/ry-cloud?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8\n    username: root\n    password: dabojin_2022\n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.ruoyi.gen.domain\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/*.xml\n\n# swagger配置\nswagger:\n  title: 代码生成接口文档\n  license: Powered By ruoyi\n  licenseUrl: https://ruoyi.vip\n\n# 代码生成\ngen: \n  # 作者\n  author: zkx\n  # 默认生成包路径 system 需改成自己的模块名称 如 system monitor tool\n  packageName: com.ruoyi.custom\n  # 自动去除表前缀，默认是false\n  autoRemovePre: true\n  # 表前缀（生成类名不会包含表前缀，多个用逗号分隔）\n  tablePrefix: sys_,t_\n', '6db7a5dbf1442d956d5ce716265ece71', '2020-11-20 00:00:00', '2024-11-15 03:39:34', 'nacos', '*************', '', '', '代码生成', 'null', 'null', 'yaml', 'null');
INSERT INTO `config_info` VALUES (7, 'ruoyi-job-dev.yml', 'DEFAULT_GROUP', '# spring配置\nspring: \n  redis:\n    host: localhost\n    port: 6379\n    password: \n  datasource:\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: jdbc:mysql://**************:23336/ry-cloud?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8\n    username: root\n    password: dabojin_2022\n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.ruoyi.job.domain\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/*.xml\n\n# swagger配置\nswagger:\n  title: 定时任务接口文档\n  license: Powered By ruoyi\n  licenseUrl: https://ruoyi.vip\n', '8b3f224adb1f0ee2843cff648d70418f', '2020-11-20 00:00:00', '2022-06-22 07:23:59', 'nacos', '0:0:0:0:0:0:0:1', '', '', '定时任务', 'null', 'null', 'yaml', 'null');
INSERT INTO `config_info` VALUES (8, 'ruoyi-file-dev.yml', 'DEFAULT_GROUP', '# FastDFS配置\nfdfs:\n  domain: http://************\n  soTimeout: 3000\n  connectTimeout: 2000\n  trackerList: ************:22122\n\n# Minio配置\nminio:\n  url: http://************:9000\n  accessKey: minioadmin\n  secretKey: minioadmin\n  bucketName: test', '24acac202ec084eea1b0f94ce34cd372', '2020-11-20 00:00:00', '2023-03-06 09:08:08', 'nacos', '127.0.0.1', '', '', '文件服务', 'null', 'null', 'yaml', 'null');
INSERT INTO `config_info` VALUES (9, 'sentinel-ruoyi-gateway', 'DEFAULT_GROUP', '[\r\n    {\r\n        \"resource\": \"ruoyi-auth\",\r\n        \"count\": 500,\r\n        \"grade\": 1,\r\n        \"limitApp\": \"default\",\r\n        \"strategy\": 0,\r\n        \"controlBehavior\": 0\r\n    },\r\n	{\r\n        \"resource\": \"ruoyi-system\",\r\n        \"count\": 1000,\r\n        \"grade\": 1,\r\n        \"limitApp\": \"default\",\r\n        \"strategy\": 0,\r\n        \"controlBehavior\": 0\r\n    },\r\n	{\r\n        \"resource\": \"ruoyi-gen\",\r\n        \"count\": 200,\r\n        \"grade\": 1,\r\n        \"limitApp\": \"default\",\r\n        \"strategy\": 0,\r\n        \"controlBehavior\": 0\r\n    },\r\n	{\r\n        \"resource\": \"ruoyi-job\",\r\n        \"count\": 300,\r\n        \"grade\": 1,\r\n        \"limitApp\": \"default\",\r\n        \"strategy\": 0,\r\n        \"controlBehavior\": 0\r\n    }\r\n]', '9f3a3069261598f74220bc47958ec252', '2020-11-20 00:00:00', '2020-11-20 00:00:00', NULL, '0:0:0:0:0:0:0:1', '', '', '限流策略', 'null', 'null', 'json', 'null');
INSERT INTO `config_info` VALUES (14, 'ruoyi-custom-dev.yml', 'DEFAULT_GROUP', '# spring配置\nspring: \n  servlet:\n    multipart:\n      maxFileSize:  1000MB\n      maxRequestSize: 1000MB\n  main:\n    allow-circular-references: true\n    allow-bean-definition-overriding: true\n  redis:\n    host: localhost\n    port: 6379\n    password: \n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: admin\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: jdbc:mysql://**************:23336/ry-cloud?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8&allowMultiQueries=true\n            username: root\n            password: dabojin_2022\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n      # seata: true    # 开启seata代理，开启后默认每个数据源都代理，如果某个不需要代理可单独关闭\n\n# seata配置\nseata:\n  # 默认关闭，如需启用spring.datasource.dynami.seata需要同时开启\n  enabled: false\n  # Seata 应用编号，默认为 ${spring.application.name}\n  application-id: ${spring.application.name}\n  # Seata 事务组编号，用于 TC 集群名\n  tx-service-group: ${spring.application.name}-group\n  # 关闭自动代理\n  enable-auto-data-source-proxy: false\n  # 服务配置项\n  service:\n    # 虚拟组和分组的映射\n    vgroup-mapping:\n      ruoyi-system-group: default\n  config:\n    type: nacos\n    nacos:\n      serverAddr: 127.0.0.1:8848\n      group: SEATA_GROUP\n      namespace:\n  registry:\n    type: nacos\n    nacos:\n      application: seata-server\n      server-addr: 127.0.0.1:8848\n      namespace:\n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.ruoyi.custom.**\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/**/*.xml\n    configuration:\n       map-underscore-to-camel-case: true\n\nmybatis-plus:\n    #配置Mapper映射文件\n    mapper-locations: classpath:mapper/**/**/*.xml\n    # 配置Mybatis数据返回类型别名（默认别名为类名）\n    type-aliases-package: com.ruoyi.custom.**\n    configuration:\n        # 自动驼峰命名\n        map-underscore-to-camel-case: true   \n\nlogging:\n  level:\n    root: INFO\n    com.ruoyi: debug\n    com.github.binarywang.demo.wx.pay: debug\n    com.github.binarywang.wxpay: debug\n\n# swagger配置\nswagger:\n  title: 客户模块接口文档\n  license: Powered By ruoyi\n  licenseUrl: https://ruoyi.vip\n\n# 编号前缀\nnumber.prefix:\n  wzck: YCJS-WZCK    # 物资出库编号前缀\n  wzrk: YCJS-WZRK    # 物资入库编号前缀\n  wzpd: YCJS-WZPD    # 物资盘点编号前缀', '29bd5dd7a22a6a6fc406cfbc55dc913b', '2022-03-23 06:04:31', '2023-12-05 09:00:19', 'nacos', '127.0.0.1', '', '', '客户模块', '', '', 'yaml', '');
INSERT INTO `config_info` VALUES (18, 'ruoyi-homecare-dev.yml', 'DEFAULT_GROUP', '# spring配置\nspring: \n  rabbitmq:\n    host: 127.0.0.1\n    port: 5672\n    username: guest\n    password: guest\n  servlet:\n    multipart:\n      maxFileSize:  1000MB\n      maxRequestSize: 1000MB\n  main:\n    allow-circular-references: true\n    allow-bean-definition-overriding: true\n  redis:\n    host: localhost\n    port: 6379\n    password: \n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: admin\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: jdbc:mysql://**************:23336/ry-homecare?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8&allowMultiQueries=true\n            username: root\n            password: dabojin_2022\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n      # seata: true    # 开启seata代理，开启后默认每个数据源都代理，如果某个不需要代理可单独关闭\n\n# seata配置\nseata:\n  # 默认关闭，如需启用spring.datasource.dynami.seata需要同时开启\n  enabled: false\n  # Seata 应用编号，默认为 ${spring.application.name}\n  application-id: ${spring.application.name}\n  # Seata 事务组编号，用于 TC 集群名\n  tx-service-group: ${spring.application.name}-group\n  # 关闭自动代理\n  enable-auto-data-source-proxy: false\n  # 服务配置项\n  service:\n    # 虚拟组和分组的映射\n    vgroup-mapping:\n      ruoyi-system-group: default\n  config:\n    type: nacos\n    nacos:\n      serverAddr: 127.0.0.1:8848\n      group: SEATA_GROUP\n      namespace:\n  registry:\n    type: nacos\n    nacos:\n      application: seata-server\n      server-addr: 127.0.0.1:8848\n      namespace:\n\n#mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.ruoyi.homecare.**\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/**/*.xml\n    configuration:\n       map-underscore-to-camel-case: true\n\nmybatis-plus:\n    #配置Mapper映射文件\n    mapper-locations: classpath:mapper/**/**/*.xml\n    # 配置Mybatis数据返回类型别名（默认别名为类名）\n    type-aliases-package: com.ruoyi.homecare.**\n    configuration:\n        # 自动驼峰命名\n        map-underscore-to-camel-case: true   \n        \nlogging:\n  level:\n    root: info\n    com.ruoyi: debug\n    com.github.binarywang.demo.wx.pay: debug\n    com.github.binarywang.wxpay: debug\n\n# swagger配置\nswagger:\n  title: jujiayanglaofuwu\n  license: Powered By ruoyi\n  licenseUrl: https://ruoyi.vip\n\nwx:\n  #小程序密钥\n  #v2 \n  appsecret: 0e5db3fd3cc6197dd68a785282dc1623\n  # appsecret: 78f1f354f767746171bcf957fb3fd3ec\n  pay:\n    appId: wxb7125f4f902fb0fe\n    # appid: wxa89aa4c24fca8ad4\n    mchId: **********\n    mchKey: dadba017qa3c7q4a8aq96bcq32df7fc5\n    apiV3Key: 2cc9f595e4d2616s865d1aa7wa13fbf4\n\nxcx:\n appid: wxb7125f4f902fb0fe\n appsecret: 0e5db3fd3cc6197dd68a785282dc1623\ngzh:\n appid: wx4ab92103bfd594e7\n appsecret: 6f297693d2cc0225a16e2f0ffdcece0c\n #  体育场馆小程序\nreserve:\n # appid: wx4ab92103bfd594e7\n # appsecret: 6f297693d2cc0225a16e2f0ffdcece0c\n appid: wxa89aa4c24fca8ad4\n appsecret: 78f1f354f767746171bcf957fb3fd3ec\n    \n\nfile:\n  prefix: /statics\n  domain: http://*************:8080\n\n\n', 'aba30525c19652dcb643c377ecff364b', '2022-06-08 08:41:59', '2023-12-05 09:16:18', 'nacos', '127.0.0.1', '', '', '', '', '', 'yaml', '');
INSERT INTO `config_info` VALUES (77, 'ruoyi-daycare-dev.yml', 'DEFAULT_GROUP', '# spring配置\nspring: \n  servlet:\n    multipart:\n      maxFileSize:  1000MB\n      maxRequestSize: 1000MB\n  main:\n    allow-circular-references: true\n    allow-bean-definition-overriding: true\n  redis:\n    host: localhost\n    port: 6379\n    password: \n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: admin\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: jdbc:mysql://**************:23336/ry-daycare?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8&allowMultiQueries=true\n            username: root\n            password: dabojin_2022\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n      # seata: true    # 开启seata代理，开启后默认每个数据源都代理，如果某个不需要代理可单独关闭\n\n# seata配置\nseata:\n  # 默认关闭，如需启用spring.datasource.dynami.seata需要同时开启\n  enabled: false\n  # Seata 应用编号，默认为 ${spring.application.name}\n  application-id: ${spring.application.name}\n  # Seata 事务组编号，用于 TC 集群名\n  tx-service-group: ${spring.application.name}-group\n  # 关闭自动代理\n  enable-auto-data-source-proxy: false\n  # 服务配置项\n  service:\n    # 虚拟组和分组的映射\n    vgroup-mapping:\n      ruoyi-system-group: default\n  config:\n    type: nacos\n    nacos:\n      serverAddr: 127.0.0.1:8848\n      group: SEATA_GROUP\n      namespace:\n  registry:\n    type: nacos\n    nacos:\n      application: seata-server\n      server-addr: 127.0.0.1:8848\n      namespace:\n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.ruoyi.daycare.**\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/**/*.xml\n    configuration:\n       map-underscore-to-camel-case: true\nlogging:\n  level:\n    root: INFO\n    com.ruoyi: debug\n    com.github.binarywang.demo.wx.pay: debug\n    com.github.binarywang.wxpay: debug\n\n# swagger配置\nswagger:\n  title: 日间照料模块接口文档\n  license: Powered By ruoyi\n  licenseUrl: https://ruoyi.vip', 'a6833833cceffbd667f52e83f8173fa1', '2022-08-31 02:38:11', '2022-08-31 03:34:12', 'nacos', '127.0.0.1', '', '', '日间照料模块', '', '', 'yaml', '');
INSERT INTO `config_info` VALUES (101, 'sentinel-ruoyi-gateway', 'DEFAULT_GROUP', '[\r\n    {\r\n        \"resource\": \"ruoyi-auth\",\r\n        \"count\": 500,\r\n        \"grade\": 1,\r\n        \"limitApp\": \"default\",\r\n        \"strategy\": 0,\r\n        \"controlBehavior\": 0\r\n    },\r\n	{\r\n        \"resource\": \"ruoyi-system\",\r\n        \"count\": 1000,\r\n        \"grade\": 1,\r\n        \"limitApp\": \"default\",\r\n        \"strategy\": 0,\r\n        \"controlBehavior\": 0\r\n    },\r\n	{\r\n        \"resource\": \"ruoyi-gen\",\r\n        \"count\": 200,\r\n        \"grade\": 1,\r\n        \"limitApp\": \"default\",\r\n        \"strategy\": 0,\r\n        \"controlBehavior\": 0\r\n    },\r\n	{\r\n        \"resource\": \"ruoyi-job\",\r\n        \"count\": 300,\r\n        \"grade\": 1,\r\n        \"limitApp\": \"default\",\r\n        \"strategy\": 0,\r\n        \"controlBehavior\": 0\r\n    }\r\n]', '9f3a3069261598f74220bc47958ec252', '2024-11-11 02:19:25', '2024-11-11 02:19:25', NULL, '*************', '', 'ecf1cef0-187f-4968-9c1e-fde5aa6c24e6', '限流策略', NULL, NULL, 'json', NULL);
INSERT INTO `config_info` VALUES (102, 'ruoyi-homecare-local.yml', 'DEFAULT_GROUP', '# spring\nspring: \n  rabbitmq:\n    host: 127.0.0.1\n    port: 5672\n    username: guest\n    password: guest\n  servlet:\n    multipart:\n      maxFileSize:  1000MB\n      maxRequestSize: 1000MB\n  main:\n    allow-circular-references: true\n    allow-bean-definition-overriding: true\n  redis:\n    host: localhost\n    port: 6379\n    password: \n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: admin\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # \n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: ******************************************************************************************************************************************************************************            username: root\n            password: dabojin_2022\n\n# seata\nseata:\n  # \n  enabled: false\n  # \n  application-id: ${spring.application.name}\n  # \n  tx-service-group: ${spring.application.name}-group\n  # \n  enable-auto-data-source-proxy: false\n  # \n  service:\n    # \n    vgroup-mapping:\n      ruoyi-system-group: default\n  config:\n    type: nacos\n    nacos:\n      serverAddr: 127.0.0.1:8848\n      group: SEATA_GROUP\n      namespace:\n  registry:\n    type: nacos\n    nacos:\n      application: seata-server\n      server-addr: 127.0.0.1:8848\n      namespace:\n\n# \n#mybatis:\n    # \n   # typeAliasesPackage: com.ruoyi.homecare.**\n    # \n    #mapperLocations: classpath:mapper/**/**/*.xml\n    #configuration:\n     #  map-underscore-to-camel-case: true\nmybatis-plus:\n    #\n    mapper-locations: classpath:mapper/**/**/*.xml\n    # \n    type-aliases-package: com.ruoyi.homecare.**\n    configuration:\n        # \n        map-underscore-to-camel-case: true  \n\nlogging:\n  level:\n    root: INFO\n    com.ruoyi: debug\n\n\n# swagger\nswagger:\n  title: jujiayanglao\n  license: Powered By ruoyi\n  licenseUrl: https://ruoyi.vip\n\n\nwx:\n  #\n  #v2 \n  appsecret: 0e5db3fd3cc6197dd68a785282dc1623\n  # appsecret: 78f1f354f767746171bcf957fb3fd3ec\n  pay:\n    appId: wxb7125f4f902fb0fe\n    # appid: wxa89aa4c24fca8ad4\n    mchId: **********\n    mchKey: dadba017qa3c7q4a8aq96bcq32df7fc5\n    apiV3Key: 2cc9f595e4d2616s865d1aa7wa13fbf4\n\nxcx:\n  appid: wxb7125f4f902fb0fe\n  appsecret: 0e5db3fd3cc6197dd68a785282dc1623\ngzh:\n  appid: wx4ab92103bfd594e7\n  appsecret: 6f297693d2cc0225a16e2f0ffdcece0c\nreserve:\n # appid: wx4ab92103bfd594e7\n # appsecret: 6f297693d2cc0225a16e2f0ffdcece0c\n  appid: wxa89aa4c24fca8ad4\n  appsecret: 78f1f354f767746171bcf957fb3fd3ec\n\nfile:\n  prefix: /statics\n  domain: http://**************:23360/file\n  path: /path', '133f0f24ddd8dec53d77828e32cc26c9', '2024-11-11 02:19:25', '2024-11-11 02:19:25', NULL, '*************', '', 'ecf1cef0-187f-4968-9c1e-fde5aa6c24e6', '', NULL, NULL, 'yaml', NULL);
INSERT INTO `config_info` VALUES (103, 'ruoyi-daycare-local.yml', 'DEFAULT_GROUP', '# spring配置\nspring: \n  servlet:\n    multipart:\n      maxFileSize:  1000MB\n      maxRequestSize: 1000MB\n  main:\n    allow-circular-references: true\n    allow-bean-definition-overriding: true\n  redis:\n    host: localhost\n    port: 6379\n    password: \n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: admin\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: *****************************************************************************************************************************************************************************            username: root\n            password: dabojin_2022\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n      # seata: true    # 开启seata代理，开启后默认每个数据源都代理，如果某个不需要代理可单独关闭\n\n# seata配置\nseata:\n  # 默认关闭，如需启用spring.datasource.dynami.seata需要同时开启\n  enabled: false\n  # Seata 应用编号，默认为 ${spring.application.name}\n  application-id: ${spring.application.name}\n  # Seata 事务组编号，用于 TC 集群名\n  tx-service-group: ${spring.application.name}-group\n  # 关闭自动代理\n  enable-auto-data-source-proxy: false\n  # 服务配置项\n  service:\n    # 虚拟组和分组的映射\n    vgroup-mapping:\n      ruoyi-system-group: default\n  config:\n    type: nacos\n    nacos:\n      serverAddr: 127.0.0.1:8848\n      group: SEATA_GROUP\n      namespace:\n  registry:\n    type: nacos\n    nacos:\n      application: seata-server\n      server-addr: 127.0.0.1:8848\n      namespace:\n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.ruoyi.daycare.**\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/**/*.xml\n    configuration:\n       map-underscore-to-camel-case: true\nlogging:\n  level:\n    root: INFO\n    com.ruoyi: debug\n    com.github.binarywang.demo.wx.pay: debug\n    com.github.binarywang.wxpay: debug\n\n# swagger配置\nswagger:\n  title: 日间照料模块接口文档\n  license: Powered By ruoyi\n  licenseUrl: https://ruoyi.vip', 'f1f2602fc0e412a9f330041469063a2f', '2024-11-11 02:19:25', '2024-11-11 02:19:25', NULL, '*************', '', 'ecf1cef0-187f-4968-9c1e-fde5aa6c24e6', '日间照料模块', NULL, NULL, 'yaml', NULL);
INSERT INTO `config_info` VALUES (104, 'application-local.yml', 'DEFAULT_GROUP', '#     \nfile:\n    #domain: http://127.0.0.1:9300\n    domain: http://*************:8080/file\n    path: D:/ruoyi/uploadPath\n    prefix: /statics\nspring:\n  main:\n    allow-circular-references: true\n    allow-bean-definition-overriding: true\n  autoconfigure:\n    exclude: com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure\n  mvc:\n    pathmatch:\n      matching-strategy: ant_path_matcher\n  cloud:\n    sentinel:\n      filter:\n        # \n        enabled: false\n  servlet:\n    multipart:\n      max-request-size: 100MB\n      max-file-size: 100MB\n\n# \nfeign:\n  sentinel:\n    enabled: true\n  okhttp:\n    enabled: true\n  httpclient:\n    enabled: false\n  client:\n    config:\n      default:\n        connectTimeout: 10000\n        readTimeout: 10000\n  compression:\n    request:\n      enabled: true\n    response:\n      enabled: true\n\n# \nmanagement:\n  endpoints:\n    web:\n      exposure:\n        include: \'*\'\n', 'b8423876c428ed2e6f019861767703df', '2024-11-11 02:19:25', '2024-11-11 02:21:34', 'nacos', '*************', '', 'ecf1cef0-187f-4968-9c1e-fde5aa6c24e6', '通用配置', '', '', 'yaml', '');
INSERT INTO `config_info` VALUES (105, 'ruoyi-gateway-local.yml', 'DEFAULT_GROUP', 'spring:\n  redis:\n    host: **************\n    port: 23405\n    password: \n  cloud:\n    gateway:\n      discovery:\n        locator:\n          lowerCaseServiceId: true\n          enabled: true\n      routes:\n        # 认证中心\n        - id: ruoyi-auth\n          uri: lb://ruoyi-auth\n          predicates:\n            - Path=/auth/**\n          filters:\n            # 验证码处理\n            - CacheRequestFilter\n            - ValidateCodeFilter\n            - StripPrefix=1\n        # 代码生成\n        - id: ruoyi-gen\n          uri: lb://ruoyi-gen\n          predicates:\n            - Path=/code/**\n          filters:\n            - StripPrefix=1\n        # 定时任务\n        - id: ruoyi-job\n          uri: lb://ruoyi-job\n          predicates:\n            - Path=/schedule/**\n          filters:\n            - StripPrefix=1\n        # 系统模块\n        - id: ruoyi-system\n          uri: lb://ruoyi-system\n          predicates:\n            - Path=/system/**\n          filters:\n            - StripPrefix=1\n        # 文件服务\n        - id: ruoyi-file\n          uri: lb://ruoyi-file\n          predicates:\n            - Path=/file/**\n          filters:\n            - StripPrefix=1\n        # 客户服务\n        - id: ruoyi-custom\n          uri: lb://ruoyi-custom\n          predicates:\n            - Path=/custom/**\n          filters:\n            - StripPrefix=1\n        # 居家服务\n        - id: ruoyi-homecare\n          uri: lb://ruoyi-homecare\n          predicates:\n            - Path=/homecare/**\n          filters:\n            - StripPrefix=1\n        # 日间照料服务\n        - id: ruoyi-daycare\n          uri: lb://ruoyi-daycare\n          predicates:\n            - Path=/daycare/**\n          filters:\n            - StripPrefix=1\n\n# 安全配置\nsecurity:\n  # 验证码\n  captcha:\n    enabled: false\n    type: math\n  # 防止XSS攻击\n  xss:\n    enabled: true\n    excludeUrls:\n      - /system/notice\n  # 不校验白名单\n  ignore:\n    whites:\n      - /auth/logout\n      - /auth/login\n      - /auth/register\n      - /*/v2/api-docs\n      - /csrf\n      - /custom/careWorkerInfo/careLogin\n      - /system/dict/data/type/**\n      - /homecare/homeCommunityBaseInfo/getCommunityList\n      - /file/**\n      - /homecare/HomeRegisterServiceProvider/registerServiceProvider\n      - /homecare/homeServiceProviderManagement/getServiceProviderLabel\n      - /homecare/appApi/getOpenIdByCode\n      - /homecare/appApi/getTokenByOpenId\n      - /homecare/homeServiceProviderManagement/getIgnoreServiceProviderLabel\n      - /homecare/wechatPay/notify/**\n      - /auth/appLogin\n      - /auth/businessLogin\n      - /auth/statics/**\n      - /homecare/appApi/xxx/onWxMsg\n      - /system/dict/data/type/**\n      - /homecare/homeCommunityBaseInfo/getCommunityList\n      - /homecare/homeTimeCoinSettings/getSettingsList\n      - /homecare/homeStreetInfo/list\n      - /homecare/homeAppServicePlaceOrderVolunteerInfo/register\n      - /homecare/homeStreetInfo/ignoreWhiteTreeselect\n      - /homecare/appApi/reserve/login\n      - /homecare/appApi/reserve/getOpenIdByCode\n      - /homecare/appApi/reserve/getTokenByOpenId\n      - /homecare/statics/**', '41a608b3c084dfbf170a54dc93e1d308', '2024-11-11 02:19:25', '2024-11-11 02:22:13', 'nacos', '*************', '', 'ecf1cef0-187f-4968-9c1e-fde5aa6c24e6', '网关模块', '', '', 'yaml', '');
INSERT INTO `config_info` VALUES (106, 'ruoyi-auth-local.yml', 'DEFAULT_GROUP', 'spring: \n  redis:\n    host: **************\n    port: 23405\n    password: \n# swagger配置\nswagger:\n  title: 权限接口文档\n  license: Powered By ruoyi\n  licenseUrl: https://ruoyi.vip', '0ba1168689b3ec4982d30568374735d4', '2024-11-11 02:19:25', '2024-11-11 02:22:38', 'nacos', '*************', '', 'ecf1cef0-187f-4968-9c1e-fde5aa6c24e6', '认证中心', '', '', 'yaml', '');
INSERT INTO `config_info` VALUES (107, 'ruoyi-monitor-local.yml', 'DEFAULT_GROUP', '# spring\r\nspring: \r\n  security:\r\n    user:\r\n      name: ruoyi\r\n      password: 123456\r\n  boot:\r\n    admin:\r\n      ui:\r\n        title: 若依服务状态监控\r\n', 'd8997d0707a2fd5d9fc4e8409da38129', '2024-11-11 02:19:25', '2024-11-11 02:19:25', NULL, '*************', '', 'ecf1cef0-187f-4968-9c1e-fde5aa6c24e6', '监控中心', NULL, NULL, 'yaml', NULL);
INSERT INTO `config_info` VALUES (109, 'ruoyi-gen-local.yml', 'DEFAULT_GROUP', '# spring配置\nspring: \n  redis:\n    host: **************\n    port: 23405\n    password: \n  datasource: \n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: jdbc:mysql://**************:23403/ry-cloud?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8\n    username: ry-cloud\n    password: rNsALTSiyXSWKczx\n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.ruoyi.gen.domain\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/*.xml\n\n# swagger配置\nswagger:\n  title: 代码生成接口文档\n  license: Powered By ruoyi\n  licenseUrl: https://ruoyi.vip\n\n# 代码生成\ngen: \n  # 作者\n  author: ruoyi\n  # 默认生成包路径 system 需改成自己的模块名称 如 system monitor tool\n  packageName: com.ruoyi.custom\n  # 自动去除表前缀，默认是false\n  autoRemovePre: true\n  # 表前缀（生成类名不会包含表前缀，多个用逗号分隔）\n  tablePrefix: sys_,t_\n', '2773b863e9ffa0981567b1eb17d9a26c', '2024-11-11 02:19:25', '2024-11-11 02:24:40', 'nacos', '*************', '', 'ecf1cef0-187f-4968-9c1e-fde5aa6c24e6', '代码生成', '', '', 'yaml', '');
INSERT INTO `config_info` VALUES (110, 'ruoyi-job-local.yml', 'DEFAULT_GROUP', '# spring配置\nspring: \n  redis:\n    host: **************\n    port: 23405\n    password: \n  datasource:\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: jdbc:mysql://**************:23403/ry-cloud?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8\n    username: ry-cloud\n    password: rNsALTSiyXSWKczx\n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.ruoyi.job.domain\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/*.xml\n\n# swagger配置\nswagger:\n  title: 定时任务接口文档\n  license: Powered By ruoyi\n  licenseUrl: https://ruoyi.vip\n', 'c73d5599cfae2ec45a0c1c330e05f5b3', '2024-11-11 02:19:25', '2024-11-11 02:25:24', 'nacos', '*************', '', 'ecf1cef0-187f-4968-9c1e-fde5aa6c24e6', '定时任务', '', '', 'yaml', '');
INSERT INTO `config_info` VALUES (111, 'ruoyi-file-local.yml', 'DEFAULT_GROUP', '# 本地文件上传    \nfile:\n    #domain: http://127.0.0.1:9300\n    domain: http://*************:8080/file\n    path: D:/ruoyi/uploadPath\n    prefix: /statics\n\n# FastDFS配置\nfdfs:\n  domain: http://************\n  soTimeout: 3000\n  connectTimeout: 2000\n  trackerList: ************:22122\n\n# Minio配置\nminio:\n  url: http://************:9000\n  accessKey: minioadmin\n  secretKey: minioadmin\n  bucketName: test', 'b02ac94e08eb4827e3c2b6471cc28d02', '2024-11-11 02:19:41', '2024-11-11 02:25:59', 'nacos', '*************', '', 'ecf1cef0-187f-4968-9c1e-fde5aa6c24e6', '文件服务', '', '', 'yaml', '');
INSERT INTO `config_info` VALUES (112, 'ruoyi-custom-local.yml', 'DEFAULT_GROUP', '# spring配置\nspring: \n  servlet:\n    multipart:\n      maxFileSize:  1000MB\n      maxRequestSize: 1000MB\n  main:\n    allow-circular-references: true\n    allow-bean-definition-overriding: true\n  redis:\n    host: **************\n    port: 23405\n    password: \n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: admin\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: jdbc:mysql://**************:23403/ry-cloud?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8&allowMultiQueries=true\n            username: ry-cloud\n            password: rNsALTSiyXSWKczx\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n      # seata: true    # 开启seata代理，开启后默认每个数据源都代理，如果某个不需要代理可单独关闭\n\n# seata配置\nseata:\n  # 默认关闭，如需启用spring.datasource.dynami.seata需要同时开启\n  enabled: false\n  # Seata 应用编号，默认为 ${spring.application.name}\n  application-id: ${spring.application.name}\n  # Seata 事务组编号，用于 TC 集群名\n  tx-service-group: ${spring.application.name}-group\n  # 关闭自动代理\n  enable-auto-data-source-proxy: false\n  # 服务配置项\n  service:\n    # 虚拟组和分组的映射\n    vgroup-mapping:\n      ruoyi-system-group: default\n  config:\n    type: nacos\n    nacos:\n      serverAddr: 127.0.0.1:8848\n      group: SEATA_GROUP\n      namespace:\n  registry:\n    type: nacos\n    nacos:\n      application: seata-server\n      server-addr: 127.0.0.1:8848\n      namespace:\n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.ruoyi.custom.**\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/**/*.xml\n    configuration:\n       map-underscore-to-camel-case: true\n\n# swagger配置\nswagger:\n  title: 客户模块接口文档\n  license: Powered By ruoyi\n  licenseUrl: https://ruoyi.vip\n\n# 编号前缀\nnumber.prefix:\n  wzck: YCJS-WZCK    # 物资出库编号前缀\n  wzrk: YCJS-WZRK    # 物资入库编号前缀\n  wzpd: YCJS-WZPD    # 物资盘点编号前缀', 'ae42edbd564ed3d15ed50d66f5062c29', '2024-11-11 02:19:41', '2024-11-11 02:28:10', 'nacos', '*************', '', 'ecf1cef0-187f-4968-9c1e-fde5aa6c24e6', '客户模块', '', '', 'yaml', '');
INSERT INTO `config_info` VALUES (128, 'ruoyi-system-ttt.yml', 'DEFAULT_GROUP', '# spring配置\nspring: \n  redis:\n    host: localhost\n    port: 6379\n    password: \n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: admin\n        loginPassword: 123456\n    dynamic:\n      primary: master\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: jdbc:mysql://**************:23403/ry-cloud?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8\n            username: ry-cloud\n            password: rNsALTSiyXSWKczx\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n      # seata: true    # 开启seata代理，开启后默认每个数据源都代理，如果某个不需要代理可单独关闭\n\n# seata配置\nseata:\n  # 默认关闭，如需启用spring.datasource.dynami.seata需要同时开启\n  enabled: false\n  # Seata 应用编号，默认为 ${spring.application.name}\n  application-id: ${spring.application.name}\n  # Seata 事务组编号，用于 TC 集群名\n  tx-service-group: ${spring.application.name}-group\n  # 关闭自动代理\n  enable-auto-data-source-proxy: false\n  # 服务配置项\n  service:\n    # 虚拟组和分组的映射\n    vgroup-mapping:\n      ruoyi-system-group: default\n  config:\n    type: nacos\n    nacos:\n      serverAddr: 127.0.0.1:8848\n      group: SEATA_GROUP\n      namespace:\n  registry:\n    type: nacos\n    nacos:\n      application: seata-server\n      server-addr: 127.0.0.1:8848\n      namespace:\n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.ruoyi.system\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/*.xml\n\n# swagger配置\nswagger:\n  title: 系统模块接口文档\n  license: Powered By ruoyi\n  licenseUrl: https://ruoyi.vip', '142ad10d1c44e2b81459e8ad21dea3e7', '2024-11-11 08:11:41', '2024-11-11 08:12:48', 'nacos', '*************', '', 'ttt', '系统模块', '', '', 'yaml', '');
INSERT INTO `config_info` VALUES (130, 'sentinel-ruoyi-gateway', 'DEFAULT_GROUP', '[\r\n    {\r\n        \"resource\": \"ruoyi-auth\",\r\n        \"count\": 500,\r\n        \"grade\": 1,\r\n        \"limitApp\": \"default\",\r\n        \"strategy\": 0,\r\n        \"controlBehavior\": 0\r\n    },\r\n	{\r\n        \"resource\": \"ruoyi-system\",\r\n        \"count\": 1000,\r\n        \"grade\": 1,\r\n        \"limitApp\": \"default\",\r\n        \"strategy\": 0,\r\n        \"controlBehavior\": 0\r\n    },\r\n	{\r\n        \"resource\": \"ruoyi-gen\",\r\n        \"count\": 200,\r\n        \"grade\": 1,\r\n        \"limitApp\": \"default\",\r\n        \"strategy\": 0,\r\n        \"controlBehavior\": 0\r\n    },\r\n	{\r\n        \"resource\": \"ruoyi-job\",\r\n        \"count\": 300,\r\n        \"grade\": 1,\r\n        \"limitApp\": \"default\",\r\n        \"strategy\": 0,\r\n        \"controlBehavior\": 0\r\n    }\r\n]', '9f3a3069261598f74220bc47958ec252', '2024-11-11 08:16:33', '2024-11-11 08:16:33', NULL, '*************', '', 'ttt', '限流策略', NULL, NULL, 'json', NULL);
INSERT INTO `config_info` VALUES (131, 'ruoyi-homecare-local.yml', 'DEFAULT_GROUP', '# spring\nspring: \n  rabbitmq:\n    host: 127.0.0.1\n    port: 5672\n    username: guest\n    password: guest\n  servlet:\n    multipart:\n      maxFileSize:  1000MB\n      maxRequestSize: 1000MB\n  main:\n    allow-circular-references: true\n    allow-bean-definition-overriding: true\n  redis:\n    host: localhost\n    port: 6379\n    password: \n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: admin\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # \n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: ******************************************************************************************************************************************************************************            username: root\n            password: dabojin_2022\n\n# seata\nseata:\n  # \n  enabled: false\n  # \n  application-id: ${spring.application.name}\n  # \n  tx-service-group: ${spring.application.name}-group\n  # \n  enable-auto-data-source-proxy: false\n  # \n  service:\n    # \n    vgroup-mapping:\n      ruoyi-system-group: default\n  config:\n    type: nacos\n    nacos:\n      serverAddr: 127.0.0.1:8848\n      group: SEATA_GROUP\n      namespace:\n  registry:\n    type: nacos\n    nacos:\n      application: seata-server\n      server-addr: 127.0.0.1:8848\n      namespace:\n\n# \n#mybatis:\n    # \n   # typeAliasesPackage: com.ruoyi.homecare.**\n    # \n    #mapperLocations: classpath:mapper/**/**/*.xml\n    #configuration:\n     #  map-underscore-to-camel-case: true\nmybatis-plus:\n    #\n    mapper-locations: classpath:mapper/**/**/*.xml\n    # \n    type-aliases-package: com.ruoyi.homecare.**\n    configuration:\n        # \n        map-underscore-to-camel-case: true  \n\nlogging:\n  level:\n    root: INFO\n    com.ruoyi: debug\n\n\n# swagger\nswagger:\n  title: jujiayanglao\n  license: Powered By ruoyi\n  licenseUrl: https://ruoyi.vip\n\n\nwx:\n  #\n  #v2 \n  appsecret: 0e5db3fd3cc6197dd68a785282dc1623\n  # appsecret: 78f1f354f767746171bcf957fb3fd3ec\n  pay:\n    appId: wxb7125f4f902fb0fe\n    # appid: wxa89aa4c24fca8ad4\n    mchId: **********\n    mchKey: dadba017qa3c7q4a8aq96bcq32df7fc5\n    apiV3Key: 2cc9f595e4d2616s865d1aa7wa13fbf4\n\nxcx:\n  appid: wxb7125f4f902fb0fe\n  appsecret: 0e5db3fd3cc6197dd68a785282dc1623\ngzh:\n  appid: wx4ab92103bfd594e7\n  appsecret: 6f297693d2cc0225a16e2f0ffdcece0c\nreserve:\n # appid: wx4ab92103bfd594e7\n # appsecret: 6f297693d2cc0225a16e2f0ffdcece0c\n  appid: wxa89aa4c24fca8ad4\n  appsecret: 78f1f354f767746171bcf957fb3fd3ec\n\nfile:\n  prefix: /statics\n  domain: http://**************:23360/file\n  path: /path', '133f0f24ddd8dec53d77828e32cc26c9', '2024-11-11 08:16:33', '2024-11-11 08:16:33', NULL, '*************', '', 'ttt', '', NULL, NULL, 'yaml', NULL);
INSERT INTO `config_info` VALUES (132, 'ruoyi-daycare-local.yml', 'DEFAULT_GROUP', '# spring配置\nspring: \n  servlet:\n    multipart:\n      maxFileSize:  1000MB\n      maxRequestSize: 1000MB\n  main:\n    allow-circular-references: true\n    allow-bean-definition-overriding: true\n  redis:\n    host: localhost\n    port: 6379\n    password: \n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: admin\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: *****************************************************************************************************************************************************************************            username: root\n            password: dabojin_2022\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n      # seata: true    # 开启seata代理，开启后默认每个数据源都代理，如果某个不需要代理可单独关闭\n\n# seata配置\nseata:\n  # 默认关闭，如需启用spring.datasource.dynami.seata需要同时开启\n  enabled: false\n  # Seata 应用编号，默认为 ${spring.application.name}\n  application-id: ${spring.application.name}\n  # Seata 事务组编号，用于 TC 集群名\n  tx-service-group: ${spring.application.name}-group\n  # 关闭自动代理\n  enable-auto-data-source-proxy: false\n  # 服务配置项\n  service:\n    # 虚拟组和分组的映射\n    vgroup-mapping:\n      ruoyi-system-group: default\n  config:\n    type: nacos\n    nacos:\n      serverAddr: 127.0.0.1:8848\n      group: SEATA_GROUP\n      namespace:\n  registry:\n    type: nacos\n    nacos:\n      application: seata-server\n      server-addr: 127.0.0.1:8848\n      namespace:\n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.ruoyi.daycare.**\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/**/*.xml\n    configuration:\n       map-underscore-to-camel-case: true\nlogging:\n  level:\n    root: INFO\n    com.ruoyi: debug\n    com.github.binarywang.demo.wx.pay: debug\n    com.github.binarywang.wxpay: debug\n\n# swagger配置\nswagger:\n  title: 日间照料模块接口文档\n  license: Powered By ruoyi\n  licenseUrl: https://ruoyi.vip', 'f1f2602fc0e412a9f330041469063a2f', '2024-11-11 08:16:33', '2024-11-11 08:16:33', NULL, '*************', '', 'ttt', '日间照料模块', NULL, NULL, 'yaml', NULL);
INSERT INTO `config_info` VALUES (133, 'application-local.yml', 'DEFAULT_GROUP', '#     \nfile:\n    #domain: http://127.0.0.1:9300\n    domain: http://*************:8080/file\n    path: D:/ruoyi/uploadPath\n    prefix: /statics\nspring:\n  main:\n    allow-circular-references: true\n    allow-bean-definition-overriding: true\n  autoconfigure:\n    exclude: com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure\n  mvc:\n    pathmatch:\n      matching-strategy: ant_path_matcher\n  cloud:\n    sentinel:\n      filter:\n        # \n        enabled: false\n  servlet:\n    multipart:\n      max-request-size: 100MB\n      max-file-size: 100MB\n\n# \nfeign:\n  sentinel:\n    enabled: true\n  okhttp:\n    enabled: true\n  httpclient:\n    enabled: false\n  client:\n    config:\n      default:\n        connectTimeout: 10000\n        readTimeout: 10000\n  compression:\n    request:\n      enabled: true\n    response:\n      enabled: true\n\n# \nmanagement:\n  endpoints:\n    web:\n      exposure:\n        include: \'*\'\n', 'b8423876c428ed2e6f019861767703df', '2024-11-11 08:16:33', '2024-11-11 08:16:33', NULL, '*************', '', 'ttt', '通用配置', NULL, NULL, 'yaml', NULL);
INSERT INTO `config_info` VALUES (134, 'ruoyi-gateway-local.yml', 'DEFAULT_GROUP', 'spring:\n  redis:\n    host: **************\n    port: 23405\n    password: \n  cloud:\n    gateway:\n      discovery:\n        locator:\n          lowerCaseServiceId: true\n          enabled: true\n      routes:\n        # 认证中心\n        - id: ruoyi-auth\n          uri: lb://ruoyi-auth\n          predicates:\n            - Path=/auth/**\n          filters:\n            # 验证码处理\n            - CacheRequestFilter\n            - ValidateCodeFilter\n            - StripPrefix=1\n        # 代码生成\n        - id: ruoyi-gen\n          uri: lb://ruoyi-gen\n          predicates:\n            - Path=/code/**\n          filters:\n            - StripPrefix=1\n        # 定时任务\n        - id: ruoyi-job\n          uri: lb://ruoyi-job\n          predicates:\n            - Path=/schedule/**\n          filters:\n            - StripPrefix=1\n        # 系统模块\n        - id: ruoyi-system\n          uri: lb://ruoyi-system\n          predicates:\n            - Path=/system/**\n          filters:\n            - StripPrefix=1\n        # 文件服务\n        - id: ruoyi-file\n          uri: lb://ruoyi-file\n          predicates:\n            - Path=/file/**\n          filters:\n            - StripPrefix=1\n        # 客户服务\n        - id: ruoyi-custom\n          uri: lb://ruoyi-custom\n          predicates:\n            - Path=/custom/**\n          filters:\n            - StripPrefix=1\n        # 居家服务\n        - id: ruoyi-homecare\n          uri: lb://ruoyi-homecare\n          predicates:\n            - Path=/homecare/**\n          filters:\n            - StripPrefix=1\n        # 日间照料服务\n        - id: ruoyi-daycare\n          uri: lb://ruoyi-daycare\n          predicates:\n            - Path=/daycare/**\n          filters:\n            - StripPrefix=1\n\n# 安全配置\nsecurity:\n  # 验证码\n  captcha:\n    enabled: false\n    type: math\n  # 防止XSS攻击\n  xss:\n    enabled: true\n    excludeUrls:\n      - /system/notice\n  # 不校验白名单\n  ignore:\n    whites:\n      - /auth/logout\n      - /auth/login\n      - /auth/register\n      - /*/v2/api-docs\n      - /csrf\n      - /custom/careWorkerInfo/careLogin\n      - /system/dict/data/type/**\n      - /homecare/homeCommunityBaseInfo/getCommunityList\n      - /file/**\n      - /homecare/HomeRegisterServiceProvider/registerServiceProvider\n      - /homecare/homeServiceProviderManagement/getServiceProviderLabel\n      - /homecare/appApi/getOpenIdByCode\n      - /homecare/appApi/getTokenByOpenId\n      - /homecare/homeServiceProviderManagement/getIgnoreServiceProviderLabel\n      - /homecare/wechatPay/notify/**\n      - /auth/appLogin\n      - /auth/businessLogin\n      - /auth/statics/**\n      - /homecare/appApi/xxx/onWxMsg\n      - /system/dict/data/type/**\n      - /homecare/homeCommunityBaseInfo/getCommunityList\n      - /homecare/homeTimeCoinSettings/getSettingsList\n      - /homecare/homeStreetInfo/list\n      - /homecare/homeAppServicePlaceOrderVolunteerInfo/register\n      - /homecare/homeStreetInfo/ignoreWhiteTreeselect\n      - /homecare/appApi/reserve/login\n      - /homecare/appApi/reserve/getOpenIdByCode\n      - /homecare/appApi/reserve/getTokenByOpenId\n      - /homecare/statics/**', '41a608b3c084dfbf170a54dc93e1d308', '2024-11-11 08:16:33', '2024-11-11 08:16:33', NULL, '*************', '', 'ttt', '网关模块', NULL, NULL, 'yaml', NULL);
INSERT INTO `config_info` VALUES (135, 'ruoyi-auth-local.yml', 'DEFAULT_GROUP', 'spring: \n  redis:\n    host: **************\n    port: 23405\n    password: \n# swagger配置\nswagger:\n  title: 权限接口文档\n  license: Powered By ruoyi\n  licenseUrl: https://ruoyi.vip', '0ba1168689b3ec4982d30568374735d4', '2024-11-11 08:16:33', '2024-11-11 08:16:33', NULL, '*************', '', 'ttt', '认证中心', NULL, NULL, 'yaml', NULL);
INSERT INTO `config_info` VALUES (136, 'ruoyi-monitor-local.yml', 'DEFAULT_GROUP', '# spring\r\nspring: \r\n  security:\r\n    user:\r\n      name: ruoyi\r\n      password: 123456\r\n  boot:\r\n    admin:\r\n      ui:\r\n        title: 若依服务状态监控\r\n', 'd8997d0707a2fd5d9fc4e8409da38129', '2024-11-11 08:16:33', '2024-11-11 08:16:33', NULL, '*************', '', 'ttt', '监控中心', NULL, NULL, 'yaml', NULL);
INSERT INTO `config_info` VALUES (137, 'ruoyi-gen-local.yml', 'DEFAULT_GROUP', '# spring配置\nspring: \n  redis:\n    host: **************\n    port: 23405\n    password: \n  datasource: \n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: jdbc:mysql://**************:23403/ry-cloud?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8\n    username: ry-cloud\n    password: rNsALTSiyXSWKczx\n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.ruoyi.gen.domain\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/*.xml\n\n# swagger配置\nswagger:\n  title: 代码生成接口文档\n  license: Powered By ruoyi\n  licenseUrl: https://ruoyi.vip\n\n# 代码生成\ngen: \n  # 作者\n  author: ruoyi\n  # 默认生成包路径 system 需改成自己的模块名称 如 system monitor tool\n  packageName: com.ruoyi.custom\n  # 自动去除表前缀，默认是false\n  autoRemovePre: true\n  # 表前缀（生成类名不会包含表前缀，多个用逗号分隔）\n  tablePrefix: sys_,t_\n', '2773b863e9ffa0981567b1eb17d9a26c', '2024-11-11 08:16:33', '2024-11-11 08:16:33', NULL, '*************', '', 'ttt', '代码生成', NULL, NULL, 'yaml', NULL);
INSERT INTO `config_info` VALUES (138, 'ruoyi-job-local.yml', 'DEFAULT_GROUP', '# spring配置\nspring: \n  redis:\n    host: **************\n    port: 23405\n    password: \n  datasource:\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: jdbc:mysql://**************:23403/ry-cloud?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8\n    username: ry-cloud\n    password: rNsALTSiyXSWKczx\n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.ruoyi.job.domain\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/*.xml\n\n# swagger配置\nswagger:\n  title: 定时任务接口文档\n  license: Powered By ruoyi\n  licenseUrl: https://ruoyi.vip\n', 'c73d5599cfae2ec45a0c1c330e05f5b3', '2024-11-11 08:16:33', '2024-11-11 08:16:33', NULL, '*************', '', 'ttt', '定时任务', NULL, NULL, 'yaml', NULL);
INSERT INTO `config_info` VALUES (139, 'ruoyi-file-local.yml', 'DEFAULT_GROUP', '# 本地文件上传    \nfile:\n    #domain: http://127.0.0.1:9300\n    domain: http://*************:8080/file\n    path: D:/ruoyi/uploadPath\n    prefix: /statics\n\n# FastDFS配置\nfdfs:\n  domain: http://************\n  soTimeout: 3000\n  connectTimeout: 2000\n  trackerList: ************:22122\n\n# Minio配置\nminio:\n  url: http://************:9000\n  accessKey: minioadmin\n  secretKey: minioadmin\n  bucketName: test', 'b02ac94e08eb4827e3c2b6471cc28d02', '2024-11-11 08:16:33', '2024-11-11 08:16:33', NULL, '*************', '', 'ttt', '文件服务', NULL, NULL, 'yaml', NULL);
INSERT INTO `config_info` VALUES (140, 'ruoyi-custom-local.yml', 'DEFAULT_GROUP', '# spring配置\nspring: \n  servlet:\n    multipart:\n      maxFileSize:  1000MB\n      maxRequestSize: 1000MB\n  main:\n    allow-circular-references: true\n    allow-bean-definition-overriding: true\n  redis:\n    host: **************\n    port: 23405\n    password: \n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: admin\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: jdbc:mysql://**************:23403/ry-cloud?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8&allowMultiQueries=true\n            username: ry-cloud\n            password: rNsALTSiyXSWKczx\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n      # seata: true    # 开启seata代理，开启后默认每个数据源都代理，如果某个不需要代理可单独关闭\n\n# seata配置\nseata:\n  # 默认关闭，如需启用spring.datasource.dynami.seata需要同时开启\n  enabled: false\n  # Seata 应用编号，默认为 ${spring.application.name}\n  application-id: ${spring.application.name}\n  # Seata 事务组编号，用于 TC 集群名\n  tx-service-group: ${spring.application.name}-group\n  # 关闭自动代理\n  enable-auto-data-source-proxy: false\n  # 服务配置项\n  service:\n    # 虚拟组和分组的映射\n    vgroup-mapping:\n      ruoyi-system-group: default\n  config:\n    type: nacos\n    nacos:\n      serverAddr: 127.0.0.1:8848\n      group: SEATA_GROUP\n      namespace:\n  registry:\n    type: nacos\n    nacos:\n      application: seata-server\n      server-addr: 127.0.0.1:8848\n      namespace:\n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.ruoyi.custom.**\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/**/*.xml\n    configuration:\n       map-underscore-to-camel-case: true\n\n# swagger配置\nswagger:\n  title: 客户模块接口文档\n  license: Powered By ruoyi\n  licenseUrl: https://ruoyi.vip\n\n# 编号前缀\nnumber.prefix:\n  wzck: YCJS-WZCK    # 物资出库编号前缀\n  wzrk: YCJS-WZRK    # 物资入库编号前缀\n  wzpd: YCJS-WZPD    # 物资盘点编号前缀', 'ae42edbd564ed3d15ed50d66f5062c29', '2024-11-11 08:16:44', '2024-11-11 08:16:44', NULL, '*************', '', 'ttt', '客户模块', NULL, NULL, 'yaml', NULL);
INSERT INTO `config_info` VALUES (141, 'sentinel-ruoyi-gateway', 'DEFAULT_GROUP', '[\r\n    {\r\n        \"resource\": \"ruoyi-auth\",\r\n        \"count\": 500,\r\n        \"grade\": 1,\r\n        \"limitApp\": \"default\",\r\n        \"strategy\": 0,\r\n        \"controlBehavior\": 0\r\n    },\r\n	{\r\n        \"resource\": \"ruoyi-system\",\r\n        \"count\": 1000,\r\n        \"grade\": 1,\r\n        \"limitApp\": \"default\",\r\n        \"strategy\": 0,\r\n        \"controlBehavior\": 0\r\n    },\r\n	{\r\n        \"resource\": \"ruoyi-gen\",\r\n        \"count\": 200,\r\n        \"grade\": 1,\r\n        \"limitApp\": \"default\",\r\n        \"strategy\": 0,\r\n        \"controlBehavior\": 0\r\n    },\r\n	{\r\n        \"resource\": \"ruoyi-job\",\r\n        \"count\": 300,\r\n        \"grade\": 1,\r\n        \"limitApp\": \"default\",\r\n        \"strategy\": 0,\r\n        \"controlBehavior\": 0\r\n    }\r\n]', '9f3a3069261598f74220bc47958ec252', '2024-11-11 08:17:07', '2024-11-11 08:17:07', NULL, '*************', '', 'local', '限流策略', NULL, NULL, 'json', NULL);
INSERT INTO `config_info` VALUES (142, 'ruoyi-homecare-local.yml', 'DEFAULT_GROUP', '# spring\nspring: \n  rabbitmq:\n    host: 127.0.0.1\n    port: 5672\n    username: guest\n    password: guest\n  servlet:\n    multipart:\n      maxFileSize:  1000MB\n      maxRequestSize: 1000MB\n  main:\n    allow-circular-references: true\n    allow-bean-definition-overriding: true\n  redis:\n    host: localhost\n    port: 6379\n    password: \n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: admin\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # \n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: ******************************************************************************************************************************************************************************            username: root\n            password: dabojin_2022\n\n# seata\nseata:\n  # \n  enabled: false\n  # \n  application-id: ${spring.application.name}\n  # \n  tx-service-group: ${spring.application.name}-group\n  # \n  enable-auto-data-source-proxy: false\n  # \n  service:\n    # \n    vgroup-mapping:\n      ruoyi-system-group: default\n  config:\n    type: nacos\n    nacos:\n      serverAddr: 127.0.0.1:8848\n      group: SEATA_GROUP\n      namespace:\n  registry:\n    type: nacos\n    nacos:\n      application: seata-server\n      server-addr: 127.0.0.1:8848\n      namespace:\n\n# \n#mybatis:\n    # \n   # typeAliasesPackage: com.ruoyi.homecare.**\n    # \n    #mapperLocations: classpath:mapper/**/**/*.xml\n    #configuration:\n     #  map-underscore-to-camel-case: true\nmybatis-plus:\n    #\n    mapper-locations: classpath:mapper/**/**/*.xml\n    # \n    type-aliases-package: com.ruoyi.homecare.**\n    configuration:\n        # \n        map-underscore-to-camel-case: true  \n\nlogging:\n  level:\n    root: INFO\n    com.ruoyi: debug\n\n\n# swagger\nswagger:\n  title: jujiayanglao\n  license: Powered By ruoyi\n  licenseUrl: https://ruoyi.vip\n\n\nwx:\n  #\n  #v2 \n  appsecret: 0e5db3fd3cc6197dd68a785282dc1623\n  # appsecret: 78f1f354f767746171bcf957fb3fd3ec\n  pay:\n    appId: wxb7125f4f902fb0fe\n    # appid: wxa89aa4c24fca8ad4\n    mchId: **********\n    mchKey: dadba017qa3c7q4a8aq96bcq32df7fc5\n    apiV3Key: 2cc9f595e4d2616s865d1aa7wa13fbf4\n\nxcx:\n  appid: wxb7125f4f902fb0fe\n  appsecret: 0e5db3fd3cc6197dd68a785282dc1623\ngzh:\n  appid: wx4ab92103bfd594e7\n  appsecret: 6f297693d2cc0225a16e2f0ffdcece0c\nreserve:\n # appid: wx4ab92103bfd594e7\n # appsecret: 6f297693d2cc0225a16e2f0ffdcece0c\n  appid: wxa89aa4c24fca8ad4\n  appsecret: 78f1f354f767746171bcf957fb3fd3ec\n\nfile:\n  prefix: /statics\n  domain: http://**************:23360/file\n  path: /path', '133f0f24ddd8dec53d77828e32cc26c9', '2024-11-11 08:17:07', '2024-11-11 08:17:07', NULL, '*************', '', 'local', '', NULL, NULL, 'yaml', NULL);
INSERT INTO `config_info` VALUES (143, 'ruoyi-daycare-local.yml', 'DEFAULT_GROUP', '# spring配置\nspring: \n  servlet:\n    multipart:\n      maxFileSize:  1000MB\n      maxRequestSize: 1000MB\n  main:\n    allow-circular-references: true\n    allow-bean-definition-overriding: true\n  redis:\n    host: localhost\n    port: 6379\n    password: \n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: admin\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: *****************************************************************************************************************************************************************************            username: root\n            password: dabojin_2022\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n      # seata: true    # 开启seata代理，开启后默认每个数据源都代理，如果某个不需要代理可单独关闭\n\n# seata配置\nseata:\n  # 默认关闭，如需启用spring.datasource.dynami.seata需要同时开启\n  enabled: false\n  # Seata 应用编号，默认为 ${spring.application.name}\n  application-id: ${spring.application.name}\n  # Seata 事务组编号，用于 TC 集群名\n  tx-service-group: ${spring.application.name}-group\n  # 关闭自动代理\n  enable-auto-data-source-proxy: false\n  # 服务配置项\n  service:\n    # 虚拟组和分组的映射\n    vgroup-mapping:\n      ruoyi-system-group: default\n  config:\n    type: nacos\n    nacos:\n      serverAddr: 127.0.0.1:8848\n      group: SEATA_GROUP\n      namespace:\n  registry:\n    type: nacos\n    nacos:\n      application: seata-server\n      server-addr: 127.0.0.1:8848\n      namespace:\n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.ruoyi.daycare.**\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/**/*.xml\n    configuration:\n       map-underscore-to-camel-case: true\nlogging:\n  level:\n    root: INFO\n    com.ruoyi: debug\n    com.github.binarywang.demo.wx.pay: debug\n    com.github.binarywang.wxpay: debug\n\n# swagger配置\nswagger:\n  title: 日间照料模块接口文档\n  license: Powered By ruoyi\n  licenseUrl: https://ruoyi.vip', 'f1f2602fc0e412a9f330041469063a2f', '2024-11-11 08:17:07', '2024-11-11 08:17:07', NULL, '*************', '', 'local', '日间照料模块', NULL, NULL, 'yaml', NULL);
INSERT INTO `config_info` VALUES (144, 'application-local.yml', 'DEFAULT_GROUP', '#     \nfile:\n    #domain: http://127.0.0.1:9300\n    domain: http://*************:8080/file\n    path: D:/ruoyi/uploadPath\n    prefix: /statics\nspring:\n  main:\n    allow-circular-references: true\n    allow-bean-definition-overriding: true\n  autoconfigure:\n    exclude: com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure\n  mvc:\n    pathmatch:\n      matching-strategy: ant_path_matcher\n  cloud:\n    sentinel:\n      filter:\n        # \n        enabled: false\n  servlet:\n    multipart:\n      max-request-size: 100MB\n      max-file-size: 100MB\n\n# \nfeign:\n  sentinel:\n    enabled: true\n  okhttp:\n    enabled: true\n  httpclient:\n    enabled: false\n  client:\n    config:\n      default:\n        connectTimeout: 10000\n        readTimeout: 10000\n  compression:\n    request:\n      enabled: true\n    response:\n      enabled: true\n\n# \nmanagement:\n  endpoints:\n    web:\n      exposure:\n        include: \'*\'\n', 'b8423876c428ed2e6f019861767703df', '2024-11-11 08:17:07', '2024-11-11 08:17:07', NULL, '*************', '', 'local', '通用配置', NULL, NULL, 'yaml', NULL);
INSERT INTO `config_info` VALUES (145, 'ruoyi-gateway-local.yml', 'DEFAULT_GROUP', 'spring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n  cloud:\n    gateway:\n      discovery:\n        locator:\n          lowerCaseServiceId: true\n          enabled: true\n      routes:\n        # 认证中心\n        - id: ruoyi-auth\n          uri: lb://ruoyi-auth\n          predicates:\n            - Path=/auth/**\n          filters:\n            # 验证码处理\n            - CacheRequestFilter\n            - ValidateCodeFilter\n            - StripPrefix=1\n        # 代码生成\n        - id: ruoyi-gen\n          uri: lb://ruoyi-gen\n          predicates:\n            - Path=/code/**\n          filters:\n            - StripPrefix=1\n        # 定时任务\n        - id: ruoyi-job\n          uri: lb://ruoyi-job\n          predicates:\n            - Path=/schedule/**\n          filters:\n            - StripPrefix=1\n        # 系统模块\n        - id: ruoyi-system\n          uri: lb://ruoyi-system\n          predicates:\n            - Path=/system/**\n          filters:\n            - StripPrefix=1\n        # 文件服务\n        - id: ruoyi-file\n          uri: lb://ruoyi-file\n          predicates:\n            - Path=/file/**\n          filters:\n            - StripPrefix=1\n        # 客户服务\n        - id: ruoyi-custom\n          uri: lb://ruoyi-custom\n          predicates:\n            - Path=/custom/**\n          filters:\n            - StripPrefix=1\n        # 居家服务\n        - id: ruoyi-homecare\n          uri: lb://ruoyi-homecare\n          predicates:\n            - Path=/homecare/**\n          filters:\n            - StripPrefix=1\n        # 日间照料服务\n        - id: ruoyi-daycare\n          uri: lb://ruoyi-daycare\n          predicates:\n            - Path=/daycare/**\n          filters:\n            - StripPrefix=1\n         # IBMS-资产\n        - id: ruoyi-assets\n          uri: lb://ruoyi-assets\n          predicates:\n            - Path=/assets/**\n          filters:\n            - StripPrefix=1  \n         # IBMS-物业\n        - id: ruoyi-realty\n          uri: lb://ruoyi-realty\n          predicates:\n            - Path=/realty/**\n          filters:\n            - StripPrefix=1  \n\n# 安全配置\nsecurity:\n  # 验证码\n  captcha:\n    configs:\n      # 应用及设置，默认关闭\n      default:\n        enabled: false\n        type: math\n      custom_pc_admin:\n        enabled: true\n        type: math\n      custom_applet_lryh:\n        enabled: false\n        type: math\n      custom_app_hg:\n        enabled: true\n        type: math      \n\n  # 防止XSS攻击\n  xss:\n    enabled: true\n    excludeUrls:\n      - /system/notice\n  # 不校验白名单\n  ignore:\n    whites:\n      - /auth/logout\n      - /auth/login\n      - /auth/register\n      - /auth/registerByRegisteredVisitor\n      - /auth/getOpenIdByCode\n      - /auth/getTokenByOpenId\n      - /auth/appLogin\n      - /auth/businessLogin\n      - /auth/statics/**\n      - /*/v2/api-docs\n      - /csrf\n      - /custom/careWorkerInfo/careLogin\n      - /system/dict/data/type/**\n      - /homecare/homeCommunityBaseInfo/getCommunityList\n      - /file/**\n      - /doc.html\n      - /homecare/HomeRegisterServiceProvider/registerServiceProvider\n      - /homecare/homeServiceProviderManagement/getServiceProviderLabel\n      - /homecare/appApi/getOpenIdByCode\n      - /homecare/appApi/getTokenByOpenId\n      - /homecare/homeServiceProviderManagement/getIgnoreServiceProviderLabel\n      - /homecare/wechatPay/notify/**\n      - /homecare/appApi/xxx/onWxMsg\n      - /system/dict/data/type/**\n      - /homecare/homeCommunityBaseInfo/getCommunityList\n      - /homecare/homeTimeCoinSettings/getSettingsList\n      - /homecare/homeStreetInfo/list\n      - /homecare/homeAppServicePlaceOrderVolunteerInfo/register\n      - /homecare/homeStreetInfo/ignoreWhiteTreeselect\n      - /homecare/appApi/reserve/login\n      - /homecare/appApi/reserve/getOpenIdByCode\n      - /homecare/appApi/reserve/getTokenByOpenId\n      - /homecare/statics/**\n      - /*/app/rest/models/*/bpmn20 \n      - /assets/fixedAssetsArchivesBase/getById \n      - /security/visual/getData\n      - /realty/visitor/addByScan\n      - /assets/fixedAssetsArchivesBase/getById\n      - /security-js/visual/getData\n      - /energy/**', 'f1a442cfd0b43ef9a328d7919f78f051', '2024-11-11 08:17:07', '2025-03-15 02:25:52', 'nacos', '*************', '', 'local', '网关模块', '', '', 'yaml', '');
INSERT INTO `config_info` VALUES (146, 'ruoyi-auth-local.yml', 'DEFAULT_GROUP', 'spring: \n  redis:\n    host: localhost\n    port: 6379\n    password: \n# swagger配置\nswagger:\n  title: 权限接口文档\n  license: Powered By ruoyi\n  licenseUrl: https://ruoyi.vip\nwx:\n  miniapp:\n    configs:\n      # 资产小程序\n      - appid: wxd6928057261bd32a\n        secret: 5606fe89dddc3a0260123076bd225093\n        msgDataFormat: JSON\n      # 养老测试\n      - appid: wxb7125f4f902fb0fe\n        secret:  0e5db3fd3cc6197dd68a785282dc1623\n        msgDataFormat: JSON\n      # 科技园区管理端\n      - appid: wx58f02809634e38cd\n        secret:  01ff8ff4587054c6be040b0581505947\n        msgDataFormat: JSON\n      # 科技园区企业端\n      - appid: wx942a2e3c254e01ce\n        secret:  39bc3b298a548b678faeade6c2a0c349\n        msgDataFormat: JSON', '01fa586820b028e761260eeb359b3b9e', '2024-11-11 08:17:07', '2024-11-13 06:52:35', 'nacos', '*************', '', 'local', '认证中心', '', '', 'yaml', '');
INSERT INTO `config_info` VALUES (147, 'ruoyi-monitor-local.yml', 'DEFAULT_GROUP', '# spring\r\nspring: \r\n  security:\r\n    user:\r\n      name: ruoyi\r\n      password: 123456\r\n  boot:\r\n    admin:\r\n      ui:\r\n        title: 若依服务状态监控\r\n', 'd8997d0707a2fd5d9fc4e8409da38129', '2024-11-11 08:17:07', '2024-11-11 08:17:07', NULL, '*************', '', 'local', '监控中心', NULL, NULL, 'yaml', NULL);
INSERT INTO `config_info` VALUES (148, 'ruoyi-gen-local.yml', 'DEFAULT_GROUP', '# spring配置\nspring: \n  redis:\n    host: localhost\n    port: 6379\n    password: \n  datasource: \n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: jdbc:mysql://**************:23403/ry-cloud?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8\n    username: ry-cloud\n    password: rNsALTSiyXSWKczx\n\n# mybatis配置\nmybatis-plus:\n  # 搜索指定包别名\n  typeAliasesPackage: com.ruoyi.gen.domain\n  # 配置mapper的扫描，找到所有的mapper.xml映射文件\n  mapperLocations: classpath:mapper/**/*.xml\n\n# swagger配置\nswagger:\n  title: 代码生成接口文档\n  license: Powered By ruoyi\n  licenseUrl: https://ruoyi.vip\n\n# 代码生成\ngen: \n  # 作者\n  author: zkx\n  # 默认生成包路径 system 需改成自己的模块名称 如 system monitor tool\n  packageName: com.ruoyi.custom\n  # 自动去除表前缀，默认是false\n  autoRemovePre: true\n  # 表前缀（生成类名不会包含表前缀，多个用逗号分隔）\n  tablePrefix: sys_,t_\n', 'fe8163b1498f548697fea6e18bb883a7', '2024-11-11 08:17:07', '2024-11-15 03:39:59', 'nacos', '*************', '', 'local', '代码生成', '', '', 'yaml', '');
INSERT INTO `config_info` VALUES (149, 'ruoyi-job-local.yml', 'DEFAULT_GROUP', '# spring配置\nspring: \n  redis:\n    host: localhost\n    port: 6379\n    password: \n  datasource:\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: jdbc:mysql://**************:23403/ry-cloud?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8\n    username: ry-cloud\n    password: rNsALTSiyXSWKczx\n\n# mybatis配置\nmybatis-plus:\n    # 搜索指定包别名\n    typeAliasesPackage: com.ruoyi.job.domain\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/*.xml\n\n# swagger配置\nswagger:\n  title: 定时任务接口文档\n  license: Powered By ruoyi\n  licenseUrl: https://ruoyi.vip\n', '0d91d2b5ca3d5af57817961db9e437cc', '2024-11-11 08:17:07', '2024-11-11 09:11:12', 'nacos', '*************', '', 'local', '定时任务', '', '', 'yaml', '');
INSERT INTO `config_info` VALUES (150, 'ruoyi-file-local.yml', 'DEFAULT_GROUP', '# 本地文件上传    \nfile:\n    #domain: http://127.0.0.1:9300\n    domain: http://*************:8080/file\n    path: D:/ruoyi/uploadPath\n    prefix: /statics\n\n# FastDFS配置\nfdfs:\n  domain: http://************\n  soTimeout: 3000\n  connectTimeout: 2000\n  trackerList: ************:22122\n\n# Minio配置\nminio:\n  url: http://************:9000\n  accessKey: minioadmin\n  secretKey: minioadmin\n  bucketName: test', 'b02ac94e08eb4827e3c2b6471cc28d02', '2024-11-11 08:17:07', '2024-11-11 08:17:07', NULL, '*************', '', 'local', '文件服务', NULL, NULL, 'yaml', NULL);
INSERT INTO `config_info` VALUES (151, 'ruoyi-custom-local.yml', 'DEFAULT_GROUP', '# spring配置\nspring: \n  servlet:\n    multipart:\n      maxFileSize:  1000MB\n      maxRequestSize: 1000MB\n  main:\n    allow-circular-references: true\n    allow-bean-definition-overriding: true\n  redis:\n    host: localhost\n    port: 6379\n    password: \n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: admin\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: jdbc:mysql://**************:23403/ry-custom?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8&allowMultiQueries=true\n            username: ry-custom\n            password: m782cLf8Jst55rDK\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n      # seata: true    # 开启seata代理，开启后默认每个数据源都代理，如果某个不需要代理可单独关闭\n\n# seata配置\nseata:\n  # 默认关闭，如需启用spring.datasource.dynami.seata需要同时开启\n  enabled: false\n  # Seata 应用编号，默认为 ${spring.application.name}\n  application-id: ${spring.application.name}\n  # Seata 事务组编号，用于 TC 集群名\n  tx-service-group: ${spring.application.name}-group\n  # 关闭自动代理\n  enable-auto-data-source-proxy: false\n  # 服务配置项\n  service:\n    # 虚拟组和分组的映射\n    vgroup-mapping:\n      ruoyi-system-group: default\n  config:\n    type: nacos\n    nacos:\n      serverAddr: 127.0.0.1:8848\n      group: SEATA_GROUP\n      namespace:\n  registry:\n    type: nacos\n    nacos:\n      application: seata-server\n      server-addr: 127.0.0.1:8848\n      namespace:\n\n# mybatis配置\nmybatis-plus:\n    # 搜索指定包别名\n    typeAliasesPackage: com.ruoyi.custom.admin.**\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/**/*.xml\n    configuration:\n       map-underscore-to-camel-case: true\n\nmybatis:\n  type-handlers-package: com.ruoyi.custom.config.mybatis.handler\n\n# swagger配置\nswagger:\n  title: 客户模块接口文档\n  license: Powered By ruoyi\n  licenseUrl: https://ruoyi.vip\n\n# 微信小程序相关\nwx:\n  #\n  #v2 \n  appsecret: 0e5db3fd3cc6197dd68a785282dc1623\n  # appsecret: 78f1f354f767746171bcf957fb3fd3ec\n  pay:\n    appId: wxb7125f4f902fb0fe\n    # appid: wxa89aa4c24fca8ad4\n    mchId: **********\n    mchKey: dadba017qa3c7q4a8aq96bcq32df7fc5\n    apiV3Key: 2cc9f595e4d2616s865d1aa7wa13fbf4\n    keyPath: classpath:wxcertificate/apiclient_cert.p12\n    privateKeyPath: classpath:wxcertificate/apiclient_key.pem\n    privateCertPath: classpath:wxcertificate/apiclient_cert.pem', 'fb91c3775e4b3f728f7fd03d6c4b749b', '2024-11-11 08:17:14', '2025-03-13 06:01:41', 'nacos', '*************', '', 'local', '客户模块', '', '', 'yaml', '');
INSERT INTO `config_info` VALUES (152, 'ruoyi-system-local.yml', 'DEFAULT_GROUP', '# spring配置\nspring: \n  redis:\n    host: localhost\n    port: 6379\n    password: \n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: admin\n        loginPassword: 123456\n    dynamic:\n      primary: master\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: jdbc:mysql://**************:23403/ry-cloud?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8\n            username: ry-cloud\n            password: rNsALTSiyXSWKczx\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n      # seata: true    # 开启seata代理，开启后默认每个数据源都代理，如果某个不需要代理可单独关闭\n\n# seata配置\nseata:\n  # 默认关闭，如需启用spring.datasource.dynami.seata需要同时开启\n  enabled: false\n  # Seata 应用编号，默认为 ${spring.application.name}\n  application-id: ${spring.application.name}\n  # Seata 事务组编号，用于 TC 集群名\n  tx-service-group: ${spring.application.name}-group\n  # 关闭自动代理\n  enable-auto-data-source-proxy: false\n  # 服务配置项\n  service:\n    # 虚拟组和分组的映射\n    vgroup-mapping:\n      ruoyi-system-group: default\n  config:\n    type: nacos\n    nacos:\n      serverAddr: 127.0.0.1:8848\n      group: SEATA_GROUP\n      namespace:\n  registry:\n    type: nacos\n    nacos:\n      application: seata-server\n      server-addr: 127.0.0.1:8848\n      namespace:\n\n# mybatis配置\nmybatis-plus:\n    # 搜索指定包别名\n    typeAliasesPackage: com.ruoyi.system\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/*.xml\n\n# swagger配置\nswagger:\n  title: 系统模块接口文档\n  license: Powered By ruoyi\n  licenseUrl: https://ruoyi.vip', '92e3b7dc8f5b4451b02636ace6a538bf', '2024-11-11 08:17:33', '2024-11-11 08:54:22', 'nacos', '*************', '', 'local', '系统模块', '', '', 'yaml', '');
INSERT INTO `config_info` VALUES (165, 'ruoyi-assets-local.yml', 'DEFAULT_GROUP', '# spring配置\nspring:\n  redis:\n    host: 127.0.0.1\n    port: 6379\n    password: \n    timeout: 10000\n  datasource:\n    type: com.alibaba.druid.pool.DruidDataSource\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: jdbc:mysql://**************:23403/ry-assets?useSSL=false&useUnicode=true&characterEncoding=utf8&serverTimezone=GMT%2B8&allowMultiQueries=true&nullCatalogMeansCurrent=true&allowPublicKeyRetrieval=true\n    username: ry-assets\n    password: EmeN33FwA7SxXtAr\n# mybatis-plus配置\nmybatis-plus:\n  mapper-locations: classpath*:/mapper/**/*Mapper.xml,classpath:/META-INF/modeler-mybatis-mappings/*.xml\n  typeAliasesPackage: com.ibms.service.**.domain\n  configuration:\n        log-impl: org.apache.ibatis.logging.stdout.StdOutImpl\n  #驼峰命名     \n  map-underscore-to-camel-case: true\n  configuration-properties:\n    blobType: BLOB\n    boolValue: true\n    prefix: \'\'\nflowable:\n  # 关闭定时任务Job\n  async-executor-activate: false\n  check-process-definitions: false\n  process-definition-location-prefix: classpath:/processes/\n  #是否检测表生成\n  database-schema-update: true\n  # properties:\n  #   blobType: BLOB\n  #   boolValue: true\n  #   prefix: \'\'\n  common:\n    app:\n      idm-url: http://localhost:9999\n      idm-admin:\n        user: admin\n        password: test\n# 本地文件地址\nfile:\n    front-domain: http://**************:23374\n    back-domain: http://**************:23374/prod-api/file\n    # assetsRqUrl: http://**************:23370/prod-api/assets/\n    path: /www/ibms/uploadPath\n    prefix: /statics\n# swagger配置\nswagger:\n  title: 资产管理模块\n  license: Powered By 大博金-研发部\n  licenseUrl: https://www.dabojinkj.com', '3932a354b6e865c7d8350eae21cbdd51', '2024-11-11 09:42:56', '2024-11-13 09:08:33', 'nacos', '*************', '', 'local', 'IBMS-资产管理', '', '', 'yaml', '');
INSERT INTO `config_info` VALUES (166, 'ruoyi-realty-local.yml', 'DEFAULT_GROUP', '# spring配置\nspring:\n  redis:\n    host: 127.0.0.1\n    port: 6379\n    password: \n    timeout: 5000\n  datasource:\n    type: com.zaxxer.hikari.HikariDataSource\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: jdbc:mysql://**************:23403/ry-realty?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8\n    username: ry-realty\n    password: EFAAD74SAis7nmSK\n      #使用HikariCP连接池\n    hikari:\n      connection-timeout: 5000 #连接超时时间，默认30s\n      maximum-pool-size: 20 #连接池最大数量，默认10\n      minimum-idle: 5 #连接池最小空闲连接数，默认10\n      idleTimeout: 60000 #连接的最大空闲时间，60s\n      maxLifetime: 1800000 #连接的最大生命周期，30min\n      # 开启JMX支持服务，方便状态监控\n      registerMbeans: true\n      jmxName: \'com.zaxxer.hikari:type=DataSource\'\n    \n# mybatis-plus配置\nmybatis-plus:\n  typeAliasesPackage: com.ibms.service.realty.web.domain\n  mapper-locations: classpath*:mapper/*Mapper.xml\n  #mybatis-plus配置控制台打印完整带参数SQL语句\n  configuration: \n    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl\n    #HikariCP连接池实现JDBC数据源\n    dataSourceType: com.zaxxer.hikari.HikariDataSource\n\n\n# swagger配置\nswagger:\n  title: 物业模块\n  license: Powered By 大博金-研发部\n  licenseUrl: https://www.dabojinkj.com\n\n# 后台域名\nbackground-domain: http://*************\n\n# 前端ng域名\nfront-domain: http://**************:23391', '9a20f4eb8b00e2b0103c512f0780bf22', '2024-11-11 09:42:56', '2024-11-12 08:01:06', 'nacos', '*************', '', 'local', '', '', '', 'yaml', '');
INSERT INTO `config_info` VALUES (178, 'sentinel-ruoyi-gateway', 'DEFAULT_GROUP', '[\r\n    {\r\n        \"resource\": \"ruoyi-auth\",\r\n        \"count\": 500,\r\n        \"grade\": 1,\r\n        \"limitApp\": \"default\",\r\n        \"strategy\": 0,\r\n        \"controlBehavior\": 0\r\n    },\r\n	{\r\n        \"resource\": \"ruoyi-system\",\r\n        \"count\": 1000,\r\n        \"grade\": 1,\r\n        \"limitApp\": \"default\",\r\n        \"strategy\": 0,\r\n        \"controlBehavior\": 0\r\n    },\r\n	{\r\n        \"resource\": \"ruoyi-gen\",\r\n        \"count\": 200,\r\n        \"grade\": 1,\r\n        \"limitApp\": \"default\",\r\n        \"strategy\": 0,\r\n        \"controlBehavior\": 0\r\n    },\r\n	{\r\n        \"resource\": \"ruoyi-job\",\r\n        \"count\": 300,\r\n        \"grade\": 1,\r\n        \"limitApp\": \"default\",\r\n        \"strategy\": 0,\r\n        \"controlBehavior\": 0\r\n    }\r\n]', '9f3a3069261598f74220bc47958ec252', '2024-11-14 08:51:07', '2024-11-14 08:51:07', NULL, '*************', '', 'test', '限流策略', NULL, NULL, 'json', NULL);
INSERT INTO `config_info` VALUES (179, 'ruoyi-homecare-test.yml', 'DEFAULT_GROUP', '# spring\nspring: \n  rabbitmq:\n    host: 127.0.0.1\n    port: 5672\n    username: guest\n    password: guest\n  servlet:\n    multipart:\n      maxFileSize:  1000MB\n      maxRequestSize: 1000MB\n  main:\n    allow-circular-references: true\n    allow-bean-definition-overriding: true\n  redis:\n    host: localhost\n    port: 6379\n    password: \n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: admin\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # \n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: ******************************************************************************************************************************************************************************            username: root\n            password: dabojin_2022\n\n# seata\nseata:\n  # \n  enabled: false\n  # \n  application-id: ${spring.application.name}\n  # \n  tx-service-group: ${spring.application.name}-group\n  # \n  enable-auto-data-source-proxy: false\n  # \n  service:\n    # \n    vgroup-mapping:\n      ruoyi-system-group: default\n  config:\n    type: nacos\n    nacos:\n      serverAddr: 127.0.0.1:8848\n      group: SEATA_GROUP\n      namespace:\n  registry:\n    type: nacos\n    nacos:\n      application: seata-server\n      server-addr: 127.0.0.1:8848\n      namespace:\n\n# \n#mybatis:\n    # \n   # typeAliasesPackage: com.ruoyi.homecare.**\n    # \n    #mapperLocations: classpath:mapper/**/**/*.xml\n    #configuration:\n     #  map-underscore-to-camel-case: true\nmybatis-plus:\n    #\n    mapper-locations: classpath:mapper/**/**/*.xml\n    # \n    type-aliases-package: com.ruoyi.homecare.**\n    configuration:\n        # \n        map-underscore-to-camel-case: true  \n\nlogging:\n  level:\n    root: INFO\n    com.ruoyi: debug\n\n\n# swagger\nswagger:\n  title: jujiayanglao\n  license: Powered By ruoyi\n  licenseUrl: https://ruoyi.vip\n\n\nwx:\n  #\n  #v2 \n  appsecret: 0e5db3fd3cc6197dd68a785282dc1623\n  # appsecret: 78f1f354f767746171bcf957fb3fd3ec\n  pay:\n    appId: wxb7125f4f902fb0fe\n    # appid: wxa89aa4c24fca8ad4\n    mchId: **********\n    mchKey: dadba017qa3c7q4a8aq96bcq32df7fc5\n    apiV3Key: 2cc9f595e4d2616s865d1aa7wa13fbf4\n\nxcx:\n  appid: wxb7125f4f902fb0fe\n  appsecret: 0e5db3fd3cc6197dd68a785282dc1623\ngzh:\n  appid: wx4ab92103bfd594e7\n  appsecret: 6f297693d2cc0225a16e2f0ffdcece0c\nreserve:\n # appid: wx4ab92103bfd594e7\n # appsecret: 6f297693d2cc0225a16e2f0ffdcece0c\n  appid: wxa89aa4c24fca8ad4\n  appsecret: 78f1f354f767746171bcf957fb3fd3ec\n\nfile:\n  prefix: /statics\n  domain: http://**************:23360/file\n  path: /path', '133f0f24ddd8dec53d77828e32cc26c9', '2024-11-14 08:51:07', '2024-11-14 08:51:07', NULL, '*************', '', 'test', '', NULL, NULL, 'yaml', NULL);
INSERT INTO `config_info` VALUES (180, 'ruoyi-daycare-test.yml', 'DEFAULT_GROUP', '# spring配置\nspring: \n  servlet:\n    multipart:\n      maxFileSize:  1000MB\n      maxRequestSize: 1000MB\n  main:\n    allow-circular-references: true\n    allow-bean-definition-overriding: true\n  redis:\n    host: localhost\n    port: 6379\n    password: \n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: admin\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: *****************************************************************************************************************************************************************************            username: root\n            password: dabojin_2022\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n      # seata: true    # 开启seata代理，开启后默认每个数据源都代理，如果某个不需要代理可单独关闭\n\n# seata配置\nseata:\n  # 默认关闭，如需启用spring.datasource.dynami.seata需要同时开启\n  enabled: false\n  # Seata 应用编号，默认为 ${spring.application.name}\n  application-id: ${spring.application.name}\n  # Seata 事务组编号，用于 TC 集群名\n  tx-service-group: ${spring.application.name}-group\n  # 关闭自动代理\n  enable-auto-data-source-proxy: false\n  # 服务配置项\n  service:\n    # 虚拟组和分组的映射\n    vgroup-mapping:\n      ruoyi-system-group: default\n  config:\n    type: nacos\n    nacos:\n      serverAddr: 127.0.0.1:8848\n      group: SEATA_GROUP\n      namespace:\n  registry:\n    type: nacos\n    nacos:\n      application: seata-server\n      server-addr: 127.0.0.1:8848\n      namespace:\n\n# mybatis配置\nmybatis:\n    # 搜索指定包别名\n    typeAliasesPackage: com.ruoyi.daycare.**\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/**/*.xml\n    configuration:\n       map-underscore-to-camel-case: true\nlogging:\n  level:\n    root: INFO\n    com.ruoyi: debug\n    com.github.binarywang.demo.wx.pay: debug\n    com.github.binarywang.wxpay: debug\n\n# swagger配置\nswagger:\n  title: 日间照料模块接口文档\n  license: Powered By ruoyi\n  licenseUrl: https://ruoyi.vip', 'f1f2602fc0e412a9f330041469063a2f', '2024-11-14 08:51:07', '2024-11-14 08:51:07', NULL, '*************', '', 'test', '日间照料模块', NULL, NULL, 'yaml', NULL);
INSERT INTO `config_info` VALUES (181, 'application-test.yml', 'DEFAULT_GROUP', '#     \nfile:\n    #domain: http://127.0.0.1:9300\n    domain: http://**************:23407/test-api/file\n    path: /www/springbootjar/uploadPath\n    prefix: /statics\nspring:\n  main:\n    allow-circular-references: true\n    allow-bean-definition-overriding: true\n  autoconfigure:\n    exclude: com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure\n  mvc:\n    pathmatch:\n      matching-strategy: ant_path_matcher\n  cloud:\n    sentinel:\n      filter:\n        # \n        enabled: false\n  servlet:\n    multipart:\n      max-request-size: 100MB\n      max-file-size: 100MB\n\n# \nfeign:\n  sentinel:\n    enabled: true\n  okhttp:\n    enabled: true\n  httpclient:\n    enabled: false\n  client:\n    config:\n      default:\n        connectTimeout: 10000\n        readTimeout: 10000\n  compression:\n    request:\n      enabled: true\n    response:\n      enabled: true\n\n# \nmanagement:\n  endpoints:\n    web:\n      exposure:\n        include: \'*\'\n', 'fc7aa5864b169e7253987e3d1854e539', '2024-11-14 08:51:07', '2024-11-14 09:22:25', 'nacos', '*************', '', 'test', '通用配置', '', '', 'yaml', '');
INSERT INTO `config_info` VALUES (182, 'ruoyi-gateway-test.yml', 'DEFAULT_GROUP', 'spring:\n  redis:\n    host: 127.0.0.1\n    port: 6379\n    password: \n  cloud:\n    gateway:\n      discovery:\n        locator:\n          lowerCaseServiceId: true\n          enabled: true\n      routes:\n        # 认证中心\n        - id: ruoyi-auth\n          uri: lb://ruoyi-auth\n          predicates:\n            - Path=/auth/**\n          filters:\n            # 验证码处理\n            - CacheRequestFilter\n            - ValidateCodeFilter\n            - StripPrefix=1\n        # 代码生成\n        - id: ruoyi-gen\n          uri: lb://ruoyi-gen\n          predicates:\n            - Path=/code/**\n          filters:\n            - StripPrefix=1\n        # 定时任务\n        - id: ruoyi-job\n          uri: lb://ruoyi-job\n          predicates:\n            - Path=/schedule/**\n          filters:\n            - StripPrefix=1\n        # 系统模块\n        - id: ruoyi-system\n          uri: lb://ruoyi-system\n          predicates:\n            - Path=/system/**\n          filters:\n            - StripPrefix=1\n        # 文件服务\n        - id: ruoyi-file\n          uri: lb://ruoyi-file\n          predicates:\n            - Path=/file/**\n          filters:\n            - StripPrefix=1\n        # 客户服务\n        - id: ruoyi-custom\n          uri: lb://ruoyi-custom\n          predicates:\n            - Path=/custom/**\n          filters:\n            - StripPrefix=1\n        # 居家服务\n        - id: ruoyi-homecare\n          uri: lb://ruoyi-homecare\n          predicates:\n            - Path=/homecare/**\n          filters:\n            - StripPrefix=1\n        # 日间照料服务\n        - id: ruoyi-daycare\n          uri: lb://ruoyi-daycare\n          predicates:\n            - Path=/daycare/**\n          filters:\n            - StripPrefix=1\n         # IBMS-资产\n        - id: ruoyi-assets\n          uri: lb://ruoyi-assets\n          predicates:\n            - Path=/assets/**\n          filters:\n            - StripPrefix=1  \n         # IBMS-物业\n        - id: ruoyi-realty\n          uri: lb://ruoyi-realty\n          predicates:\n            - Path=/realty/**\n          filters:\n            - StripPrefix=1  \n         # IBMS-安防\n        - id: ruoyi-security\n          uri: lb://ruoyi-security\n          predicates:\n            - Path=/security/**\n          filters:\n            - StripPrefix=1  \n\n# 安全配置\nsecurity:\n  # 验证码\n  captcha:\n    configs:\n      # 应用及设置，默认关闭\n      default:\n        enabled: true\n        type: math\n      custom_pc_admin:\n        enabled: true\n        type: math\n      custom_applet_lryh:\n        enabled: false\n        type: math\n      custom_app_hg:\n        enabled: true\n        type: math\n\n  # 防止XSS攻击\n  xss:\n    enabled: true\n    excludeUrls:\n      - /system/notice\n  # 不校验白名单\n  ignore:\n    whites:\n      - /auth/logout\n      - /auth/login\n      - /auth/register\n      - /auth/registerByRegisteredVisitor\n      - /auth/getOpenIdByCode\n      - /auth/getTokenByOpenId\n      - /auth/appLogin\n      - /auth/businessLogin\n      - /auth/statics/**\n      - /*/v2/api-docs\n      - /csrf\n      - /custom/careWorkerInfo/careLogin\n      - /system/dict/data/type/**\n      - /homecare/homeCommunityBaseInfo/getCommunityList\n      - /file/**\n      - /doc.html\n      - /homecare/HomeRegisterServiceProvider/registerServiceProvider\n      - /homecare/homeServiceProviderManagement/getServiceProviderLabel\n      - /homecare/appApi/getOpenIdByCode\n      - /homecare/appApi/getTokenByOpenId\n      - /homecare/homeServiceProviderManagement/getIgnoreServiceProviderLabel\n      - /homecare/wechatPay/notify/**\n      - /homecare/appApi/xxx/onWxMsg\n      - /system/dict/data/type/**\n      - /homecare/homeCommunityBaseInfo/getCommunityList\n      - /homecare/homeTimeCoinSettings/getSettingsList\n      - /homecare/homeStreetInfo/list\n      - /homecare/homeAppServicePlaceOrderVolunteerInfo/register\n      - /homecare/homeStreetInfo/ignoreWhiteTreeselect\n      - /homecare/appApi/reserve/login\n      - /homecare/appApi/reserve/getOpenIdByCode\n      - /homecare/appApi/reserve/getTokenByOpenId\n      - /homecare/statics/**\n      - /*/app/rest/models/*/bpmn20 \n      - /assets/fixedAssetsArchivesBase/getById \n      - /security/visual/getData\n      - /realty/visitor/addByScan\n      - /assets/fixedAssetsArchivesBase/getById\n      - /security-js/visual/getData\n      - /energy/**', '39749cb9fb8592f5fc09b25f03a49c5f', '2024-11-14 08:51:07', '2025-03-11 08:25:10', 'nacos', '*************', '', 'test', '网关模块', '', '', 'yaml', '');
INSERT INTO `config_info` VALUES (183, 'ruoyi-auth-test.yml', 'DEFAULT_GROUP', 'spring: \n  redis:\n    host: 127.0.0.1\n    port: 6379\n    password: \n# swagger配置\nswagger:\n  title: 权限接口文档\n  license: Powered By ruoyi\n  licenseUrl: https://ruoyi.vip\nwx:\n  miniapp:\n    configs:\n      # 资产小程序\n      - appid: wxd6928057261bd32a\n        secret: 5606fe89dddc3a0260123076bd225093\n        msgDataFormat: JSON\n      # 养老测试\n      - appid: wxb7125f4f902fb0fe\n        secret:  0e5db3fd3cc6197dd68a785282dc1623\n        msgDataFormat: JSON\n      # 科技园区管理端\n      - appid: wx58f02809634e38cd\n        secret:  01ff8ff4587054c6be040b0581505947\n        msgDataFormat: JSON\n      # 科技园区企业端\n      - appid: wx942a2e3c254e01ce\n        secret:  39bc3b298a548b678faeade6c2a0c349\n        msgDataFormat: JSON', '86973c2ce3446070cfd72753678c23e2', '2024-11-14 08:51:07', '2024-11-15 01:27:49', 'nacos', '*************', '', 'test', '认证中心', '', '', 'yaml', '');
INSERT INTO `config_info` VALUES (184, 'ruoyi-monitor-test.yml', 'DEFAULT_GROUP', '# spring\r\nspring: \r\n  security:\r\n    user:\r\n      name: ruoyi\r\n      password: 123456\r\n  boot:\r\n    admin:\r\n      ui:\r\n        title: 若依服务状态监控\r\n', 'd8997d0707a2fd5d9fc4e8409da38129', '2024-11-14 08:51:07', '2024-11-14 08:51:07', NULL, '*************', '', 'test', '监控中心', NULL, NULL, 'yaml', NULL);
INSERT INTO `config_info` VALUES (185, 'ruoyi-gen-test.yml', 'DEFAULT_GROUP', '# spring配置\nspring: \n  redis:\n    host: 127.0.0.1\n    port: 6379\n    password: \n  datasource: \n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: ***************************************************************************************************************************************************    username: ry-cloud\n    password: rNsALTSiyXSWKczx\n\n# mybatis配置\nmybatis-plus:\n  # 搜索指定包别名\n  typeAliasesPackage: com.ruoyi.gen.domain\n  # 配置mapper的扫描，找到所有的mapper.xml映射文件\n  mapperLocations: classpath:mapper/**/*.xml\n\n# swagger配置\nswagger:\n  title: 代码生成接口文档\n  license: Powered By ruoyi\n  licenseUrl: https://ruoyi.vip\n\n# 代码生成\ngen: \n  # 作者\n  author: zkx\n  # 默认生成包路径 system 需改成自己的模块名称 如 system monitor tool\n  packageName: com.ruoyi.custom\n  # 自动去除表前缀，默认是false\n  autoRemovePre: true\n  # 表前缀（生成类名不会包含表前缀，多个用逗号分隔）\n  tablePrefix: sys_,t_\n', '654d6855a966f5b0fe01bb62e9d9f43b', '2024-11-14 08:51:07', '2024-11-15 03:39:45', 'nacos', '*************', '', 'test', '代码生成', '', '', 'yaml', '');
INSERT INTO `config_info` VALUES (186, 'ruoyi-job-test.yml', 'DEFAULT_GROUP', '# spring配置\nspring: \n  redis:\n    host: 127.0.0.1\n    port: 6379\n    password: \n  datasource:\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: ***************************************************************************************************************************************************    username: ry-cloud\n    password: rNsALTSiyXSWKczx\n\n# mybatis配置\nmybatis-plus:\n    # 搜索指定包别名\n    typeAliasesPackage: com.ruoyi.job.domain\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/*.xml\n\n# swagger配置\nswagger:\n  title: 定时任务接口文档\n  license: Powered By ruoyi\n  licenseUrl: https://ruoyi.vip\n', 'd89243666118d8682a9ee3758cb52d59', '2024-11-14 08:51:07', '2024-11-15 01:28:17', 'nacos', '*************', '', 'test', '定时任务', '', '', 'yaml', '');
INSERT INTO `config_info` VALUES (187, 'ruoyi-file-test.yml', 'DEFAULT_GROUP', '# 本地文件上传    \nfile:\n  domain: http://**************:23407/test-api/file\n  path: /www/springbootjar/uploadPath\n  prefix: /statics\n\n# FastDFS配置\nfdfs:\n  domain: http://************\n  soTimeout: 3000\n  connectTimeout: 2000\n  trackerList: ************:22122\n\n# Minio配置\nminio:\n  url: http://************:9000\n  accessKey: minioadmin\n  secretKey: minioadmin\n  bucketName: test', 'ca553e2a99cb6d6abe333ae45cf0022c', '2024-11-14 08:51:07', '2024-11-14 09:24:23', 'nacos', '*************', '', 'test', '文件服务', '', '', 'yaml', '');
INSERT INTO `config_info` VALUES (188, 'ruoyi-custom-test.yml', 'DEFAULT_GROUP', '# spring配置\nspring: \n  servlet:\n    multipart:\n      maxFileSize:  1000MB\n      maxRequestSize: 1000MB\n  main:\n    allow-circular-references: true\n    allow-bean-definition-overriding: true\n  redis:\n    host: 127.0.0.1\n    port: 6379\n    password: \n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: admin\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: ***************************************************************************************************************************************************************************            username: ry-custom\n            password: m782cLf8Jst55rDK\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n      # seata: true    # 开启seata代理，开启后默认每个数据源都代理，如果某个不需要代理可单独关闭\n\n# seata配置\nseata:\n  # 默认关闭，如需启用spring.datasource.dynami.seata需要同时开启\n  enabled: false\n  # Seata 应用编号，默认为 ${spring.application.name}\n  application-id: ${spring.application.name}\n  # Seata 事务组编号，用于 TC 集群名\n  tx-service-group: ${spring.application.name}-group\n  # 关闭自动代理\n  enable-auto-data-source-proxy: false\n  # 服务配置项\n  service:\n    # 虚拟组和分组的映射\n    vgroup-mapping:\n      ruoyi-system-group: default\n  config:\n    type: nacos\n    nacos:\n      serverAddr: 127.0.0.1:8848\n      group: SEATA_GROUP\n      namespace:\n  registry:\n    type: nacos\n    nacos:\n      application: seata-server\n      server-addr: 127.0.0.1:8848\n      namespace:\n\n# mybatis配置\nmybatis-plus:\n    # 搜索指定包别名\n    typeAliasesPackage: com.ruoyi.custom.admin.**\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/**/*.xml\n    configuration:\n       map-underscore-to-camel-case: true\n\n# swagger配置\nswagger:\n  title: 客户模块接口文档\n  license: Powered By ruoyi\n  licenseUrl: https://ruoyi.vip\n\n# 微信小程序相关\nwx:\n  #\n  #v2 \n  appsecret: 0e5db3fd3cc6197dd68a785282dc1623\n  # appsecret: 78f1f354f767746171bcf957fb3fd3ec\n  pay:\n    appId: wxb7125f4f902fb0fe\n    # appid: wxa89aa4c24fca8ad4\n    mchId: **********\n    mchKey: dadba017qa3c7q4a8aq96bcq32df7fc5\n    apiV3Key: 2cc9f595e4d2616s865d1aa7wa13fbf4\n    keyPath: classpath:wxcertificate/apiclient_cert.p12\n    privateKeyPath: classpath:wxcertificate/apiclient_key.pem\n    privateCertPath: classpath:wxcertificate/apiclient_cert.pem', '2c4c2f926398155553b695cf24cb4044', '2024-11-14 08:51:32', '2024-12-17 01:34:21', 'nacos', '*************', '', 'test', '客户模块', '', '', 'yaml', '');
INSERT INTO `config_info` VALUES (189, 'ruoyi-system-test.yml', 'DEFAULT_GROUP', '# spring配置\nspring: \n  redis:\n    host: 127.0.0.1\n    port: 6379\n    password: \n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: admin\n        loginPassword: 123456\n    dynamic:\n      primary: master\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: ***************************************************************************************************************************************************            username: ry-cloud\n            password: rNsALTSiyXSWKczx\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n      # seata: true    # 开启seata代理，开启后默认每个数据源都代理，如果某个不需要代理可单独关闭\n\n# seata配置\nseata:\n  # 默认关闭，如需启用spring.datasource.dynami.seata需要同时开启\n  enabled: false\n  # Seata 应用编号，默认为 ${spring.application.name}\n  application-id: ${spring.application.name}\n  # Seata 事务组编号，用于 TC 集群名\n  tx-service-group: ${spring.application.name}-group\n  # 关闭自动代理\n  enable-auto-data-source-proxy: false\n  # 服务配置项\n  service:\n    # 虚拟组和分组的映射\n    vgroup-mapping:\n      ruoyi-system-group: default\n  config:\n    type: nacos\n    nacos:\n      serverAddr: 127.0.0.1:8848\n      group: SEATA_GROUP\n      namespace:\n  registry:\n    type: nacos\n    nacos:\n      application: seata-server\n      server-addr: 127.0.0.1:8848\n      namespace:\n\n# mybatis配置\nmybatis-plus:\n    # 搜索指定包别名\n    typeAliasesPackage: com.ruoyi.system\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/*.xml\n\n# swagger配置\nswagger:\n  title: 系统模块接口文档\n  license: Powered By ruoyi\n  licenseUrl: https://ruoyi.vip', '431a77fa4d682a981d3a1a9a15ee6d0f', '2024-11-14 08:51:32', '2024-11-14 10:13:55', 'nacos', '*************', '', 'test', '系统模块', '', '', 'yaml', '');
INSERT INTO `config_info` VALUES (190, 'ruoyi-assets-test.yml', 'DEFAULT_GROUP', '# spring配置\nspring:\n  redis:\n    host: 127.0.0.1\n    port: 6379\n    password: \n    timeout: 10000\n  datasource:\n    type: com.alibaba.druid.pool.DruidDataSource\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: ***************************************************************************************************************************************************************************************************    username: ry-assets\n    password: EmeN33FwA7SxXtAr\n# mybatis-plus配置\nmybatis-plus:\n  mapper-locations: classpath*:/mapper/**/*Mapper.xml,classpath:/META-INF/modeler-mybatis-mappings/*.xml\n  typeAliasesPackage: com.ibms.service.**.domain\n  configuration:\n        log-impl: org.apache.ibatis.logging.stdout.StdOutImpl\n  #驼峰命名     \n  map-underscore-to-camel-case: true\n  configuration-properties:\n    blobType: BLOB\n    boolValue: true\n    prefix: \'\'\nflowable:\n  # 关闭定时任务Job\n  async-executor-activate: false\n  check-process-definitions: false\n  process-definition-location-prefix: classpath:/processes/\n  #是否检测表生成\n  database-schema-update: true\n  # properties:\n  #   blobType: BLOB\n  #   boolValue: true\n  #   prefix: \'\'\n  common:\n    app:\n      idm-url: http://localhost:9999\n      idm-admin:\n        user: admin\n        password: test\n# 本地文件地址\nfile:\n    front-domain: http://**************:23407\n    back-domain: http://**************:23407/test-api/file\n    # assetsRqUrl: http://**************:23370/prod-api/assets/\n    path: /www/springbootjar/uploadPath\n    prefix: /statics\n# swagger配置\nswagger:\n  title: 资产管理模块\n  license: Powered By 大博金-研发部\n  licenseUrl: https://www.dabojinkj.com', '5aecf901e91d44147efd809376fea5a2', '2024-11-14 08:51:32', '2024-11-14 09:26:58', 'nacos', '*************', '', 'test', 'IBMS-资产管理', '', '', 'yaml', '');
INSERT INTO `config_info` VALUES (191, 'ruoyi-realty-test.yml', 'DEFAULT_GROUP', '# spring配置\nspring:\n  redis:\n    host: 127.0.0.1\n    port: 6379\n    password: \n    timeout: 5000\n  datasource:\n    type: com.zaxxer.hikari.HikariDataSource\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: ****************************************************************************************************************************************************    username: ry-realty\n    password: EFAAD74SAis7nmSK\n      #使用HikariCP连接池\n    hikari:\n      connection-timeout: 5000 #连接超时时间，默认30s\n      maximum-pool-size: 20 #连接池最大数量，默认10\n      minimum-idle: 5 #连接池最小空闲连接数，默认10\n      idleTimeout: 60000 #连接的最大空闲时间，60s\n      maxLifetime: 1800000 #连接的最大生命周期，30min\n      # 开启JMX支持服务，方便状态监控\n      registerMbeans: true\n      jmxName: \'com.zaxxer.hikari:type=DataSource\'\n    \n# mybatis-plus配置\nmybatis-plus:\n  typeAliasesPackage: com.ibms.service.realty.web.domain\n  mapper-locations: classpath*:mapper/*Mapper.xml\n  #mybatis-plus配置控制台打印完整带参数SQL语句\n  configuration: \n    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl\n    #HikariCP连接池实现JDBC数据源\n    dataSourceType: com.zaxxer.hikari.HikariDataSource\n\n\n# swagger配置\nswagger:\n  title: 物业模块\n  license: Powered By 大博金-研发部\n  licenseUrl: https://www.dabojinkj.com\n\n# 后台域名\nbackground-domain: http://*************\n\n# 前端ng域名\nfront-domain: http://**************:23409', '96f4eea0a7ebf9cb0ff781baa3d8771c', '2024-11-14 08:51:32', '2024-12-18 01:50:50', 'nacos', '*************', '', 'test', '', '', '', 'yaml', '');
INSERT INTO `config_info` VALUES (230, 'ruoyi-security-local.yml', 'DEFAULT_GROUP', '# spring配置\nspring:\n  redis:\n    host: 127.0.0.1\n    port: 6379\n    password: \n    timeout: 5000\n  datasource:\n    type: com.zaxxer.hikari.HikariDataSource\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: jdbc:mysql://**************:23403/ry-security?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8\n    username: ry-security\n    password: rLdbtEjDfyGpDWDx\n    hikari:\n      connection-timeout: 5000 #连接超时时间，默认30s\n      maximum-pool-size: 20 #连接池最大数量，默认10\n      minimum-idle: 5 #连接池最小空闲连接数，默认10\n      idleTimeout: 60000 #连接的最大空闲时间，60s\n      maxLifetime: 1800000 #连接的最大生命周期，30min\n      # 开启JMX支持服务，方便状态监控\n      registerMbeans: true\n      jmxName: \'com.zaxxer.hikari:type=DataSource\'\n  fastjson:\n    parser:\n      autoTypeAccept: com.ruoyi.system.api.domain.SysDictData  # 设置允许反序列化的包路径\n      autoTypeSupport: true  # 开启 autoType 功能\n      safeMode: true  # 开启 safeMode 模式\n    \n# mybatis-plus配置\nmybatis-plus:\n  typeAliasesPackage: com.ibms.service.**.domain\n  mapper-locations: classpath*:/mapper/**/*Mapper.xml,classpath:/META-INF/modeler-mybatis-mappings/*.xml\n  #mybatis-plus配置控制台打印完整带参数SQL语句\n  configuration: \n    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl\n    #HikariCP连接池实现JDBC数据源\n    dataSourceType: com.zaxxer.hikari.HikariDataSource\n  configuration-properties:\n    blobType: BLOB\n    boolValue: true\n    prefix: \'\'\n  global-config:\n    enable-sql-runner: true\n\nflowable:\n  # 关闭定时任务Job\n  async-executor-activate: false\n  check-process-definitions: false\n  process-definition-location-prefix: classpath:/processes/\n  #是否检测表生成\n  database-schema-update: true\n  # properties:\n  #   blobType: BLOB\n  #   boolValue: true\n  #   prefix: \'\'\n  common:\n    app:\n      idm-url: http://localhost:9999\n      idm-admin:\n        user: admin\n        password: test\n\n# swagger配置\nswagger:\n  title: 安防模块\n  license: Powered By 大博金-研发部\n  licenseUrl: https://www.dabojinkj.com\n\n# Qanything 知识库id\nQanything:\n  knowledge:\n    kbid: KB78ffd982d5b54d089d606a12fecf7e2f\n\n# 文件地址，跟file模块同步\nfile:\n    domain: http://*************:8080/file\n    path: D:/ruoyi/uploadPath\n    prefix: /statics', '7c57742f19f5ceb3d5cb21469e491d88', '2025-02-24 02:16:23', '2025-02-24 09:12:15', 'nacos', '*************', '', 'local', '', '', '', 'yaml', '');
INSERT INTO `config_info` VALUES (232, 'ruoyi-security-test.yml', 'DEFAULT_GROUP', '# spring配置\nspring:\n  redis:\n    host: 127.0.0.1\n    port: 6379\n    password: \n    timeout: 5000\n  datasource:\n    type: com.zaxxer.hikari.HikariDataSource\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: ******************************************************************************************************************************************************    username: ry-security\n    password: rLdbtEjDfyGpDWDx\n    hikari:\n      connection-timeout: 5000 #连接超时时间，默认30s\n      maximum-pool-size: 20 #连接池最大数量，默认10\n      minimum-idle: 5 #连接池最小空闲连接数，默认10\n      idleTimeout: 60000 #连接的最大空闲时间，60s\n      maxLifetime: 1800000 #连接的最大生命周期，30min\n      # 开启JMX支持服务，方便状态监控\n      registerMbeans: true\n      jmxName: \'com.zaxxer.hikari:type=DataSource\'\n  fastjson:\n    parser:\n      autoTypeAccept: com.ruoyi.system.api.domain.SysDictData  # 设置允许反序列化的包路径\n      autoTypeSupport: true  # 开启 autoType 功能\n      safeMode: true  # 开启 safeMode 模式\n    \n# mybatis-plus配置\nmybatis-plus:\n  typeAliasesPackage: com.ibms.service.**.domain\n  mapper-locations: classpath*:/mapper/**/*Mapper.xml,classpath:/META-INF/modeler-mybatis-mappings/*.xml\n  #mybatis-plus配置控制台打印完整带参数SQL语句\n  configuration: \n    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl\n    #HikariCP连接池实现JDBC数据源\n    dataSourceType: com.zaxxer.hikari.HikariDataSource\n  configuration-properties:\n    blobType: BLOB\n    boolValue: true\n    prefix: \'\'\n  global-config:\n    enable-sql-runner: true\n\nflowable:\n  # 关闭定时任务Job\n  async-executor-activate: false\n  check-process-definitions: false\n  process-definition-location-prefix: classpath:/processes/\n  #是否检测表生成\n  database-schema-update: true\n  # properties:\n  #   blobType: BLOB\n  #   boolValue: true\n  #   prefix: \'\'\n  common:\n    app:\n      idm-url: http://localhost:9999\n      idm-admin:\n        user: admin\n        password: test\n\n# swagger配置\nswagger:\n  title: 安防模块\n  license: Powered By 大博金-研发部\n  licenseUrl: https://www.dabojinkj.com\n\n# Qanything 知识库id\nQanything:\n  knowledge:\n    kbid: KB78ffd982d5b54d089d606a12fecf7e2f\n\n# 文件地址，跟file模块同步\nfile:\n    domain: http://**************:23407/test-api/file\n    path: /www/springbootjar/uploadPath\n    prefix: /statics', '02394284e2fc773cf3bcaad2ac8a1247', '2025-03-10 06:44:02', '2025-03-10 06:46:44', 'nacos', '*************', '', 'test', '', '', '', 'yaml', '');

-- ----------------------------
-- Table structure for config_info_aggr
-- ----------------------------
DROP TABLE IF EXISTS `config_info_aggr`;
CREATE TABLE `config_info_aggr`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `data_id` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT 'data_id',
  `group_id` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT 'group_id',
  `datum_id` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT 'datum_id',
  `content` longtext CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '内容',
  `gmt_modified` datetime NOT NULL COMMENT '修改时间',
  `app_name` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `tenant_id` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT '' COMMENT '租户字段',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_configinfoaggr_datagrouptenantdatum`(`data_id` ASC, `group_id` ASC, `tenant_id` ASC, `datum_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_bin COMMENT = '增加租户字段' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of config_info_aggr
-- ----------------------------

-- ----------------------------
-- Table structure for config_info_beta
-- ----------------------------
DROP TABLE IF EXISTS `config_info_beta`;
CREATE TABLE `config_info_beta`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `data_id` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT 'data_id',
  `group_id` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT 'group_id',
  `app_name` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT 'app_name',
  `content` longtext CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT 'content',
  `beta_ips` varchar(1024) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT 'betaIps',
  `md5` varchar(32) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT 'md5',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  `src_user` text CHARACTER SET utf8 COLLATE utf8_bin NULL COMMENT 'source user',
  `src_ip` varchar(50) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT 'source ip',
  `tenant_id` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT '' COMMENT '租户字段',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_configinfobeta_datagrouptenant`(`data_id` ASC, `group_id` ASC, `tenant_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_bin COMMENT = 'config_info_beta' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of config_info_beta
-- ----------------------------

-- ----------------------------
-- Table structure for config_info_tag
-- ----------------------------
DROP TABLE IF EXISTS `config_info_tag`;
CREATE TABLE `config_info_tag`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `data_id` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT 'data_id',
  `group_id` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT 'group_id',
  `tenant_id` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT '' COMMENT 'tenant_id',
  `tag_id` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT 'tag_id',
  `app_name` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT 'app_name',
  `content` longtext CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT 'content',
  `md5` varchar(32) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT 'md5',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  `src_user` text CHARACTER SET utf8 COLLATE utf8_bin NULL COMMENT 'source user',
  `src_ip` varchar(50) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT 'source ip',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_configinfotag_datagrouptenanttag`(`data_id` ASC, `group_id` ASC, `tenant_id` ASC, `tag_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_bin COMMENT = 'config_info_tag' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of config_info_tag
-- ----------------------------

-- ----------------------------
-- Table structure for config_tags_relation
-- ----------------------------
DROP TABLE IF EXISTS `config_tags_relation`;
CREATE TABLE `config_tags_relation`  (
  `id` bigint NOT NULL COMMENT 'id',
  `tag_name` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT 'tag_name',
  `tag_type` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT 'tag_type',
  `data_id` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT 'data_id',
  `group_id` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT 'group_id',
  `tenant_id` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT '' COMMENT 'tenant_id',
  `nid` bigint NOT NULL AUTO_INCREMENT,
  PRIMARY KEY (`nid`) USING BTREE,
  UNIQUE INDEX `uk_configtagrelation_configidtag`(`id` ASC, `tag_name` ASC, `tag_type` ASC) USING BTREE,
  INDEX `idx_tenant_id`(`tenant_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_bin COMMENT = 'config_tag_relation' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of config_tags_relation
-- ----------------------------

-- ----------------------------
-- Table structure for group_capacity
-- ----------------------------
DROP TABLE IF EXISTS `group_capacity`;
CREATE TABLE `group_capacity`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `group_id` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT '' COMMENT 'Group ID，空字符表示整个集群',
  `quota` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '配额，0表示使用默认值',
  `usage` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '使用量',
  `max_size` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '单个配置大小上限，单位为字节，0表示使用默认值',
  `max_aggr_count` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '聚合子配置最大个数，，0表示使用默认值',
  `max_aggr_size` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '单个聚合数据的子配置大小上限，单位为字节，0表示使用默认值',
  `max_history_count` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '最大变更历史数量',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_group_id`(`group_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_bin COMMENT = '集群、各Group容量信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of group_capacity
-- ----------------------------

-- ----------------------------
-- Table structure for his_config_info
-- ----------------------------
DROP TABLE IF EXISTS `his_config_info`;
CREATE TABLE `his_config_info`  (
  `id` bigint UNSIGNED NOT NULL,
  `nid` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `data_id` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
  `group_id` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
  `app_name` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT 'app_name',
  `content` longtext CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
  `md5` varchar(32) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `src_user` text CHARACTER SET utf8 COLLATE utf8_bin NULL,
  `src_ip` varchar(50) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `op_type` char(10) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `tenant_id` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT '' COMMENT '租户字段',
  PRIMARY KEY (`nid`) USING BTREE,
  INDEX `idx_gmt_create`(`gmt_create` ASC) USING BTREE,
  INDEX `idx_gmt_modified`(`gmt_modified` ASC) USING BTREE,
  INDEX `idx_did`(`data_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 378 CHARACTER SET = utf8 COLLATE = utf8_bin COMMENT = '多租户改造' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of his_config_info
-- ----------------------------
INSERT INTO `his_config_info` VALUES (0, 365, 'ibms-service-security-local.yml', 'DEFAULT_GROUP', '', '# spring配置\r\nspring:\r\n  redis:\r\n    host: 127.0.0.1\r\n    port: 6379\r\n    password: \r\n    timeout: 5000\r\n  datasource:\r\n    type: com.zaxxer.hikari.HikariDataSource\r\n    driver-class-name: com.mysql.cj.jdbc.Driver\r\n    url: jdbc:mysql://**************:23412/ibms_security?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8\r\n    username: ibms_security\r\n    password: Hcic2H8zKeXR5aa3\r\n    hikari:\r\n      connection-timeout: 5000 #连接超时时间，默认30s\r\n      maximum-pool-size: 20 #连接池最大数量，默认10\r\n      minimum-idle: 5 #连接池最小空闲连接数，默认10\r\n      idleTimeout: 60000 #连接的最大空闲时间，60s\r\n      maxLifetime: 1800000 #连接的最大生命周期，30min\r\n      # 开启JMX支持服务，方便状态监控\r\n      registerMbeans: true\r\n      jmxName: \'com.zaxxer.hikari:type=DataSource\'\r\n  fastjson:\r\n    parser:\r\n      autoTypeAccept: com.ruoyi.system.api.domain.SysDictData  # 设置允许反序列化的包路径\r\n      autoTypeSupport: true  # 开启 autoType 功能\r\n      safeMode: true  # 开启 safeMode 模式\r\n    \r\n# mybatis-plus配置\r\nmybatis-plus:\r\n  typeAliasesPackage: com.ibms.service.**.domain\r\n  mapper-locations: classpath*:/mapper/**/*Mapper.xml,classpath:/META-INF/modeler-mybatis-mappings/*.xml\r\n  #mybatis-plus配置控制台打印完整带参数SQL语句\r\n  configuration: \r\n    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl\r\n    #HikariCP连接池实现JDBC数据源\r\n    dataSourceType: com.zaxxer.hikari.HikariDataSource\r\n  configuration-properties:\r\n    blobType: BLOB\r\n    boolValue: true\r\n    prefix: \'\'\r\n  global-config:\r\n    enable-sql-runner: true\r\n\r\nflowable:\r\n  # 关闭定时任务Job\r\n  async-executor-activate: false\r\n  check-process-definitions: false\r\n  process-definition-location-prefix: classpath:/processes/\r\n  #是否检测表生成\r\n  database-schema-update: true\r\n  # properties:\r\n  #   blobType: BLOB\r\n  #   boolValue: true\r\n  #   prefix: \'\'\r\n  common:\r\n    app:\r\n      idm-url: http://localhost:9999\r\n      idm-admin:\r\n        user: admin\r\n        password: test\r\n\r\n# swagger配置\r\nswagger:\r\n  title: IBMS-业务安防模块\r\n  license: Powered By 大博金-研发部\r\n  licenseUrl: https://www.dabojinkj.com\r\n\r\n# Qanything 知识库id\r\nQanything:\r\n  knowledge:\r\n    kbid: KB78ffd982d5b54d089d606a12fecf7e2f\r\n\r\n# 文件地址，跟file模块同步\r\nfile:\r\n    domain: http://*************:19300\r\n    path: D:/ruoyi/uploadPath\r\n    prefix: /statics', 'bace3a0b1844e277e627ab429dbc573e', '2025-02-24 10:15:23', '2025-02-24 02:15:22', NULL, '*************', 'I', 'local');
INSERT INTO `his_config_info` VALUES (229, 366, 'ibms-service-security-local.yml', 'DEFAULT_GROUP', '', '# spring配置\r\nspring:\r\n  redis:\r\n    host: 127.0.0.1\r\n    port: 6379\r\n    password: \r\n    timeout: 5000\r\n  datasource:\r\n    type: com.zaxxer.hikari.HikariDataSource\r\n    driver-class-name: com.mysql.cj.jdbc.Driver\r\n    url: jdbc:mysql://**************:23412/ibms_security?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8\r\n    username: ibms_security\r\n    password: Hcic2H8zKeXR5aa3\r\n    hikari:\r\n      connection-timeout: 5000 #连接超时时间，默认30s\r\n      maximum-pool-size: 20 #连接池最大数量，默认10\r\n      minimum-idle: 5 #连接池最小空闲连接数，默认10\r\n      idleTimeout: 60000 #连接的最大空闲时间，60s\r\n      maxLifetime: 1800000 #连接的最大生命周期，30min\r\n      # 开启JMX支持服务，方便状态监控\r\n      registerMbeans: true\r\n      jmxName: \'com.zaxxer.hikari:type=DataSource\'\r\n  fastjson:\r\n    parser:\r\n      autoTypeAccept: com.ruoyi.system.api.domain.SysDictData  # 设置允许反序列化的包路径\r\n      autoTypeSupport: true  # 开启 autoType 功能\r\n      safeMode: true  # 开启 safeMode 模式\r\n    \r\n# mybatis-plus配置\r\nmybatis-plus:\r\n  typeAliasesPackage: com.ibms.service.**.domain\r\n  mapper-locations: classpath*:/mapper/**/*Mapper.xml,classpath:/META-INF/modeler-mybatis-mappings/*.xml\r\n  #mybatis-plus配置控制台打印完整带参数SQL语句\r\n  configuration: \r\n    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl\r\n    #HikariCP连接池实现JDBC数据源\r\n    dataSourceType: com.zaxxer.hikari.HikariDataSource\r\n  configuration-properties:\r\n    blobType: BLOB\r\n    boolValue: true\r\n    prefix: \'\'\r\n  global-config:\r\n    enable-sql-runner: true\r\n\r\nflowable:\r\n  # 关闭定时任务Job\r\n  async-executor-activate: false\r\n  check-process-definitions: false\r\n  process-definition-location-prefix: classpath:/processes/\r\n  #是否检测表生成\r\n  database-schema-update: true\r\n  # properties:\r\n  #   blobType: BLOB\r\n  #   boolValue: true\r\n  #   prefix: \'\'\r\n  common:\r\n    app:\r\n      idm-url: http://localhost:9999\r\n      idm-admin:\r\n        user: admin\r\n        password: test\r\n\r\n# swagger配置\r\nswagger:\r\n  title: IBMS-业务安防模块\r\n  license: Powered By 大博金-研发部\r\n  licenseUrl: https://www.dabojinkj.com\r\n\r\n# Qanything 知识库id\r\nQanything:\r\n  knowledge:\r\n    kbid: KB78ffd982d5b54d089d606a12fecf7e2f\r\n\r\n# 文件地址，跟file模块同步\r\nfile:\r\n    domain: http://*************:19300\r\n    path: D:/ruoyi/uploadPath\r\n    prefix: /statics', 'bace3a0b1844e277e627ab429dbc573e', '2025-02-24 10:15:42', '2025-02-24 02:15:43', NULL, '*************', 'D', 'local');
INSERT INTO `his_config_info` VALUES (0, 367, 'ruoyi-security-local.yml', 'DEFAULT_GROUP', '', '# spring配置\r\nspring:\r\n  redis:\r\n    host: 127.0.0.1\r\n    port: 6379\r\n    password: \r\n    timeout: 5000\r\n  datasource:\r\n    type: com.zaxxer.hikari.HikariDataSource\r\n    driver-class-name: com.mysql.cj.jdbc.Driver\r\n    url: jdbc:mysql://**************:23412/ibms_security?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8\r\n    username: ibms_security\r\n    password: Hcic2H8zKeXR5aa3\r\n    hikari:\r\n      connection-timeout: 5000 #连接超时时间，默认30s\r\n      maximum-pool-size: 20 #连接池最大数量，默认10\r\n      minimum-idle: 5 #连接池最小空闲连接数，默认10\r\n      idleTimeout: 60000 #连接的最大空闲时间，60s\r\n      maxLifetime: 1800000 #连接的最大生命周期，30min\r\n      # 开启JMX支持服务，方便状态监控\r\n      registerMbeans: true\r\n      jmxName: \'com.zaxxer.hikari:type=DataSource\'\r\n  fastjson:\r\n    parser:\r\n      autoTypeAccept: com.ruoyi.system.api.domain.SysDictData  # 设置允许反序列化的包路径\r\n      autoTypeSupport: true  # 开启 autoType 功能\r\n      safeMode: true  # 开启 safeMode 模式\r\n    \r\n# mybatis-plus配置\r\nmybatis-plus:\r\n  typeAliasesPackage: com.ibms.service.**.domain\r\n  mapper-locations: classpath*:/mapper/**/*Mapper.xml,classpath:/META-INF/modeler-mybatis-mappings/*.xml\r\n  #mybatis-plus配置控制台打印完整带参数SQL语句\r\n  configuration: \r\n    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl\r\n    #HikariCP连接池实现JDBC数据源\r\n    dataSourceType: com.zaxxer.hikari.HikariDataSource\r\n  configuration-properties:\r\n    blobType: BLOB\r\n    boolValue: true\r\n    prefix: \'\'\r\n  global-config:\r\n    enable-sql-runner: true\r\n\r\nflowable:\r\n  # 关闭定时任务Job\r\n  async-executor-activate: false\r\n  check-process-definitions: false\r\n  process-definition-location-prefix: classpath:/processes/\r\n  #是否检测表生成\r\n  database-schema-update: true\r\n  # properties:\r\n  #   blobType: BLOB\r\n  #   boolValue: true\r\n  #   prefix: \'\'\r\n  common:\r\n    app:\r\n      idm-url: http://localhost:9999\r\n      idm-admin:\r\n        user: admin\r\n        password: test\r\n\r\n# swagger配置\r\nswagger:\r\n  title: IBMS-业务安防模块\r\n  license: Powered By 大博金-研发部\r\n  licenseUrl: https://www.dabojinkj.com\r\n\r\n# Qanything 知识库id\r\nQanything:\r\n  knowledge:\r\n    kbid: KB78ffd982d5b54d089d606a12fecf7e2f\r\n\r\n# 文件地址，跟file模块同步\r\nfile:\r\n    domain: http://*************:19300\r\n    path: D:/ruoyi/uploadPath\r\n    prefix: /statics', 'bace3a0b1844e277e627ab429dbc573e', '2025-02-24 10:16:22', '2025-02-24 02:16:23', NULL, '*************', 'I', 'local');
INSERT INTO `his_config_info` VALUES (230, 368, 'ruoyi-security-local.yml', 'DEFAULT_GROUP', '', '# spring配置\r\nspring:\r\n  redis:\r\n    host: 127.0.0.1\r\n    port: 6379\r\n    password: \r\n    timeout: 5000\r\n  datasource:\r\n    type: com.zaxxer.hikari.HikariDataSource\r\n    driver-class-name: com.mysql.cj.jdbc.Driver\r\n    url: jdbc:mysql://**************:23412/ibms_security?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8\r\n    username: ibms_security\r\n    password: Hcic2H8zKeXR5aa3\r\n    hikari:\r\n      connection-timeout: 5000 #连接超时时间，默认30s\r\n      maximum-pool-size: 20 #连接池最大数量，默认10\r\n      minimum-idle: 5 #连接池最小空闲连接数，默认10\r\n      idleTimeout: 60000 #连接的最大空闲时间，60s\r\n      maxLifetime: 1800000 #连接的最大生命周期，30min\r\n      # 开启JMX支持服务，方便状态监控\r\n      registerMbeans: true\r\n      jmxName: \'com.zaxxer.hikari:type=DataSource\'\r\n  fastjson:\r\n    parser:\r\n      autoTypeAccept: com.ruoyi.system.api.domain.SysDictData  # 设置允许反序列化的包路径\r\n      autoTypeSupport: true  # 开启 autoType 功能\r\n      safeMode: true  # 开启 safeMode 模式\r\n    \r\n# mybatis-plus配置\r\nmybatis-plus:\r\n  typeAliasesPackage: com.ibms.service.**.domain\r\n  mapper-locations: classpath*:/mapper/**/*Mapper.xml,classpath:/META-INF/modeler-mybatis-mappings/*.xml\r\n  #mybatis-plus配置控制台打印完整带参数SQL语句\r\n  configuration: \r\n    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl\r\n    #HikariCP连接池实现JDBC数据源\r\n    dataSourceType: com.zaxxer.hikari.HikariDataSource\r\n  configuration-properties:\r\n    blobType: BLOB\r\n    boolValue: true\r\n    prefix: \'\'\r\n  global-config:\r\n    enable-sql-runner: true\r\n\r\nflowable:\r\n  # 关闭定时任务Job\r\n  async-executor-activate: false\r\n  check-process-definitions: false\r\n  process-definition-location-prefix: classpath:/processes/\r\n  #是否检测表生成\r\n  database-schema-update: true\r\n  # properties:\r\n  #   blobType: BLOB\r\n  #   boolValue: true\r\n  #   prefix: \'\'\r\n  common:\r\n    app:\r\n      idm-url: http://localhost:9999\r\n      idm-admin:\r\n        user: admin\r\n        password: test\r\n\r\n# swagger配置\r\nswagger:\r\n  title: IBMS-业务安防模块\r\n  license: Powered By 大博金-研发部\r\n  licenseUrl: https://www.dabojinkj.com\r\n\r\n# Qanything 知识库id\r\nQanything:\r\n  knowledge:\r\n    kbid: KB78ffd982d5b54d089d606a12fecf7e2f\r\n\r\n# 文件地址，跟file模块同步\r\nfile:\r\n    domain: http://*************:19300\r\n    path: D:/ruoyi/uploadPath\r\n    prefix: /statics', 'bace3a0b1844e277e627ab429dbc573e', '2025-02-24 17:12:17', '2025-02-24 09:12:15', 'nacos', '*************', 'U', 'local');
INSERT INTO `his_config_info` VALUES (0, 369, 'ruoyi-security-test.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  redis:\n    host: 127.0.0.1\n    port: 6379\n    password: \n    timeout: 5000\n  datasource:\n    type: com.zaxxer.hikari.HikariDataSource\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: jdbc:mysql://**************:23403/ry-security?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8\n    username: ry-security\n    password: rLdbtEjDfyGpDWDx\n    hikari:\n      connection-timeout: 5000 #连接超时时间，默认30s\n      maximum-pool-size: 20 #连接池最大数量，默认10\n      minimum-idle: 5 #连接池最小空闲连接数，默认10\n      idleTimeout: 60000 #连接的最大空闲时间，60s\n      maxLifetime: 1800000 #连接的最大生命周期，30min\n      # 开启JMX支持服务，方便状态监控\n      registerMbeans: true\n      jmxName: \'com.zaxxer.hikari:type=DataSource\'\n  fastjson:\n    parser:\n      autoTypeAccept: com.ruoyi.system.api.domain.SysDictData  # 设置允许反序列化的包路径\n      autoTypeSupport: true  # 开启 autoType 功能\n      safeMode: true  # 开启 safeMode 模式\n    \n# mybatis-plus配置\nmybatis-plus:\n  typeAliasesPackage: com.ibms.service.**.domain\n  mapper-locations: classpath*:/mapper/**/*Mapper.xml,classpath:/META-INF/modeler-mybatis-mappings/*.xml\n  #mybatis-plus配置控制台打印完整带参数SQL语句\n  configuration: \n    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl\n    #HikariCP连接池实现JDBC数据源\n    dataSourceType: com.zaxxer.hikari.HikariDataSource\n  configuration-properties:\n    blobType: BLOB\n    boolValue: true\n    prefix: \'\'\n  global-config:\n    enable-sql-runner: true\n\nflowable:\n  # 关闭定时任务Job\n  async-executor-activate: false\n  check-process-definitions: false\n  process-definition-location-prefix: classpath:/processes/\n  #是否检测表生成\n  database-schema-update: true\n  # properties:\n  #   blobType: BLOB\n  #   boolValue: true\n  #   prefix: \'\'\n  common:\n    app:\n      idm-url: http://localhost:9999\n      idm-admin:\n        user: admin\n        password: test\n\n# swagger配置\nswagger:\n  title: 安防模块\n  license: Powered By 大博金-研发部\n  licenseUrl: https://www.dabojinkj.com\n\n# Qanything 知识库id\nQanything:\n  knowledge:\n    kbid: KB78ffd982d5b54d089d606a12fecf7e2f\n\n# 文件地址，跟file模块同步\nfile:\n    domain: http://*************:8080/file\n    path: D:/ruoyi/uploadPath\n    prefix: /statics', '7c57742f19f5ceb3d5cb21469e491d88', '2025-03-10 14:44:02', '2025-03-10 06:44:02', NULL, '*************', 'I', 'test');
INSERT INTO `his_config_info` VALUES (232, 370, 'ruoyi-security-test.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring:\n  redis:\n    host: 127.0.0.1\n    port: 6379\n    password: \n    timeout: 5000\n  datasource:\n    type: com.zaxxer.hikari.HikariDataSource\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: jdbc:mysql://**************:23403/ry-security?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8\n    username: ry-security\n    password: rLdbtEjDfyGpDWDx\n    hikari:\n      connection-timeout: 5000 #连接超时时间，默认30s\n      maximum-pool-size: 20 #连接池最大数量，默认10\n      minimum-idle: 5 #连接池最小空闲连接数，默认10\n      idleTimeout: 60000 #连接的最大空闲时间，60s\n      maxLifetime: 1800000 #连接的最大生命周期，30min\n      # 开启JMX支持服务，方便状态监控\n      registerMbeans: true\n      jmxName: \'com.zaxxer.hikari:type=DataSource\'\n  fastjson:\n    parser:\n      autoTypeAccept: com.ruoyi.system.api.domain.SysDictData  # 设置允许反序列化的包路径\n      autoTypeSupport: true  # 开启 autoType 功能\n      safeMode: true  # 开启 safeMode 模式\n    \n# mybatis-plus配置\nmybatis-plus:\n  typeAliasesPackage: com.ibms.service.**.domain\n  mapper-locations: classpath*:/mapper/**/*Mapper.xml,classpath:/META-INF/modeler-mybatis-mappings/*.xml\n  #mybatis-plus配置控制台打印完整带参数SQL语句\n  configuration: \n    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl\n    #HikariCP连接池实现JDBC数据源\n    dataSourceType: com.zaxxer.hikari.HikariDataSource\n  configuration-properties:\n    blobType: BLOB\n    boolValue: true\n    prefix: \'\'\n  global-config:\n    enable-sql-runner: true\n\nflowable:\n  # 关闭定时任务Job\n  async-executor-activate: false\n  check-process-definitions: false\n  process-definition-location-prefix: classpath:/processes/\n  #是否检测表生成\n  database-schema-update: true\n  # properties:\n  #   blobType: BLOB\n  #   boolValue: true\n  #   prefix: \'\'\n  common:\n    app:\n      idm-url: http://localhost:9999\n      idm-admin:\n        user: admin\n        password: test\n\n# swagger配置\nswagger:\n  title: 安防模块\n  license: Powered By 大博金-研发部\n  licenseUrl: https://www.dabojinkj.com\n\n# Qanything 知识库id\nQanything:\n  knowledge:\n    kbid: KB78ffd982d5b54d089d606a12fecf7e2f\n\n# 文件地址，跟file模块同步\nfile:\n    domain: http://*************:8080/file\n    path: D:/ruoyi/uploadPath\n    prefix: /statics', '7c57742f19f5ceb3d5cb21469e491d88', '2025-03-10 14:46:43', '2025-03-10 06:46:44', 'nacos', '*************', 'U', 'test');
INSERT INTO `his_config_info` VALUES (182, 371, 'ruoyi-gateway-test.yml', 'DEFAULT_GROUP', '', 'spring:\n  redis:\n    host: 127.0.0.1\n    port: 6379\n    password: \n  cloud:\n    gateway:\n      discovery:\n        locator:\n          lowerCaseServiceId: true\n          enabled: true\n      routes:\n        # 认证中心\n        - id: ruoyi-auth\n          uri: lb://ruoyi-auth\n          predicates:\n            - Path=/auth/**\n          filters:\n            # 验证码处理\n            - CacheRequestFilter\n            - ValidateCodeFilter\n            - StripPrefix=1\n        # 代码生成\n        - id: ruoyi-gen\n          uri: lb://ruoyi-gen\n          predicates:\n            - Path=/code/**\n          filters:\n            - StripPrefix=1\n        # 定时任务\n        - id: ruoyi-job\n          uri: lb://ruoyi-job\n          predicates:\n            - Path=/schedule/**\n          filters:\n            - StripPrefix=1\n        # 系统模块\n        - id: ruoyi-system\n          uri: lb://ruoyi-system\n          predicates:\n            - Path=/system/**\n          filters:\n            - StripPrefix=1\n        # 文件服务\n        - id: ruoyi-file\n          uri: lb://ruoyi-file\n          predicates:\n            - Path=/file/**\n          filters:\n            - StripPrefix=1\n        # 客户服务\n        - id: ruoyi-custom\n          uri: lb://ruoyi-custom\n          predicates:\n            - Path=/custom/**\n          filters:\n            - StripPrefix=1\n        # 居家服务\n        - id: ruoyi-homecare\n          uri: lb://ruoyi-homecare\n          predicates:\n            - Path=/homecare/**\n          filters:\n            - StripPrefix=1\n        # 日间照料服务\n        - id: ruoyi-daycare\n          uri: lb://ruoyi-daycare\n          predicates:\n            - Path=/daycare/**\n          filters:\n            - StripPrefix=1\n         # IBMS-资产\n        - id: ruoyi-assets\n          uri: lb://ruoyi-assets\n          predicates:\n            - Path=/assets/**\n          filters:\n            - StripPrefix=1  \n         # IBMS-物业\n        - id: ruoyi-realty\n          uri: lb://ruoyi-realty\n          predicates:\n            - Path=/realty/**\n          filters:\n            - StripPrefix=1  \n\n# 安全配置\nsecurity:\n  # 验证码\n  captcha:\n    configs:\n      # 应用及设置，默认关闭\n      default:\n        enabled: false\n      custom_pc_admin:\n        enabled: true\n        type: math\n      custom_applet_lryh:\n        enabled: false\n        type: math\n      custom_app_hg:\n        enabled: true\n        type: math\n\n  # 防止XSS攻击\n  xss:\n    enabled: true\n    excludeUrls:\n      - /system/notice\n  # 不校验白名单\n  ignore:\n    whites:\n      - /auth/logout\n      - /auth/login\n      - /auth/register\n      - /auth/registerByRegisteredVisitor\n      - /auth/getOpenIdByCode\n      - /auth/getTokenByOpenId\n      - /auth/appLogin\n      - /auth/businessLogin\n      - /auth/statics/**\n      - /*/v2/api-docs\n      - /csrf\n      - /custom/careWorkerInfo/careLogin\n      - /system/dict/data/type/**\n      - /homecare/homeCommunityBaseInfo/getCommunityList\n      - /file/**\n      - /doc.html\n      - /homecare/HomeRegisterServiceProvider/registerServiceProvider\n      - /homecare/homeServiceProviderManagement/getServiceProviderLabel\n      - /homecare/appApi/getOpenIdByCode\n      - /homecare/appApi/getTokenByOpenId\n      - /homecare/homeServiceProviderManagement/getIgnoreServiceProviderLabel\n      - /homecare/wechatPay/notify/**\n      - /homecare/appApi/xxx/onWxMsg\n      - /system/dict/data/type/**\n      - /homecare/homeCommunityBaseInfo/getCommunityList\n      - /homecare/homeTimeCoinSettings/getSettingsList\n      - /homecare/homeStreetInfo/list\n      - /homecare/homeAppServicePlaceOrderVolunteerInfo/register\n      - /homecare/homeStreetInfo/ignoreWhiteTreeselect\n      - /homecare/appApi/reserve/login\n      - /homecare/appApi/reserve/getOpenIdByCode\n      - /homecare/appApi/reserve/getTokenByOpenId\n      - /homecare/statics/**\n      - /*/app/rest/models/*/bpmn20 \n      - /assets/fixedAssetsArchivesBase/getById \n      - /security/visual/getData\n      - /realty/visitor/addByScan\n      - /assets/fixedAssetsArchivesBase/getById\n      - /security-js/visual/getData\n      - /energy/**', 'feb63fc14d40abb494ef5d69ef4c4a86', '2025-03-10 18:05:09', '2025-03-10 10:05:10', 'nacos', '*************', 'U', 'test');
INSERT INTO `his_config_info` VALUES (182, 372, 'ruoyi-gateway-test.yml', 'DEFAULT_GROUP', '', 'spring:\n  redis:\n    host: 127.0.0.1\n    port: 6379\n    password: \n  cloud:\n    gateway:\n      discovery:\n        locator:\n          lowerCaseServiceId: true\n          enabled: true\n      routes:\n        # 认证中心\n        - id: ruoyi-auth\n          uri: lb://ruoyi-auth\n          predicates:\n            - Path=/auth/**\n          filters:\n            # 验证码处理\n            - CacheRequestFilter\n            - ValidateCodeFilter\n            - StripPrefix=1\n        # 代码生成\n        - id: ruoyi-gen\n          uri: lb://ruoyi-gen\n          predicates:\n            - Path=/code/**\n          filters:\n            - StripPrefix=1\n        # 定时任务\n        - id: ruoyi-job\n          uri: lb://ruoyi-job\n          predicates:\n            - Path=/schedule/**\n          filters:\n            - StripPrefix=1\n        # 系统模块\n        - id: ruoyi-system\n          uri: lb://ruoyi-system\n          predicates:\n            - Path=/system/**\n          filters:\n            - StripPrefix=1\n        # 文件服务\n        - id: ruoyi-file\n          uri: lb://ruoyi-file\n          predicates:\n            - Path=/file/**\n          filters:\n            - StripPrefix=1\n        # 客户服务\n        - id: ruoyi-custom\n          uri: lb://ruoyi-custom\n          predicates:\n            - Path=/custom/**\n          filters:\n            - StripPrefix=1\n        # 居家服务\n        - id: ruoyi-homecare\n          uri: lb://ruoyi-homecare\n          predicates:\n            - Path=/homecare/**\n          filters:\n            - StripPrefix=1\n        # 日间照料服务\n        - id: ruoyi-daycare\n          uri: lb://ruoyi-daycare\n          predicates:\n            - Path=/daycare/**\n          filters:\n            - StripPrefix=1\n         # IBMS-资产\n        - id: ruoyi-assets\n          uri: lb://ruoyi-assets\n          predicates:\n            - Path=/assets/**\n          filters:\n            - StripPrefix=1  \n         # IBMS-物业\n        - id: ruoyi-realty\n          uri: lb://ruoyi-realty\n          predicates:\n            - Path=/realty/**\n          filters:\n            - StripPrefix=1  \n         # IBMS-安防\n        - id: ruoyi-security\n          uri: lb://ruoyi-security\n          predicates:\n            - Path=/security/**\n          filters:\n            - StripPrefix=1  \n\n# 安全配置\nsecurity:\n  # 验证码\n  captcha:\n    configs:\n      # 应用及设置，默认关闭\n      default:\n        enabled: false\n      custom_pc_admin:\n        enabled: true\n        type: math\n      custom_applet_lryh:\n        enabled: false\n        type: math\n      custom_app_hg:\n        enabled: true\n        type: math\n\n  # 防止XSS攻击\n  xss:\n    enabled: true\n    excludeUrls:\n      - /system/notice\n  # 不校验白名单\n  ignore:\n    whites:\n      - /auth/logout\n      - /auth/login\n      - /auth/register\n      - /auth/registerByRegisteredVisitor\n      - /auth/getOpenIdByCode\n      - /auth/getTokenByOpenId\n      - /auth/appLogin\n      - /auth/businessLogin\n      - /auth/statics/**\n      - /*/v2/api-docs\n      - /csrf\n      - /custom/careWorkerInfo/careLogin\n      - /system/dict/data/type/**\n      - /homecare/homeCommunityBaseInfo/getCommunityList\n      - /file/**\n      - /doc.html\n      - /homecare/HomeRegisterServiceProvider/registerServiceProvider\n      - /homecare/homeServiceProviderManagement/getServiceProviderLabel\n      - /homecare/appApi/getOpenIdByCode\n      - /homecare/appApi/getTokenByOpenId\n      - /homecare/homeServiceProviderManagement/getIgnoreServiceProviderLabel\n      - /homecare/wechatPay/notify/**\n      - /homecare/appApi/xxx/onWxMsg\n      - /system/dict/data/type/**\n      - /homecare/homeCommunityBaseInfo/getCommunityList\n      - /homecare/homeTimeCoinSettings/getSettingsList\n      - /homecare/homeStreetInfo/list\n      - /homecare/homeAppServicePlaceOrderVolunteerInfo/register\n      - /homecare/homeStreetInfo/ignoreWhiteTreeselect\n      - /homecare/appApi/reserve/login\n      - /homecare/appApi/reserve/getOpenIdByCode\n      - /homecare/appApi/reserve/getTokenByOpenId\n      - /homecare/statics/**\n      - /*/app/rest/models/*/bpmn20 \n      - /assets/fixedAssetsArchivesBase/getById \n      - /security/visual/getData\n      - /realty/visitor/addByScan\n      - /assets/fixedAssetsArchivesBase/getById\n      - /security-js/visual/getData\n      - /energy/**', '59c3d7c8818fa692d7c7e716b0f790a8', '2025-03-11 16:15:20', '2025-03-11 08:15:20', 'nacos', '*************', 'U', 'test');
INSERT INTO `his_config_info` VALUES (145, 373, 'ruoyi-gateway-local.yml', 'DEFAULT_GROUP', '', 'spring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n  cloud:\n    gateway:\n      discovery:\n        locator:\n          lowerCaseServiceId: true\n          enabled: true\n      routes:\n        # 认证中心\n        - id: ruoyi-auth\n          uri: lb://ruoyi-auth\n          predicates:\n            - Path=/auth/**\n          filters:\n            # 验证码处理\n            - CacheRequestFilter\n            - ValidateCodeFilter\n            - StripPrefix=1\n        # 代码生成\n        - id: ruoyi-gen\n          uri: lb://ruoyi-gen\n          predicates:\n            - Path=/code/**\n          filters:\n            - StripPrefix=1\n        # 定时任务\n        - id: ruoyi-job\n          uri: lb://ruoyi-job\n          predicates:\n            - Path=/schedule/**\n          filters:\n            - StripPrefix=1\n        # 系统模块\n        - id: ruoyi-system\n          uri: lb://ruoyi-system\n          predicates:\n            - Path=/system/**\n          filters:\n            - StripPrefix=1\n        # 文件服务\n        - id: ruoyi-file\n          uri: lb://ruoyi-file\n          predicates:\n            - Path=/file/**\n          filters:\n            - StripPrefix=1\n        # 客户服务\n        - id: ruoyi-custom\n          uri: lb://ruoyi-custom\n          predicates:\n            - Path=/custom/**\n          filters:\n            - StripPrefix=1\n        # 居家服务\n        - id: ruoyi-homecare\n          uri: lb://ruoyi-homecare\n          predicates:\n            - Path=/homecare/**\n          filters:\n            - StripPrefix=1\n        # 日间照料服务\n        - id: ruoyi-daycare\n          uri: lb://ruoyi-daycare\n          predicates:\n            - Path=/daycare/**\n          filters:\n            - StripPrefix=1\n         # IBMS-资产\n        - id: ruoyi-assets\n          uri: lb://ruoyi-assets\n          predicates:\n            - Path=/assets/**\n          filters:\n            - StripPrefix=1  \n         # IBMS-物业\n        - id: ruoyi-realty\n          uri: lb://ruoyi-realty\n          predicates:\n            - Path=/realty/**\n          filters:\n            - StripPrefix=1  \n\n# 安全配置\nsecurity:\n  # 验证码\n  captcha:\n    configs:\n      # 应用及设置，默认关闭\n      default:\n        enabled: false\n      custom_pc_admin:\n        enabled: true\n        type: math\n      custom_applet_lryh:\n        enabled: false\n        type: math\n      custom_app_hg:\n        enabled: true\n        type: math      \n\n  # 防止XSS攻击\n  xss:\n    enabled: true\n    excludeUrls:\n      - /system/notice\n  # 不校验白名单\n  ignore:\n    whites:\n      - /auth/logout\n      - /auth/login\n      - /auth/register\n      - /auth/registerByRegisteredVisitor\n      - /auth/getOpenIdByCode\n      - /auth/getTokenByOpenId\n      - /auth/appLogin\n      - /auth/businessLogin\n      - /auth/statics/**\n      - /*/v2/api-docs\n      - /csrf\n      - /custom/careWorkerInfo/careLogin\n      - /system/dict/data/type/**\n      - /homecare/homeCommunityBaseInfo/getCommunityList\n      - /file/**\n      - /doc.html\n      - /homecare/HomeRegisterServiceProvider/registerServiceProvider\n      - /homecare/homeServiceProviderManagement/getServiceProviderLabel\n      - /homecare/appApi/getOpenIdByCode\n      - /homecare/appApi/getTokenByOpenId\n      - /homecare/homeServiceProviderManagement/getIgnoreServiceProviderLabel\n      - /homecare/wechatPay/notify/**\n      - /homecare/appApi/xxx/onWxMsg\n      - /system/dict/data/type/**\n      - /homecare/homeCommunityBaseInfo/getCommunityList\n      - /homecare/homeTimeCoinSettings/getSettingsList\n      - /homecare/homeStreetInfo/list\n      - /homecare/homeAppServicePlaceOrderVolunteerInfo/register\n      - /homecare/homeStreetInfo/ignoreWhiteTreeselect\n      - /homecare/appApi/reserve/login\n      - /homecare/appApi/reserve/getOpenIdByCode\n      - /homecare/appApi/reserve/getTokenByOpenId\n      - /homecare/statics/**\n      - /*/app/rest/models/*/bpmn20 \n      - /assets/fixedAssetsArchivesBase/getById \n      - /security/visual/getData\n      - /realty/visitor/addByScan\n      - /assets/fixedAssetsArchivesBase/getById\n      - /security-js/visual/getData\n      - /energy/**', 'f4af9cf6c3f55b9ff5637df643418c6c', '2025-03-11 16:21:31', '2025-03-11 08:21:31', 'nacos', '*************', 'U', 'local');
INSERT INTO `his_config_info` VALUES (145, 374, 'ruoyi-gateway-local.yml', 'DEFAULT_GROUP', '', 'spring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n  cloud:\n    gateway:\n      discovery:\n        locator:\n          lowerCaseServiceId: true\n          enabled: true\n      routes:\n        # 认证中心\n        - id: ruoyi-auth\n          uri: lb://ruoyi-auth\n          predicates:\n            - Path=/auth/**\n          filters:\n            # 验证码处理\n            - CacheRequestFilter\n            - ValidateCodeFilter\n            - StripPrefix=1\n        # 代码生成\n        - id: ruoyi-gen\n          uri: lb://ruoyi-gen\n          predicates:\n            - Path=/code/**\n          filters:\n            - StripPrefix=1\n        # 定时任务\n        - id: ruoyi-job\n          uri: lb://ruoyi-job\n          predicates:\n            - Path=/schedule/**\n          filters:\n            - StripPrefix=1\n        # 系统模块\n        - id: ruoyi-system\n          uri: lb://ruoyi-system\n          predicates:\n            - Path=/system/**\n          filters:\n            - StripPrefix=1\n        # 文件服务\n        - id: ruoyi-file\n          uri: lb://ruoyi-file\n          predicates:\n            - Path=/file/**\n          filters:\n            - StripPrefix=1\n        # 客户服务\n        - id: ruoyi-custom\n          uri: lb://ruoyi-custom\n          predicates:\n            - Path=/custom/**\n          filters:\n            - StripPrefix=1\n        # 居家服务\n        - id: ruoyi-homecare\n          uri: lb://ruoyi-homecare\n          predicates:\n            - Path=/homecare/**\n          filters:\n            - StripPrefix=1\n        # 日间照料服务\n        - id: ruoyi-daycare\n          uri: lb://ruoyi-daycare\n          predicates:\n            - Path=/daycare/**\n          filters:\n            - StripPrefix=1\n         # IBMS-资产\n        - id: ruoyi-assets\n          uri: lb://ruoyi-assets\n          predicates:\n            - Path=/assets/**\n          filters:\n            - StripPrefix=1  \n         # IBMS-物业\n        - id: ruoyi-realty\n          uri: lb://ruoyi-realty\n          predicates:\n            - Path=/realty/**\n          filters:\n            - StripPrefix=1  \n\n# 安全配置\nsecurity:\n  # 验证码\n  captcha:\n    configs:\n      # 应用及设置，默认关闭\n      default:\n        enabled: true\n      custom_pc_admin:\n        enabled: true\n        type: math\n      custom_applet_lryh:\n        enabled: false\n        type: math\n      custom_app_hg:\n        enabled: true\n        type: math      \n\n  # 防止XSS攻击\n  xss:\n    enabled: true\n    excludeUrls:\n      - /system/notice\n  # 不校验白名单\n  ignore:\n    whites:\n      - /auth/logout\n      - /auth/login\n      - /auth/register\n      - /auth/registerByRegisteredVisitor\n      - /auth/getOpenIdByCode\n      - /auth/getTokenByOpenId\n      - /auth/appLogin\n      - /auth/businessLogin\n      - /auth/statics/**\n      - /*/v2/api-docs\n      - /csrf\n      - /custom/careWorkerInfo/careLogin\n      - /system/dict/data/type/**\n      - /homecare/homeCommunityBaseInfo/getCommunityList\n      - /file/**\n      - /doc.html\n      - /homecare/HomeRegisterServiceProvider/registerServiceProvider\n      - /homecare/homeServiceProviderManagement/getServiceProviderLabel\n      - /homecare/appApi/getOpenIdByCode\n      - /homecare/appApi/getTokenByOpenId\n      - /homecare/homeServiceProviderManagement/getIgnoreServiceProviderLabel\n      - /homecare/wechatPay/notify/**\n      - /homecare/appApi/xxx/onWxMsg\n      - /system/dict/data/type/**\n      - /homecare/homeCommunityBaseInfo/getCommunityList\n      - /homecare/homeTimeCoinSettings/getSettingsList\n      - /homecare/homeStreetInfo/list\n      - /homecare/homeAppServicePlaceOrderVolunteerInfo/register\n      - /homecare/homeStreetInfo/ignoreWhiteTreeselect\n      - /homecare/appApi/reserve/login\n      - /homecare/appApi/reserve/getOpenIdByCode\n      - /homecare/appApi/reserve/getTokenByOpenId\n      - /homecare/statics/**\n      - /*/app/rest/models/*/bpmn20 \n      - /assets/fixedAssetsArchivesBase/getById \n      - /security/visual/getData\n      - /realty/visitor/addByScan\n      - /assets/fixedAssetsArchivesBase/getById\n      - /security-js/visual/getData\n      - /energy/**', '598175f8a8ee07821629d11c75bd77a6', '2025-03-11 16:24:54', '2025-03-11 08:24:54', 'nacos', '*************', 'U', 'local');
INSERT INTO `his_config_info` VALUES (182, 375, 'ruoyi-gateway-test.yml', 'DEFAULT_GROUP', '', 'spring:\n  redis:\n    host: 127.0.0.1\n    port: 6379\n    password: \n  cloud:\n    gateway:\n      discovery:\n        locator:\n          lowerCaseServiceId: true\n          enabled: true\n      routes:\n        # 认证中心\n        - id: ruoyi-auth\n          uri: lb://ruoyi-auth\n          predicates:\n            - Path=/auth/**\n          filters:\n            # 验证码处理\n            - CacheRequestFilter\n            - ValidateCodeFilter\n            - StripPrefix=1\n        # 代码生成\n        - id: ruoyi-gen\n          uri: lb://ruoyi-gen\n          predicates:\n            - Path=/code/**\n          filters:\n            - StripPrefix=1\n        # 定时任务\n        - id: ruoyi-job\n          uri: lb://ruoyi-job\n          predicates:\n            - Path=/schedule/**\n          filters:\n            - StripPrefix=1\n        # 系统模块\n        - id: ruoyi-system\n          uri: lb://ruoyi-system\n          predicates:\n            - Path=/system/**\n          filters:\n            - StripPrefix=1\n        # 文件服务\n        - id: ruoyi-file\n          uri: lb://ruoyi-file\n          predicates:\n            - Path=/file/**\n          filters:\n            - StripPrefix=1\n        # 客户服务\n        - id: ruoyi-custom\n          uri: lb://ruoyi-custom\n          predicates:\n            - Path=/custom/**\n          filters:\n            - StripPrefix=1\n        # 居家服务\n        - id: ruoyi-homecare\n          uri: lb://ruoyi-homecare\n          predicates:\n            - Path=/homecare/**\n          filters:\n            - StripPrefix=1\n        # 日间照料服务\n        - id: ruoyi-daycare\n          uri: lb://ruoyi-daycare\n          predicates:\n            - Path=/daycare/**\n          filters:\n            - StripPrefix=1\n         # IBMS-资产\n        - id: ruoyi-assets\n          uri: lb://ruoyi-assets\n          predicates:\n            - Path=/assets/**\n          filters:\n            - StripPrefix=1  \n         # IBMS-物业\n        - id: ruoyi-realty\n          uri: lb://ruoyi-realty\n          predicates:\n            - Path=/realty/**\n          filters:\n            - StripPrefix=1  \n         # IBMS-安防\n        - id: ruoyi-security\n          uri: lb://ruoyi-security\n          predicates:\n            - Path=/security/**\n          filters:\n            - StripPrefix=1  \n\n# 安全配置\nsecurity:\n  # 验证码\n  captcha:\n    configs:\n      # 应用及设置，默认关闭\n      default:\n        enabled: true\n      custom_pc_admin:\n        enabled: true\n        type: math\n      custom_applet_lryh:\n        enabled: false\n        type: math\n      custom_app_hg:\n        enabled: true\n        type: math\n\n  # 防止XSS攻击\n  xss:\n    enabled: true\n    excludeUrls:\n      - /system/notice\n  # 不校验白名单\n  ignore:\n    whites:\n      - /auth/logout\n      - /auth/login\n      - /auth/register\n      - /auth/registerByRegisteredVisitor\n      - /auth/getOpenIdByCode\n      - /auth/getTokenByOpenId\n      - /auth/appLogin\n      - /auth/businessLogin\n      - /auth/statics/**\n      - /*/v2/api-docs\n      - /csrf\n      - /custom/careWorkerInfo/careLogin\n      - /system/dict/data/type/**\n      - /homecare/homeCommunityBaseInfo/getCommunityList\n      - /file/**\n      - /doc.html\n      - /homecare/HomeRegisterServiceProvider/registerServiceProvider\n      - /homecare/homeServiceProviderManagement/getServiceProviderLabel\n      - /homecare/appApi/getOpenIdByCode\n      - /homecare/appApi/getTokenByOpenId\n      - /homecare/homeServiceProviderManagement/getIgnoreServiceProviderLabel\n      - /homecare/wechatPay/notify/**\n      - /homecare/appApi/xxx/onWxMsg\n      - /system/dict/data/type/**\n      - /homecare/homeCommunityBaseInfo/getCommunityList\n      - /homecare/homeTimeCoinSettings/getSettingsList\n      - /homecare/homeStreetInfo/list\n      - /homecare/homeAppServicePlaceOrderVolunteerInfo/register\n      - /homecare/homeStreetInfo/ignoreWhiteTreeselect\n      - /homecare/appApi/reserve/login\n      - /homecare/appApi/reserve/getOpenIdByCode\n      - /homecare/appApi/reserve/getTokenByOpenId\n      - /homecare/statics/**\n      - /*/app/rest/models/*/bpmn20 \n      - /assets/fixedAssetsArchivesBase/getById \n      - /security/visual/getData\n      - /realty/visitor/addByScan\n      - /assets/fixedAssetsArchivesBase/getById\n      - /security-js/visual/getData\n      - /energy/**', '202002fd7af45a7cba1e2b642dba40dc', '2025-03-11 16:25:09', '2025-03-11 08:25:10', 'nacos', '*************', 'U', 'test');
INSERT INTO `his_config_info` VALUES (151, 376, 'ruoyi-custom-local.yml', 'DEFAULT_GROUP', '', '# spring配置\nspring: \n  servlet:\n    multipart:\n      maxFileSize:  1000MB\n      maxRequestSize: 1000MB\n  main:\n    allow-circular-references: true\n    allow-bean-definition-overriding: true\n  redis:\n    host: localhost\n    port: 6379\n    password: \n  datasource:\n    druid:\n      stat-view-servlet:\n        enabled: true\n        loginUsername: admin\n        loginPassword: 123456\n    dynamic:\n      druid:\n        initial-size: 5\n        min-idle: 5\n        maxActive: 20\n        maxWait: 60000\n        timeBetweenEvictionRunsMillis: 60000\n        minEvictableIdleTimeMillis: 300000\n        validationQuery: SELECT 1 FROM DUAL\n        testWhileIdle: true\n        testOnBorrow: false\n        testOnReturn: false\n        poolPreparedStatements: true\n        maxPoolPreparedStatementPerConnectionSize: 20\n        filters: stat,slf4j\n        connectionProperties: druid.stat.mergeSql\\=true;druid.stat.slowSqlMillis\\=5000\n      datasource:\n          # 主库数据源\n          master:\n            driver-class-name: com.mysql.cj.jdbc.Driver\n            url: jdbc:mysql://**************:23403/ry-custom?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8&allowMultiQueries=true\n            username: ry-custom\n            password: m782cLf8Jst55rDK\n          # 从库数据源\n          # slave:\n            # username: \n            # password: \n            # url: \n            # driver-class-name: \n      # seata: true    # 开启seata代理，开启后默认每个数据源都代理，如果某个不需要代理可单独关闭\n\n# seata配置\nseata:\n  # 默认关闭，如需启用spring.datasource.dynami.seata需要同时开启\n  enabled: false\n  # Seata 应用编号，默认为 ${spring.application.name}\n  application-id: ${spring.application.name}\n  # Seata 事务组编号，用于 TC 集群名\n  tx-service-group: ${spring.application.name}-group\n  # 关闭自动代理\n  enable-auto-data-source-proxy: false\n  # 服务配置项\n  service:\n    # 虚拟组和分组的映射\n    vgroup-mapping:\n      ruoyi-system-group: default\n  config:\n    type: nacos\n    nacos:\n      serverAddr: 127.0.0.1:8848\n      group: SEATA_GROUP\n      namespace:\n  registry:\n    type: nacos\n    nacos:\n      application: seata-server\n      server-addr: 127.0.0.1:8848\n      namespace:\n\n# mybatis配置\nmybatis-plus:\n    # 搜索指定包别名\n    typeAliasesPackage: com.ruoyi.custom.admin.**\n    # 配置mapper的扫描，找到所有的mapper.xml映射文件\n    mapperLocations: classpath:mapper/**/**/*.xml\n    configuration:\n       map-underscore-to-camel-case: true\n# swagger配置\nswagger:\n  title: 客户模块接口文档\n  license: Powered By ruoyi\n  licenseUrl: https://ruoyi.vip\n\n# 微信小程序相关\nwx:\n  #\n  #v2 \n  appsecret: 0e5db3fd3cc6197dd68a785282dc1623\n  # appsecret: 78f1f354f767746171bcf957fb3fd3ec\n  pay:\n    appId: wxb7125f4f902fb0fe\n    # appid: wxa89aa4c24fca8ad4\n    mchId: **********\n    mchKey: dadba017qa3c7q4a8aq96bcq32df7fc5\n    apiV3Key: 2cc9f595e4d2616s865d1aa7wa13fbf4\n    keyPath: classpath:wxcertificate/apiclient_cert.p12\n    privateKeyPath: classpath:wxcertificate/apiclient_key.pem\n    privateCertPath: classpath:wxcertificate/apiclient_cert.pem', 'f4061a7dbfdf684e37c633fae9a92248', '2025-03-13 14:01:41', '2025-03-13 06:01:41', 'nacos', '*************', 'U', 'local');
INSERT INTO `his_config_info` VALUES (145, 377, 'ruoyi-gateway-local.yml', 'DEFAULT_GROUP', '', 'spring:\n  redis:\n    host: localhost\n    port: 6379\n    password: \n  cloud:\n    gateway:\n      discovery:\n        locator:\n          lowerCaseServiceId: true\n          enabled: true\n      routes:\n        # 认证中心\n        - id: ruoyi-auth\n          uri: lb://ruoyi-auth\n          predicates:\n            - Path=/auth/**\n          filters:\n            # 验证码处理\n            - CacheRequestFilter\n            - ValidateCodeFilter\n            - StripPrefix=1\n        # 代码生成\n        - id: ruoyi-gen\n          uri: lb://ruoyi-gen\n          predicates:\n            - Path=/code/**\n          filters:\n            - StripPrefix=1\n        # 定时任务\n        - id: ruoyi-job\n          uri: lb://ruoyi-job\n          predicates:\n            - Path=/schedule/**\n          filters:\n            - StripPrefix=1\n        # 系统模块\n        - id: ruoyi-system\n          uri: lb://ruoyi-system\n          predicates:\n            - Path=/system/**\n          filters:\n            - StripPrefix=1\n        # 文件服务\n        - id: ruoyi-file\n          uri: lb://ruoyi-file\n          predicates:\n            - Path=/file/**\n          filters:\n            - StripPrefix=1\n        # 客户服务\n        - id: ruoyi-custom\n          uri: lb://ruoyi-custom\n          predicates:\n            - Path=/custom/**\n          filters:\n            - StripPrefix=1\n        # 居家服务\n        - id: ruoyi-homecare\n          uri: lb://ruoyi-homecare\n          predicates:\n            - Path=/homecare/**\n          filters:\n            - StripPrefix=1\n        # 日间照料服务\n        - id: ruoyi-daycare\n          uri: lb://ruoyi-daycare\n          predicates:\n            - Path=/daycare/**\n          filters:\n            - StripPrefix=1\n         # IBMS-资产\n        - id: ruoyi-assets\n          uri: lb://ruoyi-assets\n          predicates:\n            - Path=/assets/**\n          filters:\n            - StripPrefix=1  \n         # IBMS-物业\n        - id: ruoyi-realty\n          uri: lb://ruoyi-realty\n          predicates:\n            - Path=/realty/**\n          filters:\n            - StripPrefix=1  \n\n# 安全配置\nsecurity:\n  # 验证码\n  captcha:\n    configs:\n      # 应用及设置，默认关闭\n      default:\n        enabled: true\n        type: math\n      custom_pc_admin:\n        enabled: true\n        type: math\n      custom_applet_lryh:\n        enabled: false\n        type: math\n      custom_app_hg:\n        enabled: true\n        type: math      \n\n  # 防止XSS攻击\n  xss:\n    enabled: true\n    excludeUrls:\n      - /system/notice\n  # 不校验白名单\n  ignore:\n    whites:\n      - /auth/logout\n      - /auth/login\n      - /auth/register\n      - /auth/registerByRegisteredVisitor\n      - /auth/getOpenIdByCode\n      - /auth/getTokenByOpenId\n      - /auth/appLogin\n      - /auth/businessLogin\n      - /auth/statics/**\n      - /*/v2/api-docs\n      - /csrf\n      - /custom/careWorkerInfo/careLogin\n      - /system/dict/data/type/**\n      - /homecare/homeCommunityBaseInfo/getCommunityList\n      - /file/**\n      - /doc.html\n      - /homecare/HomeRegisterServiceProvider/registerServiceProvider\n      - /homecare/homeServiceProviderManagement/getServiceProviderLabel\n      - /homecare/appApi/getOpenIdByCode\n      - /homecare/appApi/getTokenByOpenId\n      - /homecare/homeServiceProviderManagement/getIgnoreServiceProviderLabel\n      - /homecare/wechatPay/notify/**\n      - /homecare/appApi/xxx/onWxMsg\n      - /system/dict/data/type/**\n      - /homecare/homeCommunityBaseInfo/getCommunityList\n      - /homecare/homeTimeCoinSettings/getSettingsList\n      - /homecare/homeStreetInfo/list\n      - /homecare/homeAppServicePlaceOrderVolunteerInfo/register\n      - /homecare/homeStreetInfo/ignoreWhiteTreeselect\n      - /homecare/appApi/reserve/login\n      - /homecare/appApi/reserve/getOpenIdByCode\n      - /homecare/appApi/reserve/getTokenByOpenId\n      - /homecare/statics/**\n      - /*/app/rest/models/*/bpmn20 \n      - /assets/fixedAssetsArchivesBase/getById \n      - /security/visual/getData\n      - /realty/visitor/addByScan\n      - /assets/fixedAssetsArchivesBase/getById\n      - /security-js/visual/getData\n      - /energy/**', 'c19a2852925eccee48f933f7d3d29b4d', '2025-03-15 10:25:51', '2025-03-15 02:25:52', 'nacos', '*************', 'U', 'local');

-- ----------------------------
-- Table structure for permissions
-- ----------------------------
DROP TABLE IF EXISTS `permissions`;
CREATE TABLE `permissions`  (
  `role` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `resource` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `action` varchar(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  UNIQUE INDEX `uk_role_permission`(`role` ASC, `resource` ASC, `action` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of permissions
-- ----------------------------

-- ----------------------------
-- Table structure for roles
-- ----------------------------
DROP TABLE IF EXISTS `roles`;
CREATE TABLE `roles`  (
  `username` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `role` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  UNIQUE INDEX `idx_user_role`(`username` ASC, `role` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of roles
-- ----------------------------
INSERT INTO `roles` VALUES ('nacos', 'ROLE_ADMIN');

-- ----------------------------
-- Table structure for t_base_holidays
-- ----------------------------
DROP TABLE IF EXISTS `t_base_holidays`;
CREATE TABLE `t_base_holidays`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `holidays` date NOT NULL,
  `status` tinyint NULL DEFAULT 1,
  `remarks` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 357 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '法定节假日表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_base_holidays
-- ----------------------------
INSERT INTO `t_base_holidays` VALUES (2, '2021-01-02', 1, '周六  ');
INSERT INTO `t_base_holidays` VALUES (3, '2021-01-03', 1, '周天  ');
INSERT INTO `t_base_holidays` VALUES (4, '2021-01-09', 1, '周六  ');
INSERT INTO `t_base_holidays` VALUES (5, '2021-01-10', 1, '周天  ');
INSERT INTO `t_base_holidays` VALUES (6, '2021-01-16', 1, '周六  ');
INSERT INTO `t_base_holidays` VALUES (7, '2021-01-17', 1, '周天  ');
INSERT INTO `t_base_holidays` VALUES (8, '2021-01-23', 1, '周六  ');
INSERT INTO `t_base_holidays` VALUES (9, '2021-01-24', 1, '周天  ');
INSERT INTO `t_base_holidays` VALUES (10, '2021-01-30', 1, '周六  ');
INSERT INTO `t_base_holidays` VALUES (11, '2021-01-31', 1, '周天  ');
INSERT INTO `t_base_holidays` VALUES (12, '2021-02-06', 1, '周六  ');
INSERT INTO `t_base_holidays` VALUES (13, '2021-02-11', 1, '周四  除夕');
INSERT INTO `t_base_holidays` VALUES (14, '2021-02-12', 1, '周五  新年');
INSERT INTO `t_base_holidays` VALUES (15, '2021-02-13', 1, '周六  春节');
INSERT INTO `t_base_holidays` VALUES (16, '2021-02-14', 1, '周天  春节');
INSERT INTO `t_base_holidays` VALUES (17, '2021-02-15', 1, '周一  春节');
INSERT INTO `t_base_holidays` VALUES (18, '2021-02-16', 1, '周二  春节');
INSERT INTO `t_base_holidays` VALUES (19, '2021-02-17', 1, '周三  春节');
INSERT INTO `t_base_holidays` VALUES (20, '2021-02-21', 1, '周天  ');
INSERT INTO `t_base_holidays` VALUES (21, '2021-02-27', 1, '周六  ');
INSERT INTO `t_base_holidays` VALUES (22, '2021-02-28', 1, '周天  ');
INSERT INTO `t_base_holidays` VALUES (23, '2021-03-06', 1, '周六  ');
INSERT INTO `t_base_holidays` VALUES (24, '2021-03-07', 1, '周天  ');
INSERT INTO `t_base_holidays` VALUES (25, '2021-03-13', 1, '周六  ');
INSERT INTO `t_base_holidays` VALUES (26, '2021-03-14', 1, '周天  ');
INSERT INTO `t_base_holidays` VALUES (27, '2021-03-20', 1, '周六  ');
INSERT INTO `t_base_holidays` VALUES (28, '2021-03-21', 1, '周天  ');
INSERT INTO `t_base_holidays` VALUES (29, '2021-03-27', 1, '周六  ');
INSERT INTO `t_base_holidays` VALUES (30, '2021-03-28', 1, '周天  ');
INSERT INTO `t_base_holidays` VALUES (32, '2021-04-04', 1, '周天  清明节');
INSERT INTO `t_base_holidays` VALUES (33, '2021-04-05', 1, '周一  清明节');
INSERT INTO `t_base_holidays` VALUES (37, '2021-04-18', 1, '周天  ');
INSERT INTO `t_base_holidays` VALUES (38, '2021-04-24', 1, '周六  ');
INSERT INTO `t_base_holidays` VALUES (39, '2021-05-01', 1, '周六  劳动节');
INSERT INTO `t_base_holidays` VALUES (40, '2021-05-02', 1, '周天  劳动节');
INSERT INTO `t_base_holidays` VALUES (41, '2021-05-03', 1, '周一  劳动节');
INSERT INTO `t_base_holidays` VALUES (42, '2021-05-04', 1, '周二  劳动节');
INSERT INTO `t_base_holidays` VALUES (44, '2021-05-09', 1, '周天  ');
INSERT INTO `t_base_holidays` VALUES (46, '2021-05-16', 1, '周天  ');
INSERT INTO `t_base_holidays` VALUES (48, '2021-05-23', 1, '周天  ');
INSERT INTO `t_base_holidays` VALUES (50, '2021-05-30', 1, '周天  ');
INSERT INTO `t_base_holidays` VALUES (52, '2021-06-06', 1, '周天  ');
INSERT INTO `t_base_holidays` VALUES (53, '2021-06-12', 1, '周六  端午节');
INSERT INTO `t_base_holidays` VALUES (54, '2021-06-13', 1, '周天  端午节');
INSERT INTO `t_base_holidays` VALUES (55, '2021-06-14', 1, '周一  端午节');
INSERT INTO `t_base_holidays` VALUES (57, '2021-06-20', 1, '周天  ');
INSERT INTO `t_base_holidays` VALUES (59, '2021-06-27', 1, '周天  ');
INSERT INTO `t_base_holidays` VALUES (61, '2021-07-04', 1, '周天  ');
INSERT INTO `t_base_holidays` VALUES (63, '2021-07-11', 1, '周天  ');
INSERT INTO `t_base_holidays` VALUES (65, '2021-07-18', 1, '周天  ');
INSERT INTO `t_base_holidays` VALUES (67, '2021-07-25', 1, '周天  ');
INSERT INTO `t_base_holidays` VALUES (69, '2021-08-01', 1, '周天  ');
INSERT INTO `t_base_holidays` VALUES (71, '2021-08-08', 1, '周天  ');
INSERT INTO `t_base_holidays` VALUES (73, '2021-08-15', 1, '周天  ');
INSERT INTO `t_base_holidays` VALUES (75, '2021-08-22', 1, '周天  ');
INSERT INTO `t_base_holidays` VALUES (77, '2021-08-29', 1, '周天  ');
INSERT INTO `t_base_holidays` VALUES (79, '2021-09-05', 1, '周天  ');
INSERT INTO `t_base_holidays` VALUES (81, '2021-09-12', 1, '周天  ');
INSERT INTO `t_base_holidays` VALUES (82, '2021-09-19', 1, '周天  中秋节');
INSERT INTO `t_base_holidays` VALUES (83, '2021-09-20', 1, '周一  中秋节');
INSERT INTO `t_base_holidays` VALUES (84, '2021-09-21', 1, '周二  中秋节');
INSERT INTO `t_base_holidays` VALUES (86, '2021-10-01', 1, '周五  国庆节');
INSERT INTO `t_base_holidays` VALUES (87, '2021-10-02', 1, '周六  国庆节');
INSERT INTO `t_base_holidays` VALUES (88, '2021-10-03', 1, '周天  国庆节');
INSERT INTO `t_base_holidays` VALUES (89, '2021-10-04', 1, '周一  国庆节');
INSERT INTO `t_base_holidays` VALUES (90, '2021-10-05', 1, '周二  国庆节');
INSERT INTO `t_base_holidays` VALUES (91, '2021-10-06', 1, '周三  国庆节');
INSERT INTO `t_base_holidays` VALUES (92, '2021-10-07', 1, '周四  国庆节');
INSERT INTO `t_base_holidays` VALUES (93, '2021-10-10', 1, '周天  ');
INSERT INTO `t_base_holidays` VALUES (95, '2021-10-17', 1, '周天  ');
INSERT INTO `t_base_holidays` VALUES (97, '2021-10-24', 1, '周天  ');
INSERT INTO `t_base_holidays` VALUES (99, '2021-10-31', 1, '周天  ');
INSERT INTO `t_base_holidays` VALUES (101, '2021-11-07', 1, '周天  ');
INSERT INTO `t_base_holidays` VALUES (103, '2021-11-14', 1, '周天  ');
INSERT INTO `t_base_holidays` VALUES (105, '2021-11-21', 1, '周天  ');
INSERT INTO `t_base_holidays` VALUES (106, '2021-11-27', 1, '周六  ');
INSERT INTO `t_base_holidays` VALUES (107, '2021-11-28', 1, '周天  ');
INSERT INTO `t_base_holidays` VALUES (108, '2021-12-04', 1, '周六  ');
INSERT INTO `t_base_holidays` VALUES (109, '2021-12-05', 1, '周天  ');
INSERT INTO `t_base_holidays` VALUES (110, '2021-12-11', 1, '周六  ');
INSERT INTO `t_base_holidays` VALUES (111, '2021-12-12', 1, '周天  ');
INSERT INTO `t_base_holidays` VALUES (112, '2021-12-18', 1, '周六  ');
INSERT INTO `t_base_holidays` VALUES (113, '2021-12-19', 1, '周天  ');
INSERT INTO `t_base_holidays` VALUES (114, '2021-12-25', 1, '周六  ');
INSERT INTO `t_base_holidays` VALUES (115, '2021-12-26', 1, '周天  ');
INSERT INTO `t_base_holidays` VALUES (116, '2022-01-01', 1, '周六 元旦');
INSERT INTO `t_base_holidays` VALUES (117, '2022-01-02', 1, '周天 元旦');
INSERT INTO `t_base_holidays` VALUES (118, '2022-01-03', 1, '周一 元旦');
INSERT INTO `t_base_holidays` VALUES (119, '2022-01-08', 1, '周六');
INSERT INTO `t_base_holidays` VALUES (120, '2022-01-09', 1, '周天');
INSERT INTO `t_base_holidays` VALUES (121, '2022-01-15', 1, '周六');
INSERT INTO `t_base_holidays` VALUES (122, '2022-01-16', 1, '周天');
INSERT INTO `t_base_holidays` VALUES (123, '2022-01-22', 1, '周六');
INSERT INTO `t_base_holidays` VALUES (124, '2022-01-23', 1, '周天');
INSERT INTO `t_base_holidays` VALUES (125, '2022-01-31', 1, '周一 除夕');
INSERT INTO `t_base_holidays` VALUES (126, '2022-02-01', 1, '周二 春节');
INSERT INTO `t_base_holidays` VALUES (127, '2022-02-02', 1, '周三 春节');
INSERT INTO `t_base_holidays` VALUES (128, '2022-02-03', 1, '周四 春节');
INSERT INTO `t_base_holidays` VALUES (129, '2022-02-04', 1, '周五 春节');
INSERT INTO `t_base_holidays` VALUES (130, '2022-02-05', 1, '周六 春节');
INSERT INTO `t_base_holidays` VALUES (131, '2022-02-06', 1, '周天 春节');
INSERT INTO `t_base_holidays` VALUES (132, '2022-02-12', 1, '周六');
INSERT INTO `t_base_holidays` VALUES (133, '2022-02-13', 1, '周天');
INSERT INTO `t_base_holidays` VALUES (134, '2022-02-19', 1, '周六');
INSERT INTO `t_base_holidays` VALUES (135, '2022-02-20', 1, '周天');
INSERT INTO `t_base_holidays` VALUES (136, '2022-02-26', 1, '周六');
INSERT INTO `t_base_holidays` VALUES (137, '2022-02-27', 1, '周天');
INSERT INTO `t_base_holidays` VALUES (138, '2022-03-05', 1, '周六');
INSERT INTO `t_base_holidays` VALUES (139, '2022-03-06', 1, '周天');
INSERT INTO `t_base_holidays` VALUES (140, '2022-03-12', 1, '周六');
INSERT INTO `t_base_holidays` VALUES (141, '2022-03-13', 1, '周天');
INSERT INTO `t_base_holidays` VALUES (142, '2022-03-19', 1, '周六');
INSERT INTO `t_base_holidays` VALUES (143, '2022-03-20', 1, '周天');
INSERT INTO `t_base_holidays` VALUES (144, '2022-03-26', 1, '周六');
INSERT INTO `t_base_holidays` VALUES (145, '2022-03-27', 1, '周天');
INSERT INTO `t_base_holidays` VALUES (146, '2022-04-03', 0, '周天 清明节');
INSERT INTO `t_base_holidays` VALUES (147, '2022-04-04', 0, '周一 清明节');
INSERT INTO `t_base_holidays` VALUES (148, '2022-04-05', 0, '周二 清明节');
INSERT INTO `t_base_holidays` VALUES (149, '2022-04-09', 1, '周六');
INSERT INTO `t_base_holidays` VALUES (150, '2022-04-10', 1, '周天');
INSERT INTO `t_base_holidays` VALUES (151, '2022-04-16', 1, '周六');
INSERT INTO `t_base_holidays` VALUES (152, '2022-04-17', 1, '周天');
INSERT INTO `t_base_holidays` VALUES (153, '2022-04-23', 1, '');
INSERT INTO `t_base_holidays` VALUES (154, '2022-04-30', 1, '周六 劳动节');
INSERT INTO `t_base_holidays` VALUES (155, '2022-05-01', 1, '周天 劳动节');
INSERT INTO `t_base_holidays` VALUES (156, '2022-05-02', 1, '周一 劳动节');
INSERT INTO `t_base_holidays` VALUES (157, '2022-05-03', 1, '周二 劳动节');
INSERT INTO `t_base_holidays` VALUES (158, '2022-05-04', 1, '周三 劳动节');
INSERT INTO `t_base_holidays` VALUES (159, '2022-05-08', 1, '周天');
INSERT INTO `t_base_holidays` VALUES (160, '2022-05-14', 1, '周六');
INSERT INTO `t_base_holidays` VALUES (161, '2022-05-15', 1, '周天');
INSERT INTO `t_base_holidays` VALUES (162, '2022-05-21', 1, '周六');
INSERT INTO `t_base_holidays` VALUES (163, '2022-05-22', 1, '周天');
INSERT INTO `t_base_holidays` VALUES (164, '2022-05-28', 1, '周六');
INSERT INTO `t_base_holidays` VALUES (165, '2022-05-29', 1, '周天');
INSERT INTO `t_base_holidays` VALUES (166, '2022-06-03', 1, '周五 端午节');
INSERT INTO `t_base_holidays` VALUES (167, '2022-06-04', 1, '周六 端午节');
INSERT INTO `t_base_holidays` VALUES (168, '2022-06-05', 1, '周天 端午节');
INSERT INTO `t_base_holidays` VALUES (169, '2022-06-11', 1, '周六');
INSERT INTO `t_base_holidays` VALUES (170, '2022-06-12', 1, '周天');
INSERT INTO `t_base_holidays` VALUES (171, '2022-06-18', 1, '周六');
INSERT INTO `t_base_holidays` VALUES (172, '2022-06-19', 1, '周天');
INSERT INTO `t_base_holidays` VALUES (173, '2022-06-25', 1, '周六');
INSERT INTO `t_base_holidays` VALUES (174, '2022-06-26', 1, '周天');
INSERT INTO `t_base_holidays` VALUES (175, '2022-07-02', 1, '周六');
INSERT INTO `t_base_holidays` VALUES (176, '2022-07-03', 1, '周天');
INSERT INTO `t_base_holidays` VALUES (177, '2022-07-09', 1, '周六');
INSERT INTO `t_base_holidays` VALUES (178, '2022-07-10', 1, '周天');
INSERT INTO `t_base_holidays` VALUES (179, '2022-07-16', 1, '周六');
INSERT INTO `t_base_holidays` VALUES (180, '2022-07-17', 1, '周天');
INSERT INTO `t_base_holidays` VALUES (181, '2022-07-23', 1, '周六');
INSERT INTO `t_base_holidays` VALUES (182, '2022-07-24', 1, '周天');
INSERT INTO `t_base_holidays` VALUES (183, '2022-07-30', 1, '周六');
INSERT INTO `t_base_holidays` VALUES (184, '2022-07-31', 1, '周天');
INSERT INTO `t_base_holidays` VALUES (185, '2022-08-06', 1, '周六');
INSERT INTO `t_base_holidays` VALUES (186, '2022-08-07', 1, '周天');
INSERT INTO `t_base_holidays` VALUES (187, '2022-08-13', 1, '周六');
INSERT INTO `t_base_holidays` VALUES (188, '2022-08-14', 1, '周天');
INSERT INTO `t_base_holidays` VALUES (189, '2022-08-20', 1, '周六');
INSERT INTO `t_base_holidays` VALUES (190, '2022-08-21', 1, '周天');
INSERT INTO `t_base_holidays` VALUES (191, '2022-08-27', 1, '周六');
INSERT INTO `t_base_holidays` VALUES (192, '2022-08-28', 1, '周天');
INSERT INTO `t_base_holidays` VALUES (193, '2022-09-03', 1, '周六');
INSERT INTO `t_base_holidays` VALUES (194, '2022-09-04', 1, '周天');
INSERT INTO `t_base_holidays` VALUES (195, '2022-09-10', 1, '周六 中秋节');
INSERT INTO `t_base_holidays` VALUES (196, '2022-09-11', 1, '周天 中秋节');
INSERT INTO `t_base_holidays` VALUES (197, '2022-09-12', 1, '周一 中秋节');
INSERT INTO `t_base_holidays` VALUES (198, '2022-09-17', 1, '周六');
INSERT INTO `t_base_holidays` VALUES (199, '2022-09-18', 1, '周天');
INSERT INTO `t_base_holidays` VALUES (200, '2022-09-24', 1, '周六');
INSERT INTO `t_base_holidays` VALUES (201, '2022-09-25', 1, '周天');
INSERT INTO `t_base_holidays` VALUES (202, '2022-10-01', 1, '周六 国庆节');
INSERT INTO `t_base_holidays` VALUES (203, '2022-10-02', 1, '周天 国庆节');
INSERT INTO `t_base_holidays` VALUES (204, '2022-10-03', 1, '周一 国庆节');
INSERT INTO `t_base_holidays` VALUES (205, '2022-10-04', 1, '周二 国庆节');
INSERT INTO `t_base_holidays` VALUES (206, '2022-10-05', 1, '周三 国庆节');
INSERT INTO `t_base_holidays` VALUES (207, '2022-10-06', 1, '周四 国庆节');
INSERT INTO `t_base_holidays` VALUES (208, '2022-10-07', 1, '周五 国庆节');
INSERT INTO `t_base_holidays` VALUES (209, '2022-10-15', 1, '周六');
INSERT INTO `t_base_holidays` VALUES (210, '2022-10-16', 1, '周天');
INSERT INTO `t_base_holidays` VALUES (211, '2022-10-22', 1, '周六');
INSERT INTO `t_base_holidays` VALUES (212, '2022-10-23', 1, '周天');
INSERT INTO `t_base_holidays` VALUES (213, '2022-10-29', 1, '周六');
INSERT INTO `t_base_holidays` VALUES (214, '2022-10-30', 1, '周天');
INSERT INTO `t_base_holidays` VALUES (215, '2022-11-05', 1, '周六');
INSERT INTO `t_base_holidays` VALUES (216, '2022-11-06', 1, '周天');
INSERT INTO `t_base_holidays` VALUES (217, '2022-11-12', 1, '周六');
INSERT INTO `t_base_holidays` VALUES (218, '2022-11-13', 1, '周天');
INSERT INTO `t_base_holidays` VALUES (219, '2022-11-19', 1, '周六');
INSERT INTO `t_base_holidays` VALUES (220, '2022-11-20', 1, '周天');
INSERT INTO `t_base_holidays` VALUES (221, '2022-11-26', 1, '周六');
INSERT INTO `t_base_holidays` VALUES (222, '2022-11-27', 1, '周天');
INSERT INTO `t_base_holidays` VALUES (223, '2022-12-03', 1, '周六');
INSERT INTO `t_base_holidays` VALUES (224, '2022-12-04', 1, '周天');
INSERT INTO `t_base_holidays` VALUES (225, '2022-12-10', 1, '周六');
INSERT INTO `t_base_holidays` VALUES (226, '2022-12-11', 1, '周天');
INSERT INTO `t_base_holidays` VALUES (227, '2022-12-17', 1, '周六');
INSERT INTO `t_base_holidays` VALUES (228, '2022-12-18', 1, '周天');
INSERT INTO `t_base_holidays` VALUES (229, '2022-12-24', 1, '周六');
INSERT INTO `t_base_holidays` VALUES (230, '2022-12-25', 1, '周天');
INSERT INTO `t_base_holidays` VALUES (231, '2022-12-31', 1, '周六 元旦');
INSERT INTO `t_base_holidays` VALUES (232, '2023-01-01', 1, '周日 元旦');
INSERT INTO `t_base_holidays` VALUES (233, '2023-01-02', 1, '周一 元旦');
INSERT INTO `t_base_holidays` VALUES (234, '2023-01-07', 1, '周六');
INSERT INTO `t_base_holidays` VALUES (235, '2023-01-08', 1, '周日');
INSERT INTO `t_base_holidays` VALUES (236, '2023-01-14', 1, '周六');
INSERT INTO `t_base_holidays` VALUES (237, '2023-01-15', 1, '周日');
INSERT INTO `t_base_holidays` VALUES (238, '2023-01-21', 1, '周六 除夕');
INSERT INTO `t_base_holidays` VALUES (239, '2023-01-22', 1, '周日 春节');
INSERT INTO `t_base_holidays` VALUES (240, '2023-01-23', 1, '周一 春节');
INSERT INTO `t_base_holidays` VALUES (241, '2023-01-24', 1, '周二 春节');
INSERT INTO `t_base_holidays` VALUES (242, '2023-01-25', 1, '周三 春节');
INSERT INTO `t_base_holidays` VALUES (243, '2023-01-26', 1, '周四 春节');
INSERT INTO `t_base_holidays` VALUES (244, '2023-01-27', 1, '周五 春节');
INSERT INTO `t_base_holidays` VALUES (245, '2023-02-04', 1, '周六');
INSERT INTO `t_base_holidays` VALUES (246, '2023-02-05', 1, '周日');
INSERT INTO `t_base_holidays` VALUES (247, '2023-02-11', 1, '周六');
INSERT INTO `t_base_holidays` VALUES (248, '2023-02-12', 1, '周日');
INSERT INTO `t_base_holidays` VALUES (249, '2023-02-18', 1, '周六');
INSERT INTO `t_base_holidays` VALUES (250, '2023-02-19', 1, '周日');
INSERT INTO `t_base_holidays` VALUES (251, '2023-02-25', 1, '周六');
INSERT INTO `t_base_holidays` VALUES (252, '2023-02-26', 1, '周日');
INSERT INTO `t_base_holidays` VALUES (253, '2023-03-04', 1, '周六');
INSERT INTO `t_base_holidays` VALUES (254, '2023-03-05', 1, '周日');
INSERT INTO `t_base_holidays` VALUES (255, '2023-03-11', 1, '周六');
INSERT INTO `t_base_holidays` VALUES (256, '2023-03-12', 1, '周日');
INSERT INTO `t_base_holidays` VALUES (257, '2023-03-18', 1, '周六');
INSERT INTO `t_base_holidays` VALUES (258, '2023-03-19', 1, '周日');
INSERT INTO `t_base_holidays` VALUES (259, '2023-03-25', 1, '周六');
INSERT INTO `t_base_holidays` VALUES (260, '2023-03-26', 1, '周日');
INSERT INTO `t_base_holidays` VALUES (261, '2023-04-01', 1, '周六');
INSERT INTO `t_base_holidays` VALUES (262, '2023-04-02', 1, '周日');
INSERT INTO `t_base_holidays` VALUES (263, '2023-04-05', 1, '周三 清明节');
INSERT INTO `t_base_holidays` VALUES (264, '2023-04-08', 1, '周六');
INSERT INTO `t_base_holidays` VALUES (265, '2023-04-09', 1, '周日');
INSERT INTO `t_base_holidays` VALUES (266, '2023-04-15', 1, '周六');
INSERT INTO `t_base_holidays` VALUES (267, '2023-04-16', 1, '周日');
INSERT INTO `t_base_holidays` VALUES (268, '2023-04-22', 1, '周六');
INSERT INTO `t_base_holidays` VALUES (269, '2023-04-29', 1, '周六 劳动节');
INSERT INTO `t_base_holidays` VALUES (270, '2023-04-30', 1, '周日 劳动节');
INSERT INTO `t_base_holidays` VALUES (271, '2023-05-01', 1, '周一 劳动节');
INSERT INTO `t_base_holidays` VALUES (272, '2023-05-02', 1, '周二 劳动节');
INSERT INTO `t_base_holidays` VALUES (273, '2023-05-03', 1, '周三 劳动节');
INSERT INTO `t_base_holidays` VALUES (274, '2023-05-07', 1, '周日');
INSERT INTO `t_base_holidays` VALUES (275, '2023-05-13', 1, '周六');
INSERT INTO `t_base_holidays` VALUES (276, '2023-05-14', 1, '周日');
INSERT INTO `t_base_holidays` VALUES (277, '2023-05-20', 1, '周六');
INSERT INTO `t_base_holidays` VALUES (278, '2023-05-21', 1, '周日');
INSERT INTO `t_base_holidays` VALUES (279, '2023-05-27', 1, '周六');
INSERT INTO `t_base_holidays` VALUES (280, '2023-05-28', 1, '周日');
INSERT INTO `t_base_holidays` VALUES (281, '2023-06-03', 1, '周六');
INSERT INTO `t_base_holidays` VALUES (282, '2023-06-04', 1, '周日');
INSERT INTO `t_base_holidays` VALUES (283, '2023-06-10', 1, '周六');
INSERT INTO `t_base_holidays` VALUES (284, '2023-06-11', 1, '周日');
INSERT INTO `t_base_holidays` VALUES (285, '2023-06-17', 1, '周六');
INSERT INTO `t_base_holidays` VALUES (286, '2023-06-18', 1, '周日');
INSERT INTO `t_base_holidays` VALUES (287, '2023-06-22', 1, '周四 端午节');
INSERT INTO `t_base_holidays` VALUES (288, '2023-06-23', 1, '周五 端午节');
INSERT INTO `t_base_holidays` VALUES (289, '2023-06-24', 1, '周六 端午节');
INSERT INTO `t_base_holidays` VALUES (290, '2023-07-01', 1, '周六');
INSERT INTO `t_base_holidays` VALUES (291, '2023-07-02', 1, '周日');
INSERT INTO `t_base_holidays` VALUES (292, '2023-07-08', 1, '周六');
INSERT INTO `t_base_holidays` VALUES (293, '2023-07-09', 1, '周日');
INSERT INTO `t_base_holidays` VALUES (294, '2023-07-15', 1, '周六');
INSERT INTO `t_base_holidays` VALUES (295, '2023-07-16', 1, '周日');
INSERT INTO `t_base_holidays` VALUES (296, '2023-07-22', 1, '周六');
INSERT INTO `t_base_holidays` VALUES (297, '2023-07-23', 1, '周日');
INSERT INTO `t_base_holidays` VALUES (298, '2023-07-29', 1, '周六');
INSERT INTO `t_base_holidays` VALUES (299, '2023-07-30', 1, '周日');
INSERT INTO `t_base_holidays` VALUES (300, '2023-08-05', 1, '周六');
INSERT INTO `t_base_holidays` VALUES (301, '2023-08-06', 1, '周日');
INSERT INTO `t_base_holidays` VALUES (302, '2023-08-12', 1, '周六');
INSERT INTO `t_base_holidays` VALUES (303, '2023-08-13', 1, '周日');
INSERT INTO `t_base_holidays` VALUES (304, '2023-08-19', 1, '周六');
INSERT INTO `t_base_holidays` VALUES (305, '2023-08-20', 1, '周日');
INSERT INTO `t_base_holidays` VALUES (306, '2023-08-26', 1, '周六');
INSERT INTO `t_base_holidays` VALUES (307, '2023-08-27', 1, '周日');
INSERT INTO `t_base_holidays` VALUES (308, '2023-09-02', 1, '周六');
INSERT INTO `t_base_holidays` VALUES (309, '2023-09-03', 1, '周日');
INSERT INTO `t_base_holidays` VALUES (310, '2023-09-09', 1, '周六');
INSERT INTO `t_base_holidays` VALUES (311, '2023-09-10', 1, '周日');
INSERT INTO `t_base_holidays` VALUES (312, '2023-09-16', 1, '周六');
INSERT INTO `t_base_holidays` VALUES (313, '2023-09-17', 1, '周日');
INSERT INTO `t_base_holidays` VALUES (314, '2023-09-23', 1, '周六');
INSERT INTO `t_base_holidays` VALUES (315, '2023-09-24', 1, '周日');
INSERT INTO `t_base_holidays` VALUES (316, '2023-09-29', 1, '周五 中秋节');
INSERT INTO `t_base_holidays` VALUES (317, '2023-09-30', 1, '周六 中秋节');
INSERT INTO `t_base_holidays` VALUES (318, '2023-10-01', 1, '周日 国庆节');
INSERT INTO `t_base_holidays` VALUES (319, '2023-10-02', 1, '周一 国庆节');
INSERT INTO `t_base_holidays` VALUES (320, '2023-10-03', 1, '周二 国庆节');
INSERT INTO `t_base_holidays` VALUES (321, '2023-10-04', 1, '周三 国庆节');
INSERT INTO `t_base_holidays` VALUES (322, '2023-10-05', 1, '周四 国庆节');
INSERT INTO `t_base_holidays` VALUES (323, '2023-10-06', 1, '周五 国庆节');
INSERT INTO `t_base_holidays` VALUES (324, '2023-10-14', 1, '周六');
INSERT INTO `t_base_holidays` VALUES (325, '2023-10-15', 1, '周日');
INSERT INTO `t_base_holidays` VALUES (326, '2023-10-21', 1, '周六');
INSERT INTO `t_base_holidays` VALUES (327, '2023-10-22', 1, '周日');
INSERT INTO `t_base_holidays` VALUES (328, '2023-10-28', 1, '周六');
INSERT INTO `t_base_holidays` VALUES (329, '2023-10-29', 1, '周日');
INSERT INTO `t_base_holidays` VALUES (330, '2023-11-04', 1, '周六');
INSERT INTO `t_base_holidays` VALUES (331, '2023-11-05', 1, '周日');
INSERT INTO `t_base_holidays` VALUES (332, '2023-11-11', 1, '周六');
INSERT INTO `t_base_holidays` VALUES (333, '2023-11-12', 1, '周日');
INSERT INTO `t_base_holidays` VALUES (334, '2023-11-18', 1, '周六');
INSERT INTO `t_base_holidays` VALUES (335, '2023-11-19', 1, '周日');
INSERT INTO `t_base_holidays` VALUES (336, '2023-11-25', 1, '周六');
INSERT INTO `t_base_holidays` VALUES (337, '2023-11-26', 1, '周日');
INSERT INTO `t_base_holidays` VALUES (338, '2023-12-02', 1, '周六');
INSERT INTO `t_base_holidays` VALUES (339, '2023-12-03', 1, '周日');
INSERT INTO `t_base_holidays` VALUES (340, '2023-12-09', 1, '周六');
INSERT INTO `t_base_holidays` VALUES (341, '2023-12-10', 1, '周日');
INSERT INTO `t_base_holidays` VALUES (342, '2023-12-16', 1, '周六');
INSERT INTO `t_base_holidays` VALUES (343, '2023-12-17', 1, '周日');
INSERT INTO `t_base_holidays` VALUES (344, '2023-12-23', 1, '周六');
INSERT INTO `t_base_holidays` VALUES (345, '2023-12-24', 1, '周日');
INSERT INTO `t_base_holidays` VALUES (346, '2023-12-30', 1, '周六');
INSERT INTO `t_base_holidays` VALUES (347, '2023-12-31', 1, '周日');
INSERT INTO `t_base_holidays` VALUES (350, '2023-02-02', 1, '无');
INSERT INTO `t_base_holidays` VALUES (352, '2023-02-03', 1, '无');
INSERT INTO `t_base_holidays` VALUES (354, '2023-02-01', 1, '');
INSERT INTO `t_base_holidays` VALUES (356, '2023-02-15', 0, NULL);

-- ----------------------------
-- Table structure for tenant_capacity
-- ----------------------------
DROP TABLE IF EXISTS `tenant_capacity`;
CREATE TABLE `tenant_capacity`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `tenant_id` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT '' COMMENT 'Tenant ID',
  `quota` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '配额，0表示使用默认值',
  `usage` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '使用量',
  `max_size` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '单个配置大小上限，单位为字节，0表示使用默认值',
  `max_aggr_count` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '聚合子配置最大个数',
  `max_aggr_size` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '单个聚合数据的子配置大小上限，单位为字节，0表示使用默认值',
  `max_history_count` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '最大变更历史数量',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_tenant_id`(`tenant_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_bin COMMENT = '租户容量信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of tenant_capacity
-- ----------------------------

-- ----------------------------
-- Table structure for tenant_info
-- ----------------------------
DROP TABLE IF EXISTS `tenant_info`;
CREATE TABLE `tenant_info`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `kp` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT 'kp',
  `tenant_id` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT '' COMMENT 'tenant_id',
  `tenant_name` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT '' COMMENT 'tenant_name',
  `tenant_desc` varchar(256) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT 'tenant_desc',
  `create_source` varchar(32) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT 'create_source',
  `gmt_create` bigint NOT NULL COMMENT '创建时间',
  `gmt_modified` bigint NOT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_tenant_info_kptenantid`(`kp` ASC, `tenant_id` ASC) USING BTREE,
  INDEX `idx_tenant_id`(`tenant_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8 COLLATE = utf8_bin COMMENT = 'tenant_info' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of tenant_info
-- ----------------------------
INSERT INTO `tenant_info` VALUES (4, '1', 'local', 'local', '本地', 'nacos', 1731312921174, 1731312921174);
INSERT INTO `tenant_info` VALUES (5, '1', 'test', 'test', 'test', 'nacos', 1731574196358, 1731574196358);

-- ----------------------------
-- Table structure for user_info
-- ----------------------------
DROP TABLE IF EXISTS `user_info`;
CREATE TABLE `user_info`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `password` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of user_info
-- ----------------------------
INSERT INTO `user_info` VALUES (1, '刘书强', '12345621');
INSERT INTO `user_info` VALUES (2, 'SQ', '123');

-- ----------------------------
-- Table structure for users
-- ----------------------------
DROP TABLE IF EXISTS `users`;
CREATE TABLE `users`  (
  `username` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `password` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `enabled` tinyint(1) NOT NULL,
  PRIMARY KEY (`username`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of users
-- ----------------------------
INSERT INTO `users` VALUES ('nacos', '$2a$10$EuWPZHzz32dJN7jexM34MOeYirDdFAZm2kuWj7VEOJhhZkDrxfvUu', 1);

SET FOREIGN_KEY_CHECKS = 1;
