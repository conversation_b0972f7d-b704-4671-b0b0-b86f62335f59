ALTER TABLE `ry-custom`.`t_live_base_info`
    DROP COLUMN `contract_duration`,
    DROP COLUMN `payment_method`,
    DROP COLUMN `initial_payment_amount`;

CREATE TABLE `t_payment_record` (
                                    `id` varchar(64) NOT NULL COMMENT '主键ID',
                                    `contract_number` varchar(100) NOT NULL COMMENT '合同编号，管理表：t_contract_info',
                                    `elderly_id` varchar(64) NOT NULL COMMENT '老人ID',
                                    `elderly_name` varchar(50) DEFAULT NULL COMMENT '老人姓名',
                                    `elderly_phone` varchar(20) DEFAULT NULL COMMENT '老人电话',
                                    `contract_start_date` date DEFAULT NULL COMMENT '合同开始日期',
                                    `contract_end_date` date DEFAULT NULL COMMENT '合同结束日期',
                                    `contract_cycle` int DEFAULT NULL COMMENT '合同周期（月数）',
                                    `care_level` varchar(50) DEFAULT NULL COMMENT '护理级别',
                                    `bed_name` varchar(50) DEFAULT NULL COMMENT '房间号',
                                    `leave_duration` int DEFAULT NULL COMMENT '请假时长',
                                    `leave_dates` varchar(500) DEFAULT NULL COMMENT '请假日期，格式如：2024/11/11-2024/11/12,2024/12/11-025/01/12',
                                    `total_cost` decimal(12,2) DEFAULT NULL COMMENT '需缴费用',
                                    `paid_cost` decimal(12,2) DEFAULT NULL COMMENT '实缴费用',
                                    `payment_method` varchar(4) DEFAULT NULL COMMENT '缴费方式，字典：t_payment_record_payment_method',
                                    `account_add_cost` decimal(12,2) DEFAULT NULL COMMENT '本次账户增加金额',
                                    `payment_time` datetime DEFAULT NULL COMMENT '缴费、结算时间',
                                    `fee_type` varchar(2) DEFAULT NULL COMMENT '费用类型：字典：t_payment_record_fee_type',
                                    `remark` varchar(500) DEFAULT NULL COMMENT '备注',
                                    `discharge_date` date DEFAULT NULL COMMENT '离院日期（只有结算单有）',
                                    `details` json DEFAULT NULL COMMENT '费用详情（List<Detail> JSON）',
                                    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='缴费确认单';


ALTER TABLE `ry-custom`.`t_meal_sample_info`
    ADD COLUMN `img_urls` varchar(1000) NULL COMMENT '图片地址' AFTER `storage_location`;

ALTER TABLE `ry-custom`.`t_elderly_people_info`
    MODIFY COLUMN `status` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT '1' COMMENT '入住状态，字典：live_state；0：入住中，1：未入住，\r\n2：请假中，3：请假中' AFTER `social_security_no`;

ALTER TABLE `ry-custom`.`t_elderly_people_info`
    MODIFY COLUMN `id_card_num` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL COMMENT '身份证号' AFTER `sex`;
