CREATE TABLE `ry-gw`.`t_home_center_intro` (
                                       `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                       `title` VARCHAR(200) NOT NULL COMMENT '标题',
                                       `content` TEXT COMMENT '内容',
                                       `video_url` VARCHAR(500) DEFAULT NULL COMMENT '视频地址',
                                       PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='首页中心介绍表';

CREATE TABLE `ry-gw`.`t_home_elderly_service` (
                                          `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                          `remark` VARCHAR(500) DEFAULT NULL COMMENT '备注',
                                          `image_url` VARCHAR(500) DEFAULT NULL COMMENT '图片地址',
                                          PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='首页为老服务表';

INSERT INTO `ry-gw`.`t_home_elderly_service` (`id`, `remark`, `image_url`) VALUES (1, '医养结合', NULL);
INSERT INTO `ry-gw`.`t_home_elderly_service` (`id`, `remark`, `image_url`) VALUES (2, '科学膳食', NULL);
INSERT INTO `ry-gw`.`t_home_elderly_service` (`id`, `remark`, `image_url`) VALUES (3, '优雅环境', NULL);
INSERT INTO `ry-gw`.`t_home_elderly_service` (`id`, `remark`, `image_url`) VALUES (4, '品质享受', NULL);

CREATE TABLE `t_elderly_service` (
                                     `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                     `type` varchar(20) DEFAULT NULL COMMENT '类型',
                                     `content` text COMMENT '内容',
                                     `image_urls` varchar(1000) DEFAULT NULL COMMENT '图片地址，多个逗号隔开',
                                     PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='为老服务表';

INSERT INTO `ry-gw`.`t_elderly_service` (`id`, `type`, `content`, `image_urls`) VALUES (1, '医养结合', NULL, NULL);
INSERT INTO `ry-gw`.`t_elderly_service` (`id`, `type`, `content`, `image_urls`) VALUES (2, '科学膳食', NULL, NULL);
INSERT INTO `ry-gw`.`t_elderly_service` (`id`, `type`, `content`, `image_urls`) VALUES (3, '优雅环境', NULL, NULL);
INSERT INTO `ry-gw`.`t_elderly_service` (`id`, `type`, `content`, `image_urls`) VALUES (4, '品质享受', NULL, NULL);
INSERT INTO `ry-gw`.`t_elderly_service` (`id`, `type`, `content`, `image_urls`) VALUES (5, '综合评估', NULL, NULL);
INSERT INTO `ry-gw`.`t_elderly_service` (`id`, `type`, `content`, `image_urls`) VALUES (6, '健康管理', NULL, NULL);
INSERT INTO `ry-gw`.`t_elderly_service` (`id`, `type`, `content`, `image_urls`) VALUES (7, '配套医疗', NULL, NULL);
INSERT INTO `ry-gw`.`t_elderly_service` (`id`, `type`, `content`, `image_urls`) VALUES (8, '丰富娱乐', NULL, NULL);
INSERT INTO `ry-gw`.`t_elderly_service` (`id`, `type`, `content`, `image_urls`) VALUES (9, '适老环境', NULL, NULL);
INSERT INTO `ry-gw`.`t_elderly_service` (`id`, `type`, `content`, `image_urls`) VALUES (10, '营养膳食', NULL, NULL);


CREATE TABLE `ry-gw`.`t_medical_nursing_integration` (
                                                 `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                                 `type` VARCHAR(20) DEFAULT NULL COMMENT '类型',
                                                 `details` JSON DEFAULT NULL COMMENT '详细信息（JSON格式）',
                                                 PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='医养结合表';

INSERT INTO `ry-gw`.`t_medical_nursing_integration` (`id`, `type`, `details`) VALUES (1, '医院介绍', '[{\"title\": \"第二人民医院\", \"imgUrl\": \"\", \"content\": \"\"}, {\"title\": \"南院区\", \"imgUrl\": \"\", \"content\": \"\"}]');
INSERT INTO `ry-gw`.`t_medical_nursing_integration` (`id`, `type`, `details`) VALUES (2, '医养优势', '[{\"title\": \"就地快捷转诊\", \"content\": \"\"}, {\"title\": \"专业的医疗团队\", \"content\": \"\"}, {\"title\": \"个性化的护理服务\", \"content\": \"\"}, {\"title\": \"持续的管理和监控\", \"content\": \"\"}]');
INSERT INTO `ry-gw`.`t_medical_nursing_integration` (`id`, `type`, `details`) VALUES (3, '主要科室', '[{\"title\": \"中医特色科\", \"imgUrl\": \"\", \"content\": \"\"}, {\"title\": \"体检中心\", \"imgUrl\": \"\", \"content\": \"\"}, {\"title\": \"老年病科\", \"imgUrl\": \"\", \"content\": \"\"}]');
INSERT INTO `ry-gw`.`t_medical_nursing_integration` (`id`, `type`, `details`) VALUES (4, '特色服务', '[{\"title\": \"特色服务\", \"content\": \"\"}]');
INSERT INTO `ry-gw`.`t_medical_nursing_integration` (`id`, `type`, `details`) VALUES (5, '坐诊名医', '[]');
INSERT INTO `ry-gw`.`t_medical_nursing_integration` (`id`, `type`, `details`) VALUES (6, '坐诊时间', '[{\"title\": \"坐诊时间\", \"imgUrl\": \"\"}]');

ALTER TABLE `ry-gw`.`t_center_info`
    DROP COLUMN `icon_url`,
    DROP COLUMN `sort`,
    CHANGE COLUMN `title` `type` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '类型' AFTER `id`,
    CHANGE COLUMN `image_urls` `img_urls` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '图片地址，多个逗号隔开' AFTER `content`;
ALTER TABLE `ry-gw`.`t_center_info`
    MODIFY COLUMN `content` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '内容' AFTER `type`;
ALTER TABLE `ry-gw`.`t_center_info`
    CHANGE COLUMN `img_urls` `img_url` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '图片地址' AFTER `content`;

INSERT INTO `ry-gw`.`t_center_info` (`id`, `type`, `content`, `img_url`) VALUES (1, '机构介绍', NULL, NULL);
INSERT INTO `ry-gw`.`t_center_info` (`id`, `type`, `content`, `img_url`) VALUES (2, '护理团队', NULL, NULL);
INSERT INTO `ry-gw`.`t_center_info` (`id`, `type`, `content`, `img_url`) VALUES (3, '医疗团队', NULL, NULL);
INSERT INTO `ry-gw`.`t_center_info` (`id`, `type`, `content`, `img_url`) VALUES (4, '运营团队', NULL, NULL);
INSERT INTO `ry-gw`.`t_center_info` (`id`, `type`, `content`, `img_url`) VALUES (5, '后勤团队', NULL, NULL);

RENAME TABLE t_about_us TO t_about_us_recruit;

CREATE TABLE `t_about_us` (
                              `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                              `type` varchar(20) DEFAULT NULL COMMENT '类型',
                              `details` json DEFAULT NULL COMMENT '详细信息（JSON格式）',
                              PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='关于我们表';

INSERT INTO `ry-gw`.`t_about_us` (`id`, `type`, `details`) VALUES (1, '公司介绍', '[{\"title\": \"公司介绍\", \"content\": \"\"}]');
INSERT INTO `ry-gw`.`t_about_us` (`id`, `type`, `details`) VALUES (2, '组织架构', '[{\"title\": \"综合部\", \"content\": \"\"}, {\"title\": \"照护部\", \"content\": \"\"}, {\"title\": \"后勤部\", \"content\": \"\"}, {\"title\": \"运营部\", \"content\": \"\"}]');
INSERT INTO `ry-gw`.`t_about_us` (`id`, `type`, `details`) VALUES (3, '风采展示', '[{\"title\": \"市为老服务中心开展植树节公益活动\", \"content\": \"\"}, {\"title\": \"市为老服务中心举行兜底人员认亲活动\", \"content\": \"\"}, {\"title\": \"真情守护“夕阳红”，为老服务谱新篇\", \"content\": \"\"}]');
INSERT INTO `ry-gw`.`t_about_us` (`id`, `type`, `details`) VALUES (4, '加入我们', '[{\"title\": \"加入我们\", \"content\": \"\"}]');






