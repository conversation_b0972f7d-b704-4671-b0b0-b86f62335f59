UPDATE `ry-cloud`.`sys_menu` SET `menu_name` = '合同管理', `parent_id` = 2012, `order_num` = 10, `path` = 'contractManagementList', `component` = 'marketingManagement/contractManagement/list', `query` = NULL, `is_frame` = 1, `is_cache` = 0, `menu_type` = 'C', `visible` = '0', `status` = '0', `perms` = '', `icon` = 'documentation', `applet_icon` = NULL, `create_by` = 'admin', `create_time` = '2024-11-13 16:52:31', `update_by` = 'admin', `update_time` = '2025-04-18 09:07:43', `remark` = '', `platform` = '100', `applet_path` = NULL WHERE `menu_id` = 3851;
UPDATE `ry-cloud`.`sys_menu` SET `menu_name` = '费用缴纳', `parent_id` = 2012, `order_num` = 6, `path` = 'paymentOfFee', `component` = 'costManage/paymentOfFee/index', `query` = NULL, `is_frame` = 1, `is_cache` = 0, `menu_type` = 'C', `visible` = '0', `status` = '0', `perms` = '', `icon` = 'textarea', `applet_icon` = NULL, `create_by` = 'admin', `create_time` = '2025-04-13 11:30:08', `update_by` = 'admin', `update_time` = '2025-04-21 16:41:42', `remark` = '', `platform` = '100', `applet_path` = NULL WHERE `menu_id` = 3952;
UPDATE `ry-cloud`.`sys_role` SET `role_name` = '超级管理员', `role_key` = 'admin', `role_sort` = 1, `data_scope` = '1', `menu_check_strictly` = 1, `dept_check_strictly` = 1, `status` = '0', `del_flag` = '0', `create_by` = 'admin', `create_time` = '2022-03-16 10:14:02', `update_by` = '', `update_time` = NULL, `remark` = '超级管理员', `platforms` = '1,2,3,4,5,6,7,50,100,101,104,105,106' WHERE `role_id` = 1;

INSERT INTO `ry-cloud`.`sys_dict_type` (`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('机构养老-缴费方式', 't_payment_record_payment_method', '0', 'admin', '2025-04-21 19:43:34', '', NULL, NULL);
INSERT INTO `ry-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2, '支付宝', '3', 't_payment_record_payment_method', NULL, 'default', 'N', '0', 'admin', '2025-04-21 19:44:14', '', NULL, NULL);
INSERT INTO `ry-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1, '微信', '2', 't_payment_record_payment_method', NULL, 'default', 'N', '0', 'admin', '2025-04-21 19:44:04', '', NULL, NULL);
INSERT INTO `ry-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '现金', '1', 't_payment_record_payment_method', NULL, 'default', 'N', '0', 'admin', '2025-04-21 19:43:58', '', NULL, NULL);

INSERT INTO `ry-cloud`.`sys_dict_type` (`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('机构养老-费用类型', 't_payment_record_fee_type', '0', 'admin', '2025-04-22 08:53:01', '', NULL, NULL);
INSERT INTO `ry-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1, '结算', '2', 't_payment_record_fee_type', NULL, 'default', 'N', '0', 'admin', '2025-04-22 08:53:45', '', NULL, NULL);
INSERT INTO `ry-cloud`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (0, '续费', '1', 't_payment_record_fee_type', NULL, 'default', 'N', '0', 'admin', '2025-04-22 08:53:38', '', NULL, NULL);

INSERT INTO `ry-cloud`.`sys_role` (`role_name`, `role_key`, `role_sort`, `data_scope`, `menu_check_strictly`, `dept_check_strictly`, `status`, `del_flag`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `platforms`) VALUES ('普通运营人员', 'regular_operator', 10, '1', 1, 1, '0', '0', 'admin', '2025-04-18 16:53:10', 'admin', '2025-04-21 15:16:23', NULL, NULL);
INSERT INTO `ry-cloud`.`sys_role` (`role_name`, `role_key`, `role_sort`, `data_scope`, `menu_check_strictly`, `dept_check_strictly`, `status`, `del_flag`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `platforms`) VALUES ('App-运营管理端', 'app_yl_admin', 10, '1', 1, 1, '0', '0', 'admin', '2025-04-19 10:27:29', 'admin', '2025-04-19 10:31:28', NULL, NULL);
