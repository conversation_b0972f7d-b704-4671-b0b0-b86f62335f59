---
inclusion: always
---

# 项目整体代码风格和开发规范

## 项目概述

本项目基于若依(RuoYi)微服务架构，采用Spring Cloud + Spring Boot技术栈，主要包含以下模块：
- `ruoyi-gateway`: 网关服务
- `ruoyi-auth`: 认证服务
- `ruoyi-system`: 系统管理服务
- `ruoyi-custom`: 机构养老业务服务（核心业务模块）
- `ruoyi-modules`: 其他业务模块
  - `ruoyi-file`: 文件服务模块
  - `ruoyi-gen`: 代码生成模块
  - `ruoyi-job`: 定时任务模块
  - `ruoyi-gw`: 政务服务模块
  - `ruoyi-assets`: 资产管理模块
  - `ruoyi-daycare`: 日间照料模块
  - `ruoyi-homecare`: 居家养老模块
  - `ruoyi-realty`: 房地产模块
  - `ruoyi-security`: 安防模块

## 技术栈

### 后端技术
- **基础框架**: Spring Boot 2.7.x
- **微服务**: Spring Cloud 2021.x
- **安全框架**: Spring Security + JWT
- **持久层**: MyBatis + MyBatis-Plus
- **数据库**: MySQL 8.0
- **缓存**: Redis 6.x
- **消息队列**: RabbitMQ
- **注册中心**: Nacos
- **配置中心**: Nacos Config
- **网关**: Spring Cloud Gateway
- **监控**: Spring Boot Actuator + Micrometer
- **文档**: Swagger 3.x

### 开发工具
- **构建工具**: Maven 3.6+
- **JDK版本**: OpenJDK 8+
- **IDE推荐**: IntelliJ IDEA
- **版本控制**: Git
- **容器化**: Docker + Docker Compose

## 整体架构规范

### 微服务架构原则
- 单一职责：每个服务专注于特定业务领域
- 服务自治：服务间通过API通信，避免直接数据库访问
- 去中心化：避免单点故障，提高系统可用性
- 容错设计：实现熔断、降级、重试机制

### 模块依赖关系
```
ruoyi-gateway (网关层)
    ↓
ruoyi-auth (认证层)
    ↓
ruoyi-modules (业务层)
    ↓
ruoyi-common (公共层)
    ↓
ruoyi-api (接口层)
```

## 代码组织规范

### 项目结构标准
```
项目根目录/
├── bin/                       # 启动脚本目录
├── docker/                    # Docker容器化配置
├── logs/                      # 运行日志目录
├── nacos-config/              # Nacos配置中心文件
├── other/                     # 其他文档和资源
├── ruoyi-api/                 # 服务间调用接口模块
│   └── ruoyi-api-system       # 系统服务API
├── ruoyi-auth/                # 认证授权服务
├── ruoyi-common/              # 公共模块
│   ├── ruoyi-common-core      # 核心公共模块
│   ├── ruoyi-common-datascope # 数据权限模块
│   ├── ruoyi-common-datasource# 数据源配置模块
│   ├── ruoyi-common-log       # 日志处理模块
│   ├── ruoyi-common-redis     # Redis缓存模块
│   ├── ruoyi-common-security  # 安全认证模块
│   └── ruoyi-common-swagger   # Swagger文档模块
├── ruoyi-gateway/             # 网关服务
├── ruoyi-modules/             # 业务模块集合
│   ├── ruoyi-system           # 系统管理模块
│   ├── ruoyi-custom           # 机构养老业务模块
│   ├── ruoyi-file             # 文件服务模块
│   ├── ruoyi-gen              # 代码生成模块
│   ├── ruoyi-job              # 定时任务模块
│   ├── ruoyi-gw               # 政务服务模块
│   ├── ruoyi-assets           # 资产管理模块
│   ├── ruoyi-daycare          # 日间照料模块
│   ├── ruoyi-homecare         # 居家养老模块
│   ├── ruoyi-realty           # 房地产模块
│   └── ruoyi-security         # 安防模块
├── ruoyi-visual/              # 可视化监控模块
│   └── ruoyi-monitor          # 服务监控
├── sql/                       # 数据库脚本
├── pom.xml                    # 根级Maven配置
└── README.md                  # 项目说明文档
```

### 包命名规范
- 根包：`com.ruoyi`
- 公共包：`com.ruoyi.common`
- 业务包：`com.ruoyi.{模块名}`
- API包：`com.ruoyi.{模块名}.api`

### 类文件组织
每个业务模块内部结构：
```
com.ruoyi.{模块名}/
├── controller/          # 控制器层
├── domain/             # 实体类
├── mapper/             # 数据访问层
├── service/            # 服务接口
│   └── impl/           # 服务实现
├── config/             # 配置类
├── utils/              # 工具类
└── {ModuleName}Application.java  # 启动类
```

## 编码规范

### Java编码标准

**命名规范**
- 类名：大驼峰命名法 `UserController`
- 方法名：小驼峰命名法 `getUserInfo`
- 变量名：小驼峰命名法 `userName`
- 常量名：全大写下划线分隔 `MAX_RETRY_COUNT`
- 包名：全小写点分隔 `com.ruoyi.system`

**代码格式**
- 缩进：4个空格，不使用Tab
- 行长度：不超过120字符
- 大括号：K&R风格，左大括号不换行
- 空行：方法间空一行，逻辑块间空一行

**注释规范**
```java
/**
 * 类功能描述
 *
 * <AUTHOR>
 * @date 2025-01-17
 */
public class UserService {
    
    /**
     * 方法功能描述
     *
     * @param userId 用户ID
     * @return 用户信息
     */
    public User getUserById(Long userId) {
        // 单行注释说明关键逻辑
        return userMapper.selectById(userId);
    }
}
```

### 数据库设计规范

**表设计原则**
- 表名：`t_` 前缀 + 下划线命名 `t_user_info`
- 字段名：下划线命名 `user_name`
- 主键：统一使用 `id`，类型为 `bigint`
- 外键：`{关联表}_id` 格式
- 时间字段：`create_time`, `update_time`
- 操作人字段：`create_by`, `update_by`
- 逻辑删除：`del_flag` (0:正常 1:删除)

**索引规范**
- 主键索引：每表必有
- 唯一索引：业务唯一字段
- 普通索引：查询频繁字段
- 复合索引：多字段组合查询

**字段规范**
- 必须字段：`NOT NULL`
- 字符串长度：根据业务需求合理设置
- 数值类型：选择合适精度
- 时间类型：使用 `datetime`
- 枚举值：使用字典表管理

## API设计规范

### RESTful API标准

**URL设计**
- 使用名词，避免动词：`/users` 而非 `/getUsers`
- 层级关系：`/users/{id}/orders`
- 版本控制：`/api/v1/users`
- 参数传递：查询参数用于过滤和分页

**HTTP方法**
- GET：查询资源
- POST：创建资源
- PUT：更新资源（全量）
- PATCH：更新资源（部分）
- DELETE：删除资源

**响应格式**
```json
{
    "code": 200,
    "msg": "操作成功",
    "data": {
        // 业务数据
    }
}
```

**分页响应**
```json
{
    "code": 200,
    "msg": "查询成功",
    "rows": [
        // 数据列表
    ],
    "total": 100
}
```

### 接口文档规范

**Swagger注解**
```java
@Api(tags = "用户管理")
@RestController
@RequestMapping("/system/user")
public class UserController {
    
    @ApiOperation("获取用户列表")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "userName", value = "用户名", paramType = "query"),
        @ApiImplicitParam(name = "status", value = "状态", paramType = "query")
    })
    @GetMapping("/list")
    public TableDataInfo list(SysUser user) {
        // 实现逻辑
    }
}
```

## 异常处理规范

### 异常分类
- 业务异常：`ServiceException`
- 系统异常：`RuntimeException`
- 参数异常：`IllegalArgumentException`
- 权限异常：`AccessDeniedException`

### 异常处理模式
```java
@Service
public class UserServiceImpl implements UserService {
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertUser(SysUser user) {
        // 参数校验
        if (StringUtils.isEmpty(user.getUserName())) {
            throw new ServiceException("用户名不能为空");
        }
        
        // 业务校验
        if (userMapper.checkUserNameUnique(user.getUserName()) > 0) {
            throw new ServiceException("用户名已存在");
        }
        
        return userMapper.insertUser(user);
    }
}
```

### 全局异常处理
```java
@RestControllerAdvice
public class GlobalExceptionHandler {
    
    @ExceptionHandler(ServiceException.class)
    public AjaxResult handleServiceException(ServiceException e) {
        return AjaxResult.error(e.getMessage());
    }
    
    @ExceptionHandler(Exception.class)
    public AjaxResult handleException(Exception e) {
        log.error("系统异常", e);
        return AjaxResult.error("系统繁忙，请稍后再试");
    }
}
```

## 配置管理规范

### 配置文件层级
1. `bootstrap.yml` - 引导配置（Nacos连接）
2. `application.yml` - 应用配置（本地开发）
3. `application-{profile}.yml` - 环境配置
4. Nacos配置中心 - 动态配置

### 配置命名规范
```yaml
# 应用配置
spring:
  application:
    name: ruoyi-system
  
# 数据源配置
spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ************************************
    username: ${DB_USERNAME:root}
    password: ${DB_PASSWORD:password}

# 业务配置
ruoyi:
  name: RuoYi-Cloud
  version: 3.6.0
  copyrightYear: 2025
```

## 日志规范

### 日志级别
- ERROR：系统错误，需要立即处理
- WARN：警告信息，需要关注
- INFO：重要业务信息
- DEBUG：调试信息，生产环境关闭

### 日志格式
```java
@Slf4j
@Service
public class UserServiceImpl {
    
    public User getUserById(Long userId) {
        log.info("查询用户信息，用户ID：{}", userId);
        
        try {
            User user = userMapper.selectById(userId);
            log.debug("查询结果：{}", user);
            return user;
        } catch (Exception e) {
            log.error("查询用户失败，用户ID：{}，错误信息：{}", userId, e.getMessage(), e);
            throw new ServiceException("查询用户失败");
        }
    }
}
```

### 操作日志
```java
@Log(title = "用户管理", businessType = BusinessType.INSERT)
@PostMapping
public AjaxResult add(@RequestBody SysUser user) {
    return toAjax(userService.insertUser(user));
}
```

## 安全规范

### 权限控制
```java
// 方法级权限控制（注释形式，不实际启用）
// @RequiresPermissions("system:user:add")
@PostMapping
public AjaxResult add(@RequestBody SysUser user) {
    return toAjax(userService.insertUser(user));
}
```

### 数据校验
```java
@Data
public class SysUser extends BaseEntity {
    
    @NotBlank(message = "用户名不能为空")
    @Size(min = 2, max = 20, message = "用户名长度必须在2-20个字符之间")
    private String userName;
    
    @Email(message = "邮箱格式不正确")
    private String email;
}
```

### SQL注入防护
```xml
<!-- 使用参数化查询 -->
<select id="selectUserList" resultMap="SysUserResult">
    SELECT * FROM sys_user 
    WHERE del_flag = '0'
    <if test="userName != null and userName != ''">
        AND user_name LIKE CONCAT('%', #{userName}, '%')
    </if>
</select>
```

## 性能优化规范

### 数据库优化
- 合理使用索引
- 避免N+1查询
- 使用分页查询
- 读写分离
- 连接池配置

### 缓存策略
```java
@Service
public class UserServiceImpl {
    
    @Cacheable(value = "user", key = "#userId")
    public User getUserById(Long userId) {
        return userMapper.selectById(userId);
    }
    
    @CacheEvict(value = "user", key = "#user.userId")
    public int updateUser(SysUser user) {
        return userMapper.updateUser(user);
    }
}
```

### 异步处理
```java
@Async("taskExecutor")
public void sendNotification(String message) {
    // 异步发送通知
    notificationService.send(message);
}
```

## 测试规范

### 单元测试
```java
@SpringBootTest
class UserServiceTest {
    
    @Autowired
    private UserService userService;
    
    @Test
    void testGetUserById() {
        // Given
        Long userId = 1L;
        
        // When
        User user = userService.getUserById(userId);
        
        // Then
        assertThat(user).isNotNull();
        assertThat(user.getUserId()).isEqualTo(userId);
    }
}
```

### 集成测试
```java
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureTestDatabase
class UserControllerTest {
    
    @Autowired
    private TestRestTemplate restTemplate;
    
    @Test
    void testGetUserList() {
        ResponseEntity<String> response = restTemplate.getForEntity("/system/user/list", String.class);
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
    }
}
```

## 部署规范

### Docker配置
```dockerfile
FROM openjdk:8-jre-slim

VOLUME /tmp
COPY target/*.jar app.jar
EXPOSE 8080

ENTRYPOINT ["java", "-jar", "/app.jar"]
```

### 环境配置
- 开发环境：dev
- 测试环境：test  
- 预生产环境：pre
- 生产环境：prod

### 监控配置
- 应用监控：Spring Boot Actuator
- 链路追踪：SkyWalking
- 日志收集：ELK Stack
- 指标监控：Prometheus + Grafana

## 代码质量

### 代码审查
- 功能正确性
- 代码规范性
- 性能合理性
- 安全性检查
- 可维护性

### 静态代码分析
- SonarQube质量检查
- FindBugs缺陷检测
- PMD代码规范检查
- Checkstyle格式检查

### 版本控制

**Git Flow工作流**
- `master`: 生产环境分支
- `develop`: 开发环境分支
- `feature/*`: 功能开发分支
- `hotfix/*`: 紧急修复分支
- `release/*`: 发布准备分支

**提交信息规范**
```
<type>(<scope>): <subject>

<body>

<footer>
```

提交类型：
- `feat`: 新功能
- `fix`: 修复bug
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

示例：
```
feat(custom): 添加长者入住关怀功能

- 新增长者入住关怀列表导出功能
- 更新老人基础信息列表接口，增加新字段和优化现有字段
- 添加添加长者入住关怀的功能

Closes #123
```

**分支命名规范**
- 功能分支: `feature/功能描述`
- 修复分支: `hotfix/问题描述`
- 发布分支: `release/版本号`

## 开发流程规范

### 1. 需求分析阶段
- 明确业务需求和技术需求
- 确定接口设计和数据库设计
- 评估开发工作量和时间

### 2. 设计阶段
- 数据库表结构设计
- API接口设计
- 前端页面原型设计
- 技术方案确定

### 3. 开发阶段
- 创建功能分支
- 按照代码规范进行开发
- 编写单元测试
- 自测功能完整性

### 4. 测试阶段
- 提交代码审查
- 部署测试环境
- 功能测试和集成测试
- 性能测试（如需要）

### 5. 发布阶段
- 合并到主分支
- 部署生产环境
- 监控系统运行状态
- 记录发布日志

## 代码审查规范

### 审查要点
1. **功能正确性**
   - 是否实现了需求功能
   - 边界条件处理是否正确
   - 异常情况处理是否完善

2. **代码质量**
   - 是否遵循编码规范
   - 代码结构是否清晰
   - 是否有重复代码

3. **性能考虑**
   - 数据库查询是否优化
   - 是否存在性能瓶颈
   - 内存使用是否合理

4. **安全性**
   - 输入参数是否校验
   - SQL注入防护
   - 权限控制是否正确

### 审查流程
1. 开发者提交Pull Request
2. 指定审查人员进行代码审查
3. 审查人员提出修改意见
4. 开发者根据意见修改代码
5. 审查通过后合并代码

## 环境管理规范

### 环境分类
- **开发环境(dev)**: 开发人员本地开发使用
- **测试环境(test)**: 功能测试和集成测试使用
- **预生产环境(pre)**: 生产环境的镜像，用于最终验证
- **生产环境(prod)**: 正式对外提供服务的环境

### 配置管理
- 使用Nacos配置中心统一管理配置
- 敏感信息使用加密存储
- 不同环境使用不同的配置文件
- 配置变更需要审批流程

### 数据库管理
- 开发环境使用测试数据
- 测试环境定期同步生产数据（脱敏）
- 生产环境数据定期备份
- 数据库变更使用版本化SQL脚本

## 监控和运维规范

### 应用监控
- 使用Spring Boot Actuator暴露监控端点
- 集成Micrometer收集应用指标
- 配置健康检查接口

### 日志管理
- 统一日志格式和级别
- 使用ELK Stack收集和分析日志
- 重要操作记录审计日志
- 错误日志及时告警

### 性能监控
- 监控应用响应时间
- 监控数据库连接池状态
- 监控JVM内存使用情况
- 监控接口调用频率和成功率

### 告警机制
- 系统异常自动告警
- 性能指标超阈值告警
- 业务指标异常告警
- 告警信息及时通知相关人员

## 文档规范

### API文档
- 使用Swagger生成API文档
- 接口描述要清晰明确
- 参数和返回值要详细说明
- 提供接口调用示例

### 数据库文档
- 表结构设计文档
- 字段含义和约束说明
- 索引设计说明
- 数据字典维护

### 部署文档
- 环境搭建指南
- 部署步骤说明
- 配置文件说明
- 常见问题解决方案

### 开发文档
- 项目架构说明
- 开发环境搭建
- 编码规范说明
- 常用工具和框架使用指南